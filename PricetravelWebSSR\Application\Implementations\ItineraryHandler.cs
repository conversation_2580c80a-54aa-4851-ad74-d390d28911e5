﻿using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;

namespace PricetravelWebSSR.Application.Implementations
{
    public class ItineraryHandler : IItineraryHandler
    {
        private readonly IQueryHandlerAsync<ItineraryRequest, ItineraryResponse> _itinerary;
        public ItineraryHandler(IQueryHandlerAsync<ItineraryRequest, ItineraryResponse> itinerary)
        {
            _itinerary = itinerary;
        }

        public async Task<ItineraryResponse> QueryAsync(ItineraryRequest request, CancellationToken ct)
        {
            var response = await _itinerary.QueryAsync(request, ct);

            return response;
        }
    }
}

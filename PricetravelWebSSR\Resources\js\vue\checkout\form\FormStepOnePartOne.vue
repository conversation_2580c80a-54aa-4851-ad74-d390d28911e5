<template id="child">
    <div class="form-details">
        <fieldset>
            <div class="form-content">
                <div class="form-group">


                    <ValidationProvider rules="required|alpha_spaces|min:2" v-slot="{ errors, flags }"
                        ref="first_name">
                        <label for="first_name" class="form__label">{{ __('messages.first_name') }}</label>
                        <input type="text" id="first_name" name="first_name" class="form-control form__input"
                            oninput="this.value = this.value.replace(/[0-9]/g, '')"
                            :class="{
                                'is-invalid': showErrors.first_name && errors && errors.length,
                                'is-valid': flags.valid && !errors.length
                            }"
                            v-model="user.first_name" :placeholder="__('messages.placeholder_name')"
                            @input="handleFieldInput('first_name')"
                            @focus="handleFieldFocus('first_name')"
                            @blur="handleFieldBlur('first_name')">
                        <span v-if="showErrors.first_name && errors[0]" class="invalid-feedback">{{
                            __(`customErrorMessages.first_name.${errors[0]}`) }}</span>
                    </ValidationProvider>

                </div>


                <div class="form-group">
                    <ValidationProvider rules="required|alpha_spaces|min:2" v-slot="{ errors, flags }"
                        ref="last_name">
                        <label for="last_name" class="form__label">{{ __('messages.last_name') }}</label>
                        <input type="text" id="last_name" name="last_name" class="form-control form__input"
                            oninput="this.value = this.value.replace(/[0-9]/g, '')"
                            :class="{
                                'is-invalid': showErrors.last_name && errors && errors.length,
                                'is-valid': flags.valid && !errors.length
                            }"
                            v-model="user.last_name" :placeholder="__('messages.placeholder_lastname')"
                            @input="handleFieldInput('last_name')"
                            @focus="handleFieldFocus('last_name')"
                            @blur="handleFieldBlur('last_name')">
                        <span v-if="showErrors.last_name && errors[0]" class="invalid-feedback">{{
                            __(`customErrorMessages.last_name.${errors[0]}`) }}</span>
                    </ValidationProvider>
                </div>
            </div>
     
            <div class="form-content">
                <div class="form-group-1 position-relative">
                    <ValidationProvider rules="required|email" v-slot="{ errors, flags }"
                        vid="confirm" ref="email">
                        <label for="email" class="form__label">{{ __('messages.email') }}</label>
                        <input type="text" id="email" name="email" class="form-control form__input"
                            :class="{
                                'is-invalid': showErrors.email && errors && errors.length,
                                'is-valid': flags.valid && !errors.length
                            }"
                            v-model="user.email" :placeholder="__('messages.placeholder_email')"
                            @blur="handleEmailFieldBlur('email')" autocomplete="off"
                            autocorrect="off" autocapitalize="off" spellcheck="false"
                            @input="handleEmailFieldInput('email')"
                            @focus="handleEmailFieldFocus('email')"
                            @keydown.down.prevent="navigateSuggestions('email', 1)"
                            @keydown.up.prevent="navigateSuggestions('email', -1)"
                            @keydown.enter.prevent="selectCurrentSuggestion('email')">
                        <!-- Lista de sugerencias de email -->
                        <div class="email-suggestions"
                            v-if="showSuggestionsState.email && filteredSuggestions.email.length > 0 && user.email.length > 0"
                            ref="emailSuggestionsContainer">
                            <ul class="list-group">
                                <li class="list-group-item" 
                                    v-for="(suggestion, index) in filteredSuggestions.email"
                                    :key="index" 
                                    @click="selectSuggestion('email', suggestion)" 
                                    @mousedown.prevent
                                    :class="{ 'active': index === currentSuggestionIndex.email }">
                                    <span class="email-content">
                                        <span class="email-prefix">{{ user.email.split('@')[0] }}</span>
                                        <span class="email-domain">@{{ suggestion }}</span>
                                    </span>
                                </li>
                            </ul>
                        </div>
                        <span v-if="showErrors.email && errors[0]" class="invalid-feedback">{{ __(`customErrorMessages.email.${errors[0]}`) }}</span>
                    </ValidationProvider>
                </div>
            </div>
            <div class="form-content">
                <div class="form-group">
                    <label for="phone" id="tel-label" class="form__label phone__label">{{ __('messages.phone')
                        }}</label>
                    <ValidationProvider rules="required|integer" v-slot="{ errors, flags }"
                        ref="phone">
                        <input type="tel" class="form-control form__input" id="phone"
                            :placeholder="__('messages.placeholder_phone')"
                            :class="{
                                'is-invalid': showErrors.phone && errors && errors.length,
                                'is-valid': flags.valid && !errors.length
                            }"
                            v-model.number="user.phone"
                            @input="handleFieldInput('phone')"
                            @focus="handleFieldFocus('phone')"
                            @blur="handleFieldBlur('phone')"
                            oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\.*?)\..*/g, '$1');">
                        <span v-if="showErrors.phone && errors[0]" class="text-danger mt-1" style="font-size: 80%;"> {{
                            __(`customErrorMessages.phone.${errors[0]}`) }} </span>
                    </ValidationProvider>
                </div>
                <div class="form-group"></div>
            </div>
            <div v-if="provider === 1 && totalRooms == 1">
                <p><button type="button" data-toggle="collapse" data-target="#collapseExample" aria-expanded="false"
                        @click="toggleSpecialRequests" aria-controls="collapseExample"
                        class="btn btn-light btn-block border-0 text-dark text-left bg-white px-0 w-100">
                        <div class="d-flex  align-items-center">
                            <span class="special-requests-text">{{ __('messages.special_requests')
                            }}</span>
                            <i class="font-20 ml-1"
                                :class="[isSpecialRequestsOpen ? 'icons-chevron-up' : 'icons-chevron-down', 'arrow-icon']"></i>
                        </div>
                    </button></p>
                <div id="collapseExample" class="collapse">
                    <div class="card card-body border-0 p-0">
                        <div>
                            <label for="message" class="mb-3 font-400">{{ __('messages.we_send_your_requests')
                            }}</label>
                            <textarea class="form-control" id="special_request" rows="3" v-model="user.special_request"
                                @keyup="onChangeSpecialRequest($event)" :maxlength="limitTextArea"
                                :placeholder="__('messages.special_request_placeh')"></textarea>
                            <small class="float-right">{{ user.special_request.length }} / {{ limitTextArea }}</small>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>

    </div>
</template>

<script>

import intlTelInput from 'intl-tel-input';
import { ValidationProvider } from 'vee-validate';
import { HotelAnalytic } from '../../analytics/HotelsAnalytics'
const site = window.__pt.settings.site;
const culture = window.__pt.cultureData;
const userLocation = window.__pt.userLocation;
export default {
    components: {
        ValidationProvider
    },
    data() {
        return {
            limitTextArea: 200,
            showSpecialRequest: culture.InternalCultureCode != "es-mx",
            isTextAreaOpen: false,
            formFields: ['first_name', 'last_name', 'email', 'phone'],
            isSpecialRequestsOpen: false,
            // Control de foco para no mostrar errores mientras está en foco
            fieldInFocus: {
                first_name: false,
                last_name: false,
                email: false,
                phone: false
            },
            // Control para mostrar errores después del blur
            showErrors: {
                first_name: false,
                last_name: false,
                email: false,
                phone: false
            },

            emailDomains: ['gmail.com', 'hotmail.com', 'outlook.com', 'yahoo.com'],
            filteredSuggestions: {
                email: [],
            },
            showSuggestionsState: {
                email: false,
            },
            currentSuggestionIndex: {
                email: -1,
            },
            emailTouched: false,
            shouldTruncate: false
        }
    },
    props: ['user', 'provider', 'submitEvent', 'totalRooms'],
    async mounted() {
        this.onInit();
        window.addEventListener('resize', this.handleResize);
        this.$nextTick(() => {
            this.checkTextOverflow();
        });

        // Validar campos pre-cargados después de que el componente esté montado
        setTimeout(async () => {
            for (const field of this.formFields) {
                if (this.user[field] && this.$refs[field]) {
                    await this.$refs[field].validate({ silent: false });
                }
            }
        }, 200);

        $('[data-toggle="collapse"]').on('click', function () {
            if ($(this).attr('aria-expanded') == 'true') {
                $(this).find('i').addClass('icons-chevron-down').removeClass('icons-chevron-up');
            } else {
                $(this).find('i').addClass('icons-chevron-up').removeClass('icons-chevron-down');
            }
        });
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.handleResize);
    },
    watch: {
        submitEvent: {
            deep: false,
            handler() {
                setTimeout(async () => {
                    let firstErrorField = null;

                    for (const fieldName of this.formFields) {
                        this.showErrors[fieldName] = true;
                        const provider = this.$refs[fieldName];

                        if (provider) {
                            const { valid } = await provider.validate();
                            if (!valid && !firstErrorField) {
                                firstErrorField = fieldName;
                            }
                        }
                    }
                    if (firstErrorField) {
                        this.scrollToError(firstErrorField);
                    }
                });
            },
        },

        user: {
            deep: true,
            immediate: true,
            handler(newUser) {
                if (newUser) {
                    // Agregar delay para asegurar que los refs estén disponibles
                    setTimeout(() => {
                        this.$nextTick(async () => {
                            // Validar como submit pero solo los campos que tienen datos
                            for (const field of this.formFields) {
                                if (this.user[field] && this.$refs[field]) {
                                    // Validar con silent: false para activar flags.valid
                                    await this.$refs[field].validate({ silent: false });
                                }
                            }
                        });
                    }, 100);
                }
            }
        }

    },
    methods: {
        onInit() {


            
            const input = document.querySelector("#phone");
            let iti = intlTelInput(input, {
                initialCountry: userLocation.country,
                preferredCountries: site.preferredCountries
            });

            input.addEventListener("countrychange", () => {
                const selected = iti.getSelectedCountryData();
                if (selected) {
                    this.user.dial_code = selected.dialCode;
                }
            });

            var countryData = iti.getSelectedCountryData();

            if (countryData) {
                this.user.dial_code = countryData.dialCode;
            }
        },
        async validateField(field) {
            const provider = this.$refs[field];

            if (!provider) {
                console.error(`ValidationProvider ref '${field}' not found`);
                return;
            }

            const { valid, errors } = await provider.validate({ silent: false });

            if (!valid) {
                HotelAnalytic.trackUserError(
                    field,
                    { errors },
                    this.__('messages.enter_your_data')
                );
            }
        },
        async handleFieldInput(field) {
            const provider = this.$refs[field];
            const { valid } = await provider.validate({ silent: true });

            if (valid && this.showErrors[field]) {
                this.showErrors[field] = false;
            }
        },

        handleFieldFocus(field) {
            this.fieldInFocus[field] = true;
        },
        async handleFieldBlur(field) {
            this.fieldInFocus[field] = false;

            if (!this.showErrors[field]) {
                this.showErrors[field] = true;
                await this.validateField(field);
            }
        },

        async handleEmailFieldInput(field) {
            this.filterSuggestions(field);

            const provider = this.$refs[field];
            const { valid } = await provider.validate({ silent: true });

            if (valid && this.showErrors[field]) {
                this.showErrors[field] = false;
            }
        },

        handleEmailFieldFocus(field) {
            this.showSuggestions(field, true);
            this.fieldInFocus[field] = true;
        },

        async handleEmailFieldBlur(field) {

            this.showSuggestions(field, false);
            this.fieldInFocus[field] = false;

            if (!this.showErrors[field]) {
                this.showErrors[field] = true;
                await this.validateField(field);
            }
        },
        onChangeSpecialRequest() {
            if (this.user.special_request.length > this.limitTextArea) {
                this.user.special_request = this.user.special_request.substring(0, this.limitTextArea);
            }
        },
        toggleSpecialRequests() {
            this.isSpecialRequestsOpen = !this.isSpecialRequestsOpen;
        },
     handleEmailBlur(field) {
            setTimeout(() => {
                this.showSuggestionsState[field] = false;
                this.validateField(field);
                if (field === 'email') {
                    this.emailTouched = true;
                }
            }, 200);
        },

        showSuggestions(field, show) {
            this.showSuggestionsState[field] = show;
            if (show) {
                this.filterSuggestions(field);
            }
        },

        filterSuggestions(field) {
            const email = this.user[field] || '';
            const emailParts = email.split('@');

            if (emailParts.length === 1) {
                // No hay @ todavía, mostrar todos los dominios
                this.filteredSuggestions[field] = this.emailDomains;
                this.showSuggestionsState[field] = true;
            } else if (emailParts.length === 2) {
                // Hay exactamente un @, filtrar dominios basado en lo que se escribió después del @
                const searchTerm = emailParts[1].toLowerCase();
                this.filteredSuggestions[field] = this.emailDomains.filter(domain =>
                    domain.toLowerCase().startsWith(searchTerm)
                );
                this.showSuggestionsState[field] = this.filteredSuggestions[field].length > 0;
            } else {
                // Más de un @, ocultar sugerencias
                this.showSuggestionsState[field] = false;
            }

            // Resetear el índice de navegación
            this.currentSuggestionIndex[field] = -1;
        },

        navigateSuggestions(field, direction) {
            if (!this.showSuggestionsState[field]) {
                this.showSuggestionsState[field] = true;
                this.filterSuggestions(field);
                return;
            }

            const suggestions = this.filteredSuggestions[field];
            if (suggestions.length === 0) return;

            let newIndex = this.currentSuggestionIndex[field] + direction;
            
            if (newIndex < -1) newIndex = suggestions.length - 1;
            if (newIndex >= suggestions.length) newIndex = -1;
            
            this.currentSuggestionIndex[field] = newIndex;
        },

        selectCurrentSuggestion(field) {
            if (this.currentSuggestionIndex[field] >= 0 && 
                this.currentSuggestionIndex[field] < this.filteredSuggestions[field].length) {
                this.selectSuggestion(
                    field,
                    this.filteredSuggestions[field][this.currentSuggestionIndex[field]]
                );
            }
            this.showSuggestionsState[field] = false;
        },

        selectSuggestion(field, suggestion) {
            const emailPrefix = (this.user[field] || '').split('@')[0];
            this.user[field] = `${emailPrefix}@${suggestion}`;
            this.showSuggestionsState[field] = false;
            this.currentSuggestionIndex[field] = -1;
            this.$nextTick(() => {
                this.validateField(field);
            });
        },
        truncatePrefix(prefix) {
            const maxLength = 15; // Máximo de caracteres a mostrar antes de truncar
            return prefix.length > maxLength
                ? `${prefix.substring(0, maxLength)}...`
                : prefix;
        },
        checkTextOverflow() {
            if (!this.$refs.suggestionsContainer) return;
            const container = this.$refs.suggestionsContainer;
            const firstItem = container.querySelector('li');
            if (!firstItem) return;
            // Verificamos si el texto desborda el contenedor
            const isOverflowing = firstItem.scrollWidth > firstItem.offsetWidth;
            this.shouldTruncate = isOverflowing;
        },
        handleResize() {
            this.checkTextOverflow();
        },

        scrollToError(fieldName) {
            this.$nextTick(() => {
                // Verificar que el campo realmente tenga error antes de hacer focus
                const provider = this.$refs[fieldName];
                if (!provider) return;

                provider.validate({ silent: true }).then(({ valid }) => {
                    if (!valid) {
                        const element = document.getElementById(fieldName);
                        if (element) {
                            // Solo hacer focus si el campo tiene error
                            element.focus();

                            // Calcular la posición del elemento con un offset para el header
                            const elementPosition = element.getBoundingClientRect().top + window.pageYOffset;
                            const offsetPosition = elementPosition - 100; // 100px de offset para el header

                            // Hacer scroll suave al elemento
                            window.scrollTo({
                                top: offsetPosition,
                                behavior: 'smooth'
                            });
                        }
                    }
                });
            });
        }
    }
}
</script>
<style>
.special-requests-link {
    border: 0;
    color: var(--text-dark);
    text-align: left;
    font-size: 16px;
    font-weight: 500;
    height: auto;
    background-color: white;
    padding-left: 0;
    padding-right: 0;
    cursor: pointer;
    display: block;
    text-decoration: none;
}

.collapse-request {
    margin-bottom: 44px;
}

.special-requests-text {
    font: var(--title-xxs);
    color: var(--text-main);
    display: inline-block;
}

.special-requests-container {
    border: 0;
    padding: 0;
    margin-bottom: 1rem;
}

.special-requests-label {
    font: var(--body-sm);
    color: var(--text-strong);
    margin-bottom: 12px !important;
}

.special-requests-textarea {
    height: 57px;
    resize: vertical;
}

.special-requests-counter {
    float: right;
    margin-top: 4px;
    color: var(--text-subtle);
}

.email-suggestions {
    position: absolute;
    z-index: 1000;
    width: 100%;
    max-height: 200px;
    background: var(--bg-base);
    border-top: none;
    border-radius: 0 0 4px 4px;
    box-shadow: var(--shadow-200);
    overflow-y: auto;
    .list-group-item.active {
        background-color: var(--bg-level1);
        color: var(--text-main);
    }
    ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
            padding: 8px 12px;
            cursor: pointer;
            border: none !important;
            overflow: hidden;

            &:hover {
                background-color: var(--bg-level1);
            }

            .email-content {
                display: flex;
                width: 100%;
                white-space: nowrap;

                .email-prefix {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    flex-shrink: 1;
                    min-width: 0;
                }

                .email-domain {
                    flex-shrink: 0;
                }
            }
        }
    }
}
</style>
﻿class AccountAnalytics {
    constructor() {
        this.data = window.__pt.data || {};
        this.site = window.__pt.settings.site || {};
    }

    handlePayReservationClick(reservationType, productType ,idReservation) {
        let event = {
            event: "gtmEvent",
            eventAction: "Pago pendiente",
            eventCategory: "Login Profile",
            eventLabel: `${reservationType}|${productType}|${idReservation}`,
            eventName: "gtmEvent",
        };

        this.setDataLayer(event);
    }

    viewUserReservations() {
        let event = {
            event: "gtmEvent",
            eventAction: "Mis reservas",
            eventCategory: "Login Profile",
            eventLabel: "Reservas - Categorias",
            eventName: "gtmEvent"
        };

        this.setDataLayer(event);
    }

    handleCategorySelection(category) {
        let event = {
            event: "gtmEvent",
            eventAction: "Mis reservas",
            eventCategory: "Login Profile",
            eventLabel: `Reservas - Categorias - ${category}`,
            eventName: "gtmEvent"
        };

        this.setDataLayer(event);
    }

    calculateTotalReservations(total) {
        let event = {
            event: "gtmEvent",
            eventAction: "Usuario con reservas",
            eventCategory: "Login Profile",
            eventLabel: `Reservas - Total -  ${total}`,
            eventName: "gtmEvent"
        };

        this.setDataLayer(event);
    }

    editUserProfile(editOrNew, accountType) {
        let event = {
            event: "gtmEvent",
            eventAction: "Edicion de perfil",
            eventCategory: "Login Profile",
            eventLabel: `${editOrNew}|${accountType}`,
            eventName: "gtmEvent"
        };

        this.setDataLayer(event);
    }

    editCompanions(editOrNew, accountType) {
        let event = {
            event: "gtmEvent",
            eventAction: "Acompañantes Edicion",
            eventCategory: "Login Profile",
            eventLabel: `${editOrNew}|${accountType}`,
            eventName: "gtmEvent"
        };

        this.setDataLayer(event);
    }

    addNewCompanion(accountType) {
        let event = {
            event: "gtmEvent",
            eventAction: "Acompañantes Nuevo",
            eventCategory: "Login Profile",
            eventLabel: accountType,
            eventName: "gtmEvent",
        };

        this.setDataLayer(event);
    }

    trackBookingNotFoundOnImport() {
        let event = {
            event: "trackEvent",
            eventAction: "Acompañantes Nuevo",
            error_message: "importación::no encontrada",
            content_type: "boton",
            element: "form_reservaciones",
            page_type: "reservaciones",
            layer: "cuenta",
            eventName: "importacion_error",
        };

        this.setDataLayer(event);
    }

    setDataLayer(args) {
        if (window.dataLayer) {
            dataLayer.push(args);
        }
    }
}

export const accountAnalytics = new AccountAnalytics();

'use strict';

import DetailTracker from '../MetricsTracker/detail/metrics';
import CategoryTracker from '../MetricsTracker/categories/metrics'
import FooterTracker from '../MetricsTracker/footer/metrics';
import { buildGridGallery } from '../utils/pricetravel/detail/helpers';
import { getHeaders } from '../utils/pricetravel/shared/tokenUtil';
import { matchFamilyRule, paxRateFamily } from '../utils/pricetravel/shared/utilsFamily';
import { formatDate, calculateDateDifference } from '../utils/constants/date';
import BookerTracker from '../MetricsTracker/booker/metrics';
import { setPickerDates } from '../utils/constants/datePicker'
import LowPriceNotifierService from '../utils/pricetravel/shared/lowPriceNotifierService';
import GlobalUtil from '../utils/pricetravel/shared/globalUtil';
import FavsTracker from '../MetricsTracker/favorite/metrics';
import { StorageService } from '../vue/services/StorageService'
window.app.controller('DetailController', controller);

controller.$inject = ["$scope", "$http", "$filter", "$location", "$rootScope", "localStorageService", "$timeout"];

function controller($scope, $http, $filter, $location, $rootScope, localStorageService, $timeout) {
    let config = window.__pt.settings.site;
    let vm = this;
    let fn = window.__pt.fn;
    let sliderClass = '.slider-gallery-view';
    let hotel = window.__pt.hotel || {};
    let settings = window.__pt.settings;
    let WidthDevice = window.innerWidth || document.documentElement.clientWidth;
    let _isBot = window.__pt.settings.rb || false;
    let box = window.__pt.box || {};
    let place = window.__pt.place || {};
    let placeContainer = window.__pt.placeContainer || {};
    let cultureData = window.__pt.cultureData || {};
    let ln = window.__pt.ln || {};
    let _user = window.__pt.user || null;
    let skillBase = {};
    let userLocation = window.__pt.userLocation;
    let providers = window.__pt.providers && window.__pt.providers.providersId ? window.__pt.providers.providersId.join(',') : '';
    let memorySlider = {};
    let _showMoreRooms = true;
    let _successSkillBase = "200 - OK";
    let _campaignToken = null;
    let _campaignId = null;
    let _globalUtil = new GlobalUtil();
    let _userKey = _globalUtil.getUserKey();
    let _userKeyFac = _globalUtil.getUserKeyFavorites();
    let _isLogin = !!_user;
    let headerPhoneId = '.header-phonebase';
    let priceInfoId = '#price-modal-info';
    let promotionsBlackList = [' Special Price.', ' Precio Especial.', 'Precio especial'];
    let countriescodes = { 'MEX': 'MX', 'COL': 'CO' };
    let _paramQuery = fn.search();
    let _paxes = {};
    let trackerFooter = new FooterTracker();
    let tracker = null;
    let errorMessagesStatus = null;
    let pax_after_requote = null;
    let isMobile = fn.mobileAndTabletCheck();
    let sharedGlobalKey = "";
    let sharedGlobalUrl = "";
    let params = {
        checkIn: box.checkIn,
        checkOut: box.checkOut
    }
    let trackerMetric = new BookerTracker();
    let trackerFavs = new FavsTracker();
    let trackCategory = new CategoryTracker();
    let paramsSkillBase = {
        checkInDate: null,
        checkOutDate: null,
        placeId: placeContainer.id,
        adultQuantity: 0,
        kidQuantity: 0,
        hotelId: hotel.hotelId,
        stepId: 3
    };
    vm.roomsTotal = _paramQuery.rooms || 0;
    let paramsRoomRates = {
        checkin: box.checkIn,
        checkout: box.checkOut,
        hotelid: hotel.hotelId,
        CampaignToken: _campaignToken,
        source: box.source ? box.source : "SPA-Hotel-List",
        ispackage: false,
        site: config.domain,
        culture: cultureData.internalCultureCode,
        rooms: 2,
        promotions: _paramQuery.promotions ? _paramQuery.promotions.split(',') : null
    }
    let paramnsRevalidate = {
        checkin: box.checkIn,
        checkout: box.checkOut,
        hotelid: hotel.hotelId,
        ispackage: false,
        site: config.domain
    }
    let roomsLogin = [];
    vm.customLimit = 0;
    vm.RAPDObject = {
        totalSticky: 0,
        totalHasTaxSticky: false,
        totalImpSticky: 0,
        totalFeesIncludes: 0
    };

    vm.allImagesText = '@viewHelper.Localizer("all_images")';
    vm.isFilterMenuOpen = false;
    vm.selectedClasificationName = null;
    vm.galleryIndexIndex = null;
    vm.defaultHotel = settings.defaultHotel;
    vm.checkIn = box.checkIn;
    vm.checkOut = box.checkOut;
    vm._isLogin = _isLogin;
    vm.dateConfig = settings.formatDateSlash;
    vm.dateConfigbook = settings.formatDateSlashBook;
    vm.configPrefix = settings.site.sufix;
    vm.mealplans = ln.mealplans;
    vm.lnMessages = ln;
    vm.hotel = hotel;
    vm.rooms = [];
    vm.availableRooms = [];
    vm.unavailableRooms = [];
    vm.reviewsLoadingModal = true;
    vm.gallery = [];
    vm.grid = [];
    vm.isTrivagoModal = false;
    vm.modalRapdCurrentData = null;
    vm.getRecomedationLabelSticky = "";
    vm.currentRoom = null;
    vm.cheaperRoomRate = {
        messages: "",
        disabled: true,
        loading: true
    };
    vm.clasifications = [];
    vm.filteredImages = [];
    vm.code = config.code;
    vm.boxData = { rooms: 0, nights: 0, totalPax: 0 };
    vm.assigRateSet = {};
    vm.assigRoomRateModal = {};
    vm.isTrivagoRate = _paramQuery.room_search ? true : false;
    vm.listReviews = {};
    vm.showDetailScoreReviews = false;
    vm.sliderIsInit = false;
    vm.loading = true;
    vm.toggleDescription = true;
    vm.toggleDescriptionAbout = true;
    vm.isIncompletePax = "";
    vm.overViewMinors = "";
    vm.averageDetailToggle = false;
    vm.phoneBase = config.phoneDefault || "";
    vm.currency = config.currencyCodeName;
    vm.roomsLimit = config.limitRoomsRates || 0;
    vm.ratesLimit = config.limitRates || 0;
    vm.roomsAvailables = 0;
    vm.limitRoomsFilter = vm.roomsLimit;
    vm.roomRateMobileSelected = {};
    vm.source_origin = "";
    vm.mealplanCodesList = [];
    vm.sizeDescriptionString = config.hotelAboutConfigIdeal;
    vm.responsePayment = {
        fixedPayments: [],
        monthInterestFree: []
    };
    vm.TitleModalRAPD = "";
    vm.dateRAPDSticky = "";
    vm.isCancelation = false;
    vm.selectModalRoom = 0;
    vm.totalSticky = 0;
    vm.totalPromoDiscount = 0;
    vm.totalImpSticky = 0;
    vm.totalFeesIncludes = 0;
    vm.totalRoomSticky = 0;
    vm.viewDetailSticky = false;
    vm.totalHasTaxSticky = false;
    vm.datesRecommended = [];
    vm.pageError = {
        has: false,
        type: null
    }
    vm.responseroomsAvailables = [];
    vm.monthInterestData = {};
    vm.ratesResponse = {
        rooms: []
    };
    vm.sumTotalSticky = 0;
    vm.ratesResponse = {};
    vm.onSubmitLoading = false;
    vm.onSubmitLoadingRecomendation = false;
    vm.hasCancellationFree = false;
    vm.hasPayLater = false;
    vm.isBlockRAPD = false;
    vm.RateForRAPD = {};
    vm.roomsForRAPD = "";
    vm.headTabs = {
        rooms: '#rooms',
        amenities: '#amenities',
        map: '#location',
        current: '',
    }
    vm.featuredRoom = {};
    vm.isRequote = false;
    vm.boxData = { rooms: 0, nights: 0, totalPax: 0 };
    vm.roomDelTrivago = {};
    vm.indexRoomTrivago = -1;
    vm.recommendedDates = null;
    vm.sourceTriGooHotelsIds = false;
    vm.isMsiAvailable = false;
    vm.msiMonthsAllow = [];
    vm.notAmount = false;
    vm.notMessage = false;
    let copyBox = {};
    vm.isRAPDRecomendation = false;
    vm.hasErrorHandle = false;
    vm.rateModal = {};
    vm.roomModal = {
        totalRooms: 0
    };
    vm.roomIndexModal = {};
    vm.isGoSection = false;
    //favorites
    const service = new LowPriceNotifierService(config.domainAPIUrl);
    vm.alertsFavorites = {
        notificationSuccess: false,
        notificationError: false,
        notificationWarning: false,
        message: "",
        actionType: '' // 'save' or 'delete'
    };
    vm.favoriteHotels = [];
    vm.showTimer = false;
    let notificationTimeout = null;
    vm.sourceOrigen = "";
    vm.filteredServices = filterServicesByCharge(vm.hotel.services);
    vm.visibleServicesCount = 9;
    vm.visibleSInfoHotelCount = 2;
    vm.toggleDescription = false;
    vm.toggleServices = false;
    vm.toggleSellingPoints = false;
    vm.stickyRates = [];
    vm.totalTotalSticky = 0;
    vm.isRecomendationCheckout = false;
    vm.recomendationsList = {
        loading: true,
        rooms: [],
        checkoutHash: []
    }
    vm.recomendationsListCopy = {
        rooms: []
    }
    vm.showStickyHeader = false;
    vm.quantityLoop = 0;
    let lang = window.__pt.ln;
    vm.roomsAvailLoading = false;
    let newPax = {};
    let newPaxRequote = {};
    vm.lengthPaxes = lengthPax(box.pax);
    vm.paxFam = newPax;
    vm.applicableRAPDSticky = false;
    vm.appicableExperiment = WidthDevice >= 992;
    vm.openServicesSections = {};
    vm.nearbyPlacesCategory = 0;
    vm.applicableGallery = window.innerWidth <= 767;
    const hasAdultsOnly = hotel.tags && hotel.tags.some(tag => {
        const name = tag.name.toLowerCase();
        return name === "solo adultos" || name === "adults only";
    });
    vm.toltipSticky = false;
    vm.totalChildrens = 0;
    vm.validPax = false;
    vm.onlyAdults = hasAdultsOnly;
    vm.currentSlideIndex = 1;
    vm.currentSlideTitle = "";
    //Favorites funciontality
    vm.getFavoriteHotelDetails = function () {
        return vm.favoriteHotels.find(favHotel => favHotel.hotelId === vm.hotel.hotelId &&
            favHotel.checkin === box.checkIn &&
            favHotel.checkout === box.checkOut &&
            angular.equals(comvertToAB(favHotel.rooms), box.pax)
        );
    };
    vm.updateHotelsState = function () {
        const favoriteHotel = vm.getFavoriteHotelDetails();
        if (favoriteHotel) {
            vm.hotel.isFavorite = true;
            vm.hotel.favoriteDetails = favoriteHotel;
        } else {
            vm.hotel.isFavorite = false;
            vm.hotel.favoriteDetails = null;
        }
    };
    vm.addFavorites = function (event) {
        event.preventDefault();
        event.stopPropagation();

        let copyBoxPax = comvertToMain(box.pax);
        const addHotelRequest = {
            hotelId: hotel.hotelId,
            userId: _userKeyFac,
            uri: place.uri,
            site: config.domain,
            channel: isMobile ? config.chkSourceMobile : config.chkSourceDesktop,
            originalPrice: vm.cheaperRoomRate.disabled ? 0 : vm.cheaperRoomRate.taxes.totalRoomRatePerNight,
            checkin: box.checkIn,
            checkout: box.checkOut,
            rooms: copyBoxPax.map(pax => ({
                adults: pax.adults,
                children: pax.children.map(child => child.year)
            })),
            campaignToken: _campaignToken || "",
            placeId: placeContainer.id,
            placeUri: placeContainer.uri,
            placeType: placeContainer.type,
            placeName: placeContainer.displayText,
            isMobile: isMobile
        };
        if (vm.hotel.isFavorite) {
            const favoriteId = vm.getFavoriteId(hotel.favoriteDetails.id, box.checkIn, box.checkOut);
            if (favoriteId) {
                service.removeHotel({ Id: favoriteId, UserId: _userKeyFac }).then(response => {
                    $scope.$applyAsync(() => {

                        vm.favoriteHotels = vm.favoriteHotels.filter(favHotel =>
                            !(favHotel.hotelId === hotel.hotelId &&
                                favHotel.checkin === box.checkIn &&
                                favHotel.checkout === box.checkOut &&
                                angular.equals(comvertToAB(favHotel.rooms), box.pax))
                        );
                        const favoriteHotel = { ...addHotelRequest, id: favoriteId, name: hotel.name };
                        trackerFavs.logDeleteFav(favoriteHotel, 'details');
                        vm.updateHotelsState();
                        vm.showNotification('error', hotel.name, 'delete');
                    });
                }).catch(error => {
                    $scope.$applyAsync(() => {
                        vm.showNotification('error', error);
                    });
                });
            }
        } else {
            // Agregar el hotel a favoritos
            service.addHotel(addHotelRequest).then(response => {
                $scope.$applyAsync(() => {
                    if (response.value) {
                        const favoriteHotel = { ...addHotelRequest, id: response.value, name: hotel.name };
                        vm.favoriteHotels.push(favoriteHotel);
                        trackerFavs.logAddFav(favoriteHotel, 'details');
                        vm.updateHotelsState();
                        vm.showNotification('success', hotel.name, 'save');
                    } else {
                        vm.showNotification('warning', '', '');
                    }

                });
            }).catch(error => {
                $scope.$applyAsync(() => {
                    vm.showNotification('error', error);
                });
            });
        }
        _globalUtil.vibrate();
    };
    vm.showNotification = (type, message, actionType) => {
        // Cancelar cualquier timeout anterior
        if (notificationTimeout) {
            $timeout.cancel(notificationTimeout);
        }
        vm.showTimer = false;
        $timeout(() => {
            vm.showTimer = true;
        });
        vm.alertsFavorites.notificationSuccess = type === 'success';
        vm.alertsFavorites.notificationError = type === 'error';
        vm.alertsFavorites.notificationWarning = type === 'warning';
        vm.alertsFavorites.message = message;
        vm.alertsFavorites.actionType = actionType; // 'save' or 'delete'

        // Ocultar la notificación después de 6 segundos
        notificationTimeout = $timeout(function () {
            vm.alertsFavorites.notificationSuccess = false;
            vm.alertsFavorites.notificationError = false;
            vm.alertsFavorites.notificationWarning = false;
            vm.alertsFavorites.message = "";
            vm.alertsFavorites.actionType = '';
        }, 6000);
    };

    function getFavorites() {
        const request = { UserId: _userKeyFac, country: config.country.toUpperCase() };

        service.getHotelsByUser(request).then(response => {
            $scope.$applyAsync(() => {
                vm.favoriteHotels = response.value;
                vm.updateHotelsState();

            });
        }).catch(error => {
            console.error('Error fetching hotels by user:', error);
        });
    }

    vm.getFavoriteId = function (Id, checkin, checkout) {
        const favorite = vm.favoriteHotels.find(favHotel =>
            favHotel.id === Id &&
            favHotel.checkin === checkin &&
            favHotel.checkout === checkout &&
            angular.equals(comvertToAB(favHotel.rooms), box.pax)
        );
        return favorite ? favorite.id : null;
    };

    vm.isFavoriteHotel = () => {
        return vm.favoriteHotels.some(favHotel =>
            favHotel.hotelId === hotel.hotelId &&
            favHotel.checkin === box.checkIn &&
            favHotel.checkout === box.checkOut &&
            angular.equals(comvertToAB(favHotel.rooms), box.pax)
        );
    };
    function comvertToAB(input) {
        return input.reduce((acc, room) => {
            acc.adults += room.adults;
            acc.children = acc.children.concat(room.children.map(age => ({ "year": age })));
            acc.rooms += 1;
            return acc;
        }, { "adults": 0, "children": [], "rooms": 0 });
    }
    function comvertToMain(data) {
        const { adults, children, rooms } = data;

        const adultsPerRoom = Math.floor(adults / rooms);
        const extraAdults = adults % rooms;

        const childrenPerRoom = Math.floor(children.length / rooms);
        const extraChildren = children.length % rooms;

        const result = [];

        let childIndex = 0;

        for (let i = 0; i < rooms; i++) {
            const currentRoomAdults = adultsPerRoom + (i < extraAdults ? 1 : 0);
            const currentRoomChildrenCount = childrenPerRoom + (i < extraChildren ? 1 : 0);
            const currentRoomChildren = [];
            for (let j = 0; j < currentRoomChildrenCount; j++) {
                currentRoomChildren.push({ year: children[childIndex].year });
                childIndex++;
            }

            result.push({
                adults: currentRoomAdults,
                children: currentRoomChildren
            });
        }

        return result;
    }
    $rootScope.$on(settings.bookerChangeEvent, function (event, args) {
        vm.roomsAvailLoading = false;
        vm.validPax = false;
        let paxesDeslink = fn.clone(args);
        vm.recomendationsList = {
            loading: true,
            rooms: [],
            checkoutHash: []
        }
        vm.recomendationsListCopy = {
            rooms: []
        }
        vm.hasErrorHandle = false;
        roomsLogin = [];
        vm.isTrivagoRate = false;
        box.pax = paxesDeslink.paxes;
        paramsRoomRates.checkin = fn.formatDate(args.checkIn, settings.formatDate);
        paramsRoomRates.checkout = fn.formatDate(args.checkOut, settings.formatDate);
        box.checkIn = paramsRoomRates.checkin;
        box.checkOut = paramsRoomRates.checkout;
        paramnsRevalidate.checkin = fn.formatDate(args.checkIn, settings.formatDate);
        paramnsRevalidate.checkout = fn.formatDate(args.checkOut, settings.formatDate);
        vm.updateHotelsState();
        newPax = matchFamilyRule(box.pax, vm.appicableExperiment);
        vm.lengthPaxes = lengthPax(box.pax);
        copyBox = fn.clone(box);
        vm.paxFam = newPax;
        initializeValues();
        vm.stickyRates = [];
        vm.isGoSection = true;

        loadRoomRates();
        
        if (!vm.notMessage) {
            loadRatesFromLogin();
        }

        if (settings.site.siteName == "pricetravel" && userLocation.country == "MX") {
            getSkillBase();
        }
    });

    function init() {
        vm.roomsAvailLoading = false;
        vm.recomendationsList = {
            loading: true,
            rooms: []
        }
        vm.recomendationsListCopy = {
            rooms: []
        }
        caruosel();
        vm.validPax = false;
        vm.notAmount = config.hotelsTagLogin ? config.hotelsTagLogin.listA.includes(hotel.hotelId) : false;
        vm.notMessage = config.hotelsTagLogin ? config.hotelsTagLogin.listB.includes(hotel.hotelId) : false;
        var paramns = _paramQuery.source || null;
        fn.setCookie("source_origin", paramns, "1");
        vm.source_origin = fn.getCookie("source_origin");

        let codept = _paramQuery.coupon;
        var codeptFromCookie = fn.getCookie("codept");
        var finalCodept = codept || codeptFromCookie;

        fn.setCookie("codept", finalCodept, "1");

        let indexHotelPla = config.hotelsIdsPR ? config.hotelsIdsPR.find(x => x == hotel.hotelId) : -1;
        if (indexHotelPla > -1) { //&& (vm.source_origin == "trivago" || vm.source_origin == "google-hotel-ads")) {
            vm.sourceTriGooHotelsIds = true;
        }
        copyBox = fn.clone(box);
        initializeValues();
        newPax = matchFamilyRule(box.pax, vm.appicableExperiment);
        vm.paxFam = newPax;
        vm.isGoSection = false;
        if (vm.source_origin) {
            loadRoomRatesTrivago();
        }

        loadRoomRates();
      
        getFavorites();
        if (!vm.notMessage) {
            loadRatesFromLogin();
        }
        if (!settings.rb) {
            if (settings.site.siteName == "pricetravel" && userLocation.country == "MX") {
                getSkillBase();
            }

            onSearchPaymentMethods();
            initStickySearchBox()
            transformDescription();
            handleScroll();
            vm.grid = buildGridGallery(vm.gallery);
            setRecentDestination(hotel);
        }

    }

    function initializeValues() {
        _paxes = getPaxes();
        vm.gallery = vm.hotel.gallery;
        vm.rooms = vm.hotel.rooms;
        if (config.showReviews && !settings.rb) {
            vm.reviewsLoadingModal = true;
            vm.getReviews(vm.hotel.hotelId, false);
        }
        initialCampaignToken();
    }
    /*FAMILIAS*/
    function lengthPax(pax) {
        let totalPax = pax.adults;
        if (pax.children && pax.children.length > 0) {
            totalPax += pax.children.length;
        }
        return totalPax;
    }
    vm.setRecomedationLabel = () => {
        const roomTitle = vm.recomendationsList.totalRooms == 1 ? lang.room : lang.rooms;
        const adultsTitle = newPax.paxes.adults == 1 ? lang.adult : lang.adults;
        const childrenTitle = newPax.paxes.children.length == 1 ? lang.child : lang.children;
        const childrenPart = newPax.paxes.children.length > 0
            ? `, ${newPax.paxes.children.length} ${childrenTitle}`
            : '';
        return `${vm.recomendationsList.totalRooms} ${roomTitle} ${lang.for} ${newPax.paxes.adults} ${adultsTitle}${childrenPart}`;
    }
    vm.setRecomendationMetas = () => {
        const roomTitle = vm.roomsTotal == 1 ? lang.room : lang.rooms;
        const adultsTitle = newPax.paxes.adults == 1 ? lang.adult : lang.adults;
        const childrenTitle = newPax.paxes.children.length == 1 ? lang.child : lang.children;
        const childrenPart = newPax.paxes.children.length > 0
            ? `, ${newPax.paxes.children.length} ${childrenTitle}`
            : '';
        return `${vm.roomsTotal} ${roomTitle} ${lang.for} ${newPax.paxes.adults} ${adultsTitle}${childrenPart}`;
    }
    vm.getRecomendateDatePaxNight = (days) => {
        const nightTitle = days == 1 ? lang.night : lang.nights;
        return `${days} ${nightTitle}`;
    }
    vm.getRecomendateDatePax = (days) => {
        const adultsTitle = newPax.paxes.adults == 1 ? lang.adult : lang.adults;
        const childrenTitle = newPax.paxes.children.length == 1 ? lang.child : lang.children;
        const childrenPart = newPax.paxes.children.length > 0
            ? ` ${lang.and} ${newPax.paxes.children.length} ${childrenTitle}:`
            : ':';
        return `${lang.for} ${newPax.paxes.adults} ${adultsTitle}${childrenPart}`;
    }

    vm.getIsPaxesDefined = () => {
        return newPax.paxes.adults == 2 && newPax.paxes.children.length == 0;
    }
    //3 adultos, 1 menor
    vm.getRoom = (total) => {
        return `${total == 1 ? lang.room : lang.rooms}`;
    }
    vm.getTitleStickyDetail = (paramns, rate) => {
        if (params) {
            let datos = paramns.split("|");
            const adultsTitle = datos[0] * rate.selectedRoomValue == 1 ? lang.adult : lang.adults;
            const childrenAges = datos[1] ? datos[1].split(",").length * rate.selectedRoomValue : "";
            if (childrenAges) {
                const childrenTitle = datos[1].split(",").length * rate.selectedRoomValue == 1 ? lang.child : lang.children;
                return `${datos[0] * rate.selectedRoomValue} ${adultsTitle}, ${datos[1].split(",").length * rate.selectedRoomValue} ${childrenTitle}`;
            } else {
                return `${datos[0] * rate.selectedRoomValue} ${adultsTitle}`;
            }
        }
    }
    vm.getNumDistri = (params, AorM) => {
        if (params) {
            let datos = params.split("|");
            const adultsTitle = datos[0] == 1 ? lang.adult : lang.adults;
            if (datos.length > 1) {
                var titleChildren = datos[1].split(",").length == 1 ? lang.child : lang.children
                return AorM ? `${datos[0]} ${adultsTitle}` : `${datos[1].split(",").length} ${titleChildren}`;
            } else {
                return AorM ? `${datos[0]} ${adultsTitle}` : 0;
            }
        }
    }
    vm.getTitleRecomendedPax = (params) => {
        if (params) {
            let datos = params.split("|");
            const adultsTitle = datos[0] == 1 ? lang.adult : lang.adults;
            if (datos.length > 1) {
                var titleChildren = datos[1].split(",").length == 1 ? lang.child : lang.children;
                return `${datos[0]} ${adultsTitle}, ${datos[1].split(",").length} ${titleChildren}`;
            } else {
                return `${datos[0]} ${adultsTitle}`;
            }
        }
    }
    //(1 y 4 años)
    vm.getNumDistriToltip = (params) => {
        let datos = params.split("|");
        const adultsTitle = datos[0] == 1 ? lang.adult : lang.adults;
        const childrenAges = datos[1] ? datos[1].split(",").join(", ") : "";
        if (childrenAges) {
            const childrenTitle = datos[1].length == 1 ? lang.child : lang.children;
            return `(${childrenAges} años)`;
        }
    };
    vm.getNumDistriMovil = (params, AorM) => {
        if (params) {
            let datos = params.split("|");
            if (datos.length > 1) {
                return AorM ? parseInt(datos[0]) : parseInt(datos[1].split(",").length);
            } else {
                return AorM ? parseInt(datos[0]) : 0;
            }
        }
    }
    vm.distriTablePax = (params) => {
        if (params) {
            let datos = params.split("|");
            const adultsTitle = datos[0] == 1 ? lang.adult : lang.adults;
            const childrenTitle = datos[1] && datos[1].split(",").length == 1 ? lang.child : lang.children;

            const childrenAges = datos[1] ? datos[1].split(",").join(", ") : "";
            if (childrenAges) {
                return `${parseInt(datos[0])} ${adultsTitle}, ${datos[1].split(",").length} ${childrenTitle} ${lang.the} ${childrenAges} ${datos[1].split(",").length == 1 && childrenAges == "1" ? lang.year : lang.years}`;
            } else {
                return `${parseInt(datos[0])} ${adultsTitle}`;
            }

        }
    };
    //(1 y 4 años)
    vm.getNumDistriToltipMovil = (params) => {
        let datos = params.split("|");
        const adultsTitle = datos[0] == 1 ? lang.adult : lang.adults;
        const childrenAges = datos[1] ? datos[1].split(",").join(", ") : "";
        if (childrenAges) {
            const childrenTitle = datos[1].split(",").length == 1 ? lang.child : lang.children;
            return `${datos[0]} ${adultsTitle} y ${datos[1].split(",").length} ${childrenTitle} (de ${childrenAges} ${lang.years})`;
        } else {
            return `${datos[0]} ${adultsTitle}`;
        }
    };
    vm.getRecomendationTitlePax = (params) => {
        let datos = params.split("|");
        const rooms = newPax.paxes.rooms > 1 ? `${lang.fora} ${lang.room}` : ``;
        if (datos.length > 1) {
            const adultsTitle = datos[0] == 1 ? lang.adult : lang.adults;

            const childrenTitle = datos[1].split(",").length == 1 ? lang.child : lang.children;
            return `${lang.for} ${datos[0]} ${adultsTitle},  ${datos[1].split(",").length} ${childrenTitle} ${rooms}`;
        } else {
            const adultsTitle = datos[0] == 1 ? lang.adult : lang.adults;
            return `${lang.for} ${datos[0]} ${adultsTitle} ${rooms}`;
        }
    }
    vm.titlePax = () => {
        const roomTitle = vm.recomendationsList.totalRooms == 1 ? lang.room : lang.rooms;
        let checkIn = fn.getDate(paramsRoomRates.checkin);
        let checkOut = fn.getDate(paramsRoomRates.checkout);
        const nightCounts = fn.diffBetweenDays(checkIn, checkOut);
        const nightTitle = nightCounts == 1 ? lang.night : lang.nights;
        return `${vm.recomendationsList.totalRooms} ${roomTitle}, ${nightCounts} ${nightTitle} `;
    }
    vm.titlePaxMeta = () => {
        const roomTitle = vm.roomsTotal == 1 ? lang.room : lang.rooms;
        let checkIn = fn.getDate(paramsRoomRates.checkin);
        let checkOut = fn.getDate(paramsRoomRates.checkout);
        const nightCounts = fn.diffBetweenDays(checkIn, checkOut);
        const nightTitle = nightCounts == 1 ? lang.night : lang.nights;
        return `${vm.roomsTotal} ${roomTitle}, ${nightCounts} ${nightTitle} `;
    }
    vm.getTitleSticky = () => {
        const nightCounts = vm.getNights();
        const nightTitle = nightCounts == 1 ? lang.night : lang.nights;
        return `${vm.totalRoomSticky} ${vm.totalRoomSticky > 1 ? lang.rooms : lang.room}, ${vm.getNights()} ${nightTitle}`;
    }
    vm.getNights = () => {
        let checkIn = fn.getDate(paramsRoomRates.checkin);
        let checkOut = fn.getDate(paramsRoomRates.checkout);
        return fn.diffBetweenDays(checkIn, checkOut);
    }
    vm.getTitleModalRAPD = (isRecommendation) => {
        if (!isRecommendation) {
            return `${lang.total} ${vm.totalRoomSticky} ${vm.totalRoomSticky > 1 ? lang.rooms : lang.room}, ${vm.sumTotalSticky} ${vm.sumTotalSticky > 1 ? lang.persons : lang.person}:`;
        } else {
            const sumTotal = newPax.paxes.adults + newPax.children;
            return `${lang.total} ${vm.recomendationsList.totalRooms} ${vm.recomendationsList.totalRooms > 1 ? lang.rooms : lang.room}, ${sumTotal} ${sumTotal > 1 ? lang.persons : lang.person}:`;
        }

    }
    vm.getRecomendationTitleTotal = () => {
        const adultsTitle = newPax.paxes.adults == 1 ? lang.adult : lang.adults;
        const childrenPart = newPax.children > 0
            ? `, ${newPax.children} ${newPax.children == 1 ? lang.child : lang.children}`
            : '';
        let checkIn = fn.getDate(paramsRoomRates.checkin);
        let checkOut = fn.getDate(paramsRoomRates.checkout);
        const nightCounts = fn.diffBetweenDays(checkIn, checkOut);
        const nightTitle = nightCounts == 1 ? lang.night : lang.nights;
        return `${nightCounts} ${nightTitle}, ${newPax.paxes.adults} ${adultsTitle}${childrenPart}`;
    };
    vm.asigRateModal = (rate, room = {}) => {
        vm.assigRateSet = rate;
        vm.selectModalRoom = vm.assigRateSet.selectedRoomValue ?? 0;
        vm.assigRoomRateModal = room;
    }
    vm.showModal = (modal, btnTitle = "", btnText = "") => {
        $(`#${modal}`).modal("show");
        if (modal == "modal-payform" || modal == "modal-contact") {
            trackerFooter.selectContent(btnTitle, btnText);
        }
        if (modal === 'gallery-modal') {
            memorySlider = {};
            let paramsClasification = {
                hotelId: vm.hotel.hotelId,
                culture: cultureData.internalCultureCode
            };
            $http.get(config.endPoints.clasificationsUrl, { params: paramsClasification, headers: getHeaders() }).then(onSuccessClasifications, (err) => {
                onErrorCategories(err);
            });
        }
    }

    vm.showModalReviews = (modal, option) => {
        var content = new Object();
        content.calf = vm.listReviews?.ratings?.guest?.overall;
        content.calfDesc = vm.getOverallText(Math.round(vm.listReviews?.ratings?.guest?.overall));
        content.content_type = "link";
        if (option == "galery") {
            content.elementText = "reviews_header";
        } else if (option = "detail") {
            content.elementText = "reviews_content";
        }
        tracker.selectedReviews(content);
        vm.getReviews(vm.hotel.hotelId, true);
        $(`#${modal}`).modal("show");
    }

    vm.getHotelFeeDescription = () => {
        let text = '';

        if (vm.hotel.fees) {
            const description = vm.hotel.fees.description || '';
            const alternative = vm.hotel.fees.alternative || '';

            const validDescription = hasMeaningfulContent(description) ? description : '';
            const validAlternative = hasMeaningfulContent(alternative) ? alternative : '';

            text = [validDescription, validAlternative].filter(Boolean).join('');
        }

        if (text == "" && vm.cheaperRoomRate.externalSalesAdvisory) {
            text = vm.cheaperRoomRate.externalSalesAdvisory;
        }

        if (!vm.cheaperRoomRate.showAditional && text && text.length > 500) {
            return text.substring(0, 500) + '...';
        }

        return text;
    };


    vm.eventSetReviews = () => {
        vm.showDetailScoreReviews = !vm.showDetailScoreReviews;
        var content = new Object();
        content.calf = vm.listReviews.ratings.guest.overall;
        content.calfDesc = vm.getOverallText(Math.round(vm.listReviews.ratings.guest.overall));
        content.elementText = "reviews_scoredetail";
        content.content_type = "button";
        tracker.selectedReviews(content);
    }
    vm.showModalRAPDOrigin = (modal, rate, room) => {
        if (rate) {
            vm.RateForRAPD = rate;
            vm.roomsForRAPD = room;
        }
        $(`#${modal}`).modal("show");
    }
    vm.showModalRAPD = (modal, rate, room, roomIndex, trivago = false) => {
        if (rate) {
            vm.rateModal = rate;
            vm.roomModal = room;
            vm.roomModal.totalRooms = vm.isRequote ? newPaxRequote.paxes.rooms : newPax.paxes.rooms;
            vm.roomIndexModal = roomIndex;
            vm.isTrivagoModal = trivago
        }


        $(`#${modal}`).modal("show");
    }
    vm.showModalRAPDFam = (modal, isRecommendation = false) => {
        vm.isTrivagoModal = false;
        if (vm.stickyRates == 0 && !isRecommendation) {
            vm.hasErrorHandle = true;
            return;
        } else {
            vm.hasErrorHandle = false;
        }

        vm.modalRapdCurrentData = null;
        vm.RAPDObject = {
            totalSticky: 0,
            totalHasTaxSticky: false,
            totalImpSticky: 0,
            totalTotalSticky: 0,
            feesAmountTotal: 0
        };
        if (isRecommendation) {
            let totalAdulst = 0;
            let totalChildrens = 0;
            // Verificar las condiciones en vm.recomendationsList.rooms
            const allConditionsMet = Object.values(vm.recomendationsList.rooms).every(room => {
                // Asegúrate de ajustar las propiedades y condiciones según la estructura de tus datos
                const rate = room.rate.rate;
                const paxFamParts = rate.paxFam.split('|');
                const adults = paxFamParts[0] ? parseInt(paxFamParts[0]) : 0;
                const childs = paxFamParts[1] ? parseInt(paxFamParts[1].split(',').length) : 0;
                totalAdulst += adults;
                totalChildrens += childs;
                if (rate && rate.bookNowPayLaterTimeLimit && rate.isBookNowPayLaterApplicable && !vm.isBlockRAPD && rate.collectType != 2) {
                    vm.dateRAPDSticky = rate.bookNowPayLaterTimeLimit;
                    return true; // La condición se cumple
                } else {
                    return false; // La condición no se cumple
                }
            });

            if (allConditionsMet && modal) {
                let checkIn = fn.getDate(paramsRoomRates.checkin);
                let checkOut = fn.getDate(paramsRoomRates.checkout);
                const nightCounts = fn.diffBetweenDays(checkIn, checkOut);
                const nightTitle = nightCounts == 1 ? lang.night : lang.nights;
                const adultsTitle = totalAdulst == 1 ? lang.adult : lang.adults;
                const childrenPart = totalChildrens > 0
                    ? `, ${totalChildrens} ${totalChildrens == 1 ? lang.child : lang.children}`
                    : '';
                vm.sumTotalSticky = totalAdulst + totalChildrens;
                vm.getRecomedationLabelSticky = `${nightCounts} ${nightTitle}, ${totalAdulst} ${adultsTitle}${childrenPart}`;

                vm.isRecomendationCheckout = true;
                vm.applicableRAPDSticky = true;
                vm.RAPDObject.totalSticky = vm.recomendationsList.totalRoomRatePerNight;
                vm.RAPDObject.totalHasTaxSticky = vm.recomendationsList.hasTaxes;
                vm.RAPDObject.totalImpSticky = vm.recomendationsList.totalTaxesPerRoomPerNight;
                vm.RAPDObject.totalTotalSticky = vm.recomendationsList.totalRoomRate;
                const sumTotal = newPax.paxes.adults + newPax.children;
                vm.RAPDObject.TotalPersons = sumTotal;
                vm.RAPDObject.TotalRooms = vm.recomendationsList.totalRooms;
                vm.RAPDObject.feesAmountTotal = vm.recomendationsList.feesAmountTotal;
                vm.TitleModalRAPD = vm.getTitleModalRAPD(isRecommendation);
                $(`#${modal}`).modal("show");
            } else {
                vm.applicableRAPDSticky = false;
                vm.onSubmit(null, null, null, false, isRecommendation);
            }
        } else {
            if (vm.applicableRAPDSticky && modal) {
                vm.isRecomendationCheckout = false;
                vm.RAPDObject.totalSticky = vm.totalSticky;
                vm.RAPDObject.totalHasTaxSticky = vm.totalHasTaxSticky;
                vm.RAPDObject.totalImpSticky = vm.totalImpSticky;
                vm.RAPDObject.totalFeesIncludes = vm.totalFeesIncludes;
                vm.RAPDObject.TotalPersons = vm.sumTotalSticky;
                vm.RAPDObject.totalTotalSticky = vm.totalTotalSticky;
                vm.RAPDObject.TotalRooms = vm.totalRoomSticky;
                vm.RAPDObject.feesAmountTotal = vm.totalFeesIncludes;
                vm.TitleModalRAPD = vm.getTitleModalRAPD(false);
                $(`#${modal}`).modal("show");
            } else {
                vm.onSubmit(null, null, null, false, false);
            }
        }
    }
    vm.showModalShared = (modal) => {
        $(`#${modal}`).modal("show");
    }

    vm.showAmenitiesModal = (modal, room) => {
        vm.showModal(modal);
        vm.currentRoom = { ...room };
    }



    vm.slideCarrousel = async (event, room, direction) => {
        event.preventDefault();

        room.galleryPosition += direction == 'right' ? +1 : -1;

        $timeout(function () {
            room.picture.cloudUri = room.pictures[Math.abs((room.galleryPosition - 1) % room.pictures.length)]["cloudUri"];
        }, 0);

    }

    vm.showInclusiveModal = (modal) => {
        vm.showModal(modal);
    }

    vm.onClosed = (modal) => {
        $(`#${modal}`).modal("hide");
    }

    vm.lazyLoadImage = ({ isVisible, element }, url) => {
        const image = vm.filteredImages.find(img => img.uri === url);
        if (isVisible && image && !image.loaded) {

            element.style.backgroundImage = isVisible ? `url(${url})` : element.style.backgroundImage;
            image.loaded = true;
        }
    }

    vm.renderMap = () => {
        let context = vm;
        vm.showCardMap = true;

        const handleMap = () => {
            const mapElement = document.getElementById("map");
            if (!mapElement) {
                return;
            }

            if (!context.hotel || !context.hotel.location ||
                typeof context.hotel.location.latitude === 'undefined' ||
                typeof context.hotel.location.longitude === 'undefined') {
                return;
            }

            const latLng = {
                lat: +context.hotel.location.latitude,
                lng: +context.hotel.location.longitude
            };

            const map = new google.maps.Map(mapElement, {
                center: latLng,
                zoom: 13
            });
            map.setOptions({ styles: styleArray });

            const marker = new google.maps.Marker({
                position: latLng,
                map,
                title: context.hotel.name,
                icon: {
                    path: 'M12.3115 28.459C15.1504 24.9062 21.625 16.2956 21.625 11.459C21.625 5.5931 16.8659 0.833984 11 0.833984C5.13411 0.833984 0.375 5.5931 0.375 11.459C0.375 16.2956 6.84961 24.9062 9.68848 28.459C10.3691 29.3057 11.6309 29.3057 12.3115 28.459ZM11 7.91732C11.9393 7.91732 12.8401 8.29046 13.5043 8.95465C14.1685 9.61884 14.5417 10.5197 14.5417 11.459C14.5417 12.3983 14.1685 13.2991 13.5043 13.9633C12.8401 14.6275 11.9393 15.0007 11 15.0007C10.0607 15.0007 9.15985 14.6275 8.49566 13.9633C7.83147 13.2991 7.45833 12.3983 7.45833 11.459C7.45833 10.5197 7.83147 9.61884 8.49566 8.95465C9.15985 8.29046 10.0607 7.91732 11 7.91732Z',
                    fillColor: settings.site.siteName == "pricetravel" ? '#5C469C' : '#DC3004',
                    fillOpacity: 1,
                    strokeWeight: 0,
                    scale: 1,
                    anchor: new google.maps.Point(12, 24)
                }
            });

            const isMobile = fn.mobileAndTabletCheck();
            if (isMobile) {
                addLocationIcon(google.maps.ControlPosition.RIGHT_TOP, map);
            }

            const infoWindow = new google.maps.InfoWindow();

            marker.addListener('click', () => {
                $timeout(() => vm.showCardMap = true);
            });

            map.addListener('tilesloaded', () => {
                smoothZoom(map, 18, map.getZoom());
                google.maps.event.clearListeners(map, 'tilesloaded');
            });
        };

        const loadGoogleMapsAPI = () => {
            return new Promise((resolve, reject) => {
                if (typeof google !== 'undefined' && google.maps) {
                    resolve();
                } else {
                    if (document.querySelector('script[src*="maps.googleapis.com/maps/api/js"]')) {
                        resolve();
                    } else {
                        fn.getScript(config.endPoints.googleMapsApi, "", () => {
                            if (typeof google !== 'undefined' && google.maps) {
                                resolve();
                            } else {
                                reject(new Error('Failed to load Google Maps API'));
                            }
                        });
                    }
                }
            });
        };

        loadGoogleMapsAPI()
            .then(handleMap)
            .catch(() => {
            });
    };

    function addLocationIcon(container, map) {
        var locationIconContainer = document.createElement("div");

        locationIconContainer.style.position = "absolute";
        locationIconContainer.style.top = "60px";
        locationIconContainer.style.width = "40px";
        locationIconContainer.style.height = "40px";
        locationIconContainer.style.backgroundColor = "white";
        locationIconContainer.style.borderRadius = "50%";
        locationIconContainer.style.display = "flex";
        locationIconContainer.style.justifyContent = "center";
        locationIconContainer.style.alignItems = "center";
        locationIconContainer.style.left = "88vw";

        var locationIcon = document.createElement("img");
        locationIcon.style.width = "30px";
        locationIcon.style.height = "30px";
        locationIcon.style.cursor = "pointer";
        locationIcon.src = `${config.cloudCdn}/assets/img/current_position.svg`;

        locationIconContainer.appendChild(locationIcon);
        map.controls[container].push(locationIconContainer);

        locationIconContainer.addEventListener("click", function () {
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(function (position) {
                    var userLatLng = {
                        lat: position.coords.latitude,
                        lng: position.coords.longitude
                    };
                    new google.maps.Marker({
                        position: userLatLng,
                        map: map,
                        icon: {
                            url: `${config.cloudCdn}/assets/img/point.png`,
                            scaledSize: new google.maps.Size(25, 25)
                        }
                    });
                    map.setCenter(userLatLng);
                }, function (error) {
                    switch (error.code) {
                        case error.PERMISSION_DENIED:
                            console.log("El usuario denegó la solicitud de geolocalización.");
                            break;
                        case error.POSITION_UNAVAILABLE:
                            console.log("La información de ubicación no está disponible.");
                            break;
                        case error.TIMEOUT:
                            console.log("Se ha agotado el tiempo de espera para obtener la ubicación del usuario.");
                            break;
                        case error.UNKNOWN_ERROR:
                            console.log("Se produjo un error desconocido al obtener la ubicación del usuario.");
                            break;
                    }
                });
            } else {
                console.log("El navegador no soporta la geolocalización.");
            }
        });
    }

    vm.sendContentFooter = (title) => {
        trackerFooter.clickLink(`footer :: ${title}`);

    }

    vm.showMoreRooms = () => {
        _showMoreRooms = !_showMoreRooms;

        if (!_showMoreRooms) {
            vm.limitRoomsFilter = vm.roomsAvailables;
        } else {
            tracker.selectedFilter("Ver todas las habitaciones");
            vm.limitRoomsFilter = vm.roomsLimit;
        }

    }

    vm.sharedBy = (destino) => {
        let paramsSharedRequest = {
        }
        paramsSharedRequest.Key = "";
        paramsSharedRequest.Url = window.location.pathname + window.location.search + "&utm_source=" + (destino ? destino : "copy_link") + "&utm_medium=social&utm_campaign=share_hotel";
        $http.get(config.uriShared, { params: paramsSharedRequest }).then(function (response) { onSuccessSharedRequest(response, destino) }, onErrorShared);
    }

    vm.onSelectDate = (date) => {
        const { checkin, checkout } = date;
        vm.defaultHotel['checkIn'] = new Date(`${checkin}T00:00:00`);
        vm.defaultHotel['checkOut'] = new Date(`${checkout}T00:00:00`);
        paramsRoomRates.CampaignToken = _campaignToken;
        paramsRoomRates.userKey = _userKey;
        paramsRoomRates.mobile = isMobile;
        paramsRoomRates.login = _isLogin;
        paramsRoomRates.responseTimeout = 20000;
        paramsRoomRates.source = box.source ? box.source : "SPA-Hotel-List";
        paramsRoomRates.channelId = isMobile ? config.chkSourceMobile : config.chkSourceDesktop;
        paramsRoomRates.checkin = checkin;
        paramsRoomRates.checkout = checkout;
        box.checkIn = checkin;
        box.checkOut = checkout;

        vm.datesRecommended = [];
        const data = {
            paxes: newPax.paxes,
            checkIn: new Date(`${checkin}T00:00:00`),
            checkOut: new Date(`${checkout}T00:00:00`),
            ...paramsRoomRates
        }

        $rootScope.$broadcast(settings.bookerSearchEvent, data);

        setPickerDates(vm.defaultHotel.checkIn, vm.defaultHotel.checkOut);
    }
    vm.handleClick = function () {
        const dataMetric = { ...box, ...hotel, ..._paxes };
        if (typeof trackerMetric !== 'undefined' && typeof trackerMetric.metricDatesRecommended === 'function') {
            trackerMetric.metricDatesRecommended('click_suggested_dates', { data: dataMetric });
        }
        var roomsSection = document.getElementById('rooms');
        var roomsSectionPosition = roomsSection.getBoundingClientRect().top + window.pageYOffset;

        window.scrollTo({ top: roomsSectionPosition - 250, behavior: 'smooth' });
    };
    vm.isSubmitOrRAPD = (room, rate, roomIndex, meta = false) => {
        if (rate.bookNowPayLaterTimeLimit
            && rate.isBookNowPayLaterApplicable
            && !vm.isBlockRAPD
            && rate.collectType != 2) {
            vm.showModalRAPD('modal-rapd-mini', rate, room, roomIndex, meta);
        } else {
            vm.onSubmit(room, rate, roomIndex, false, false, true)
        }
    }
    vm.onSubmit = (room, rate, roomIndex, paynow = false, isRecommendation = false, typeMain = false) => {
        var paxesToSend = [];
        if (typeMain) {
            if (vm.onlyAdults) {
                newPax.paxesFormat.forEach((pax) => {
                    paxesToSend.push(pax.split('|')[0]);
                });
            } else {
                paxesToSend = newPax.paxesFormat;
            }

        } else if (isRecommendation || vm.isRecomendationCheckout) {
            vm.onSubmitLoadingRecomendation = true;
            vm.recomendationsList.checkoutHash.forEach((roomrate) => {
                paxesToSend.push(roomrate.paxFam);
            });
        } else {
            vm.onSubmitLoading = true;
            vm.stickyRates.forEach((roomrate) => {
                let paxFamParts = roomrate.paxFam.split('|');
                let multiplier = roomrate.selectedRoomValue;
                var totlaAdults = parseInt(paxFamParts) * multiplier;
                var totalChildres = "";
                if (paxFamParts[1] && paxFamParts[1].length > 0) {
                    totalChildres = Array.from({ length: multiplier }, () => paxFamParts[1]).flat().join(',');
                }
                let result = totalChildres && totalChildres != "" ? totlaAdults.toString() + '|' + totalChildres : totlaAdults.toString();
                paxesToSend.push(result);
            });
        }
        const paxesData = fn.mapPaxToUrlNew(paxesToSend, true, true);
        const valuesDefaults = getValuesDefaults();
        let items = [];
        if (typeMain) {
            items.push({
                idRate: rate.rateId,
                rateKey: rate.rateKey,
                idRoom: room.roomId,
                promotionValue: rate.promotionValue,
                totalRoomRatePerNight: rate.taxes.totalRoomRatePerNight,
                name: room.name,
                mealPlanCode: rate.mealPlanCode,
                totalAmount: rate.totalAmount,
                checkoutHash: rate.checkoutHash,
                isBookNowPayLater: paynow,
                rooms: vm.isRequote ? newPaxRequote.paxes.rooms : newPax.paxes.rooms,
                pax: rate.paxFam,
                index: roomIndex,
                provider: rate.provider
            });
        } else if (isRecommendation || vm.isRecomendationCheckout) {
            vm.recomendationsList.checkoutHash.forEach((roomrate, index) => {
                items.push(
                    {
                        idRate: roomrate.rateId,
                        rateKey: roomrate.rateKey,
                        idRoom: roomrate.roomId,
                        promotionValue: roomrate.promotionValue,
                        totalRoomRatePerNight: roomrate.taxes.totalRoomRatePerNight,
                        name: roomrate.name,
                        mealPlanCode: roomrate.mealPlanCode,
                        totalAmount: roomrate.totalAmount,
                        checkoutHash: roomrate.checkoutHash,
                        isBookNowPayLater: paynow,
                        rooms: 1,
                        pax: roomrate.paxFam,
                        index: index + 1,
                        provider: roomrate.provider
                    }
                );
            });
        } else {
            vm.stickyRates.forEach((roomrate, index) => {
                items.push(
                    {
                        idRate: roomrate.rateId,
                        rateKey: roomrate.rateKey,
                        idRoom: roomrate.roomId,
                        promotionValue: roomrate.promotionValue,
                        totalRoomRatePerNight: roomrate.taxes.totalRoomRatePerNight,
                        name: roomrate.name,
                        mealPlanCode: roomrate.mealPlanCode,
                        totalAmount: roomrate.totalAmount,
                        checkoutHash: roomrate.checkoutHash,
                        isBookNowPayLater: paynow,
                        rooms: roomrate.selectedRoomValue,
                        pax: roomrate.paxFam,
                        index: index + 1,
                        provider: roomrate.provider
                    }
                );
            });
        }
        let data = {
            idHotel: hotel.hotelId,
            hotelCheckIn: box.checkIn,
            CheckIn: box.checkIn,
            CheckOut: box.checkOut,
            Source: config.source,
            ChkSource: isMobile ? config.chkSourceMobile : config.chkSourceDesktop,
            ChkSourceGroup: isMobile ? config.chkSourceGroupMobile : config.chkSourceGroupDesktop,
            isBookNowPayLater: paynow,
            index: 1,
            pageUrl: window.location.pathname + window.location.search,
            isMetaSearchEngine: vm.source_origin !== "",
            Amount: typeMain ? rate.totalAmount : isRecommendation || vm.isRecomendationCheckout ? vm.recomendationsList.totalRoomRatePerNight : vm.totalSticky,
            useNewConfig: true,
            applicable: typeMain,
            promotions: paramsRoomRates.promotions
        };
        data = { ...data, ...paxesData, ...valuesDefaults, site: settings.site.siteName };
        var form = document.createElement("form");
        form.setAttribute("method", "POST");
        form.setAttribute("action", `/${cultureData.cultureCode}${config.checkoutUrl}`);
        StorageService.setSession(data);
        for (var key in data) {
            if (data[key] != null) {
                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", key);
                hiddenField.setAttribute("value", data[key]);
                form.appendChild(hiddenField);
            }
        }
        items.map((room, index) => {
            for (var key in room) {
                var hiddenField = document.createElement("input");
                hiddenField.setAttribute("type", "hidden");
                hiddenField.setAttribute("name", `roomList[${index}][${key}]`);
                hiddenField.setAttribute("value", room[key]);
                form.appendChild(hiddenField);
            }
        });
        const eventData = {
            ...data
        }
        tracker.setUserInteraction(paynow);
        tracker.setAddToCart(eventData, items);
        tracker.beginCheckOutTB(eventData, items);

        $("#loader-page").removeClass("d-none");
        document.body.appendChild(form);
        form.submit();
    }


    vm.getReviews = (propertyId, isReviews) => {
        vm.reviewsLoadingModal = true;
        let paramsReviewsRequest = {
        }
        paramsReviewsRequest.PropertyId = propertyId;
        paramsReviewsRequest.Language = config.cultureReviews;
        paramsReviewsRequest.IsReviews = isReviews;
        $http.get(config.expediaReviewsURL, { params: paramsReviewsRequest }).then(function (response) { onSuccessReviewsRequest(response) }, onErrorShared);
    }

    vm.getOverallText = (overall) => {
        return ln.rating_reviews[overall];
    }

    vm.getStart = (stars) => {
        var htmlStar = "";
        var limit = 5;

        for (var i = 0; i < limit; i++) {
            if (stars > i) {
                if ((stars - i) == 0.5) {
                    htmlStar += "<i class='bi bi-star-half'></i>";
                }
                else {
                    htmlStar += "<i class='bi bi-star-fill'></i>";
                }
            }
            else {
                htmlStar += "";
            }
        }

        return htmlStar;
    }
    vm.goSection = (section, gap = 45, timeOut = 100) => {
        var element = document.getElementById(`${section}`);
        if (element && element.scrollIntoView) {
            setTimeout(() => {
                element.scrollIntoView({ behavior: 'smooth' });
            }, timeOut)
        } else {
            $('html, body').scrollTop($(`#${section}`).offset().top - gap);
        }
    }

    vm.goSectCloseModal = (modal, section, gap = 45) => {
        $(`#${modal}`).modal("hide");
        vm.goSection(section)
    }

    vm.showSpecialPrice = (rate) => {
        if (!rate || Object.keys(rate).length === 0) {
            return false;
        }
        return (
            (rate.promotionValue && promotionsBlackList.includes(rate.promotionValue)) ||
            (promotionsBlackList.includes(rate.promotion) && (!rate.bookingDates && !rate.travelDates))
        );
    };
    vm.setLimitRoom = () => {

        if (vm.roomsAvailables && vm.roomsAvailables > 0) {
            const availableRooms = vm.roomsAvailables;
            vm.roomsAvailables = availableRooms;

            // Caso 1: Si hay menos de 5 habitaciones disponibles, mostrar solo las habitaciones disponibles.
            if (availableRooms < 5) {
                vm.showMoreRoomsButton = false; // No mostrar el botón "Ver más habitaciones".
                return availableRooms;
            }
            // Caso 2: Si hay 5 o más habitaciones disponibles, determinar si se expande o se contrae.
            if (availableRooms >= 5) {
                vm.showMoreRoomsButton = availableRooms > vm.limitRoomsFilter; // Mostrar el botón si hay más de 8 habitaciones disponibles.
                return Math.min(availableRooms, vm.limitRoomsFilter);
            }
        }

        // Caso 3: Si no hay ninguna habitación disponible, mostrar solo 1 habitación sin disponibilidad.
        vm.showMoreRoomsButton = false; // No mostrar el botón "Ver más habitaciones".
        return 1;
    }

    vm.getShowPromotion = (rate) => {
        var promotionsToShow = config.promotionKeyShow;
        if (promotionsToShow) {
            var promotionToFind = rate ? promotionsToShow.find(x => x.key == rate.promotionType) : "";
            if (promotionToFind) {
                return promotionToFind.blackFAndHotS;
            }
        }

        return false;
    }

    vm.onSelectRecommenderDate = (e, item) => {
        e.preventDefault();
        e.stopPropagation();

        tracker.selectDateSuggested(item);

        let urlarray = window.location.href.split('?');
        let query = fn.searchUri(urlarray[1]);
        query.checkin = item.CheckIn;
        query.checkout = item.CheckOut;
        let queryString = fn.objectToQueryParams(query);
        window.location.href = urlarray[0] + '?' + queryString;
    }

    vm.selectRateMobile = (rate, roomIndex, rateIndex) => {
        vm.rooms[roomIndex].rate.rate.forEach(function (rate) {
            rate.selected = false;
        });
        vm.rooms[roomIndex].rate.rate[rateIndex].selected = vm.rooms[roomIndex].rate.rate[rateIndex].selected ? true : !vm.rooms[roomIndex].rate.rate[rateIndex].selected;
        vm.rooms[roomIndex].roomRateMobileSelected = rate;
    }
    function loadRoomRatesTrivago() {
        if (_user && _user.firebaseId) {
            _campaignToken = config.campaignTokenUser;
        } else {
            initialCampaignToken();
        }
        vm.loading = true;
        vm.recomendationsList.loading = true;
        vm.hasCancellationFree = false;
        vm.hasPayLater = false;
        vm.roomsAvailables = 0;
        vm.recommendedDates = null;
        vm.checkIn = paramsRoomRates.checkin;
        vm.checkOut = paramsRoomRates.checkout;
        let paramnsRatesClone = { ...paramsRoomRates }
        paramnsRatesClone.rooms = getUserSelectionParams().paxesFormat;
        paramnsRatesClone.CampaignToken = _campaignToken;
        paramnsRatesClone.userKey = _userKey;
        paramnsRatesClone.culture = cultureData.internalCultureCode;
        paramnsRatesClone.mobile = isMobile;
        paramnsRatesClone.login = _isLogin;
        paramnsRatesClone.responseTimeout = 20000;
        paramnsRatesClone.source = box.source ? box.source : "SPA-Hotel-List";
        paramnsRatesClone.channelId = isMobile ? config.chkSourceMobile : config.chkSourceDesktop;


        $http.get(config.endPoints.detailQuoteUrl, { params: paramnsRatesClone, headers: getHeaders() }).then(onSuccessRoomRatesTrivago, (err) => {
            onError(err);
        });
    }
    function loadRoomRatesUnique() {
        if (_user && _user.firebaseId) {
            _campaignToken = config.campaignTokenUser;
        } else {
            initialCampaignToken();
        }
        vm.checkIn = paramsRoomRates.checkin;
        vm.checkOut = paramsRoomRates.checkout;
        let paramnsRatesClone = { ...paramsRoomRates }
        paramnsRatesClone.rooms = newPax.paxesFormat;
        paramnsRatesClone.CampaignToken = _campaignToken;
        paramnsRatesClone.userKey = _userKey;
        paramnsRatesClone.culture = cultureData.internalCultureCode;
        paramnsRatesClone.mobile = isMobile;
        paramnsRatesClone.login = _isLogin;
        paramnsRatesClone.isUnique = true;
        paramnsRatesClone.responseTimeout = 20000;
        paramnsRatesClone.source = box.source ? box.source : "SPA-Hotel-List";
        paramnsRatesClone.channelId = isMobile ? config.chkSourceMobile : config.chkSourceDesktop;


        $http.get(vm.appicableExperiment ? config.endPoints.detailQuoteUrlFam : config.endPoints.detailQuoteUrl, { params: paramnsRatesClone, headers: getHeaders() }).then(onSuccessRoomRatesUnique, (err) => {
            onError(err);
        });
    }
    function getUserSelectionParams() {
        let params = fn.search();

        let rooms = +params.rooms;
        let paxes = [];

        for (let i = 0; i < rooms; i++) {
            let index = i + 1;
            let pax = {
                adults: +(params[`room${index}.adults`] || 2),
                children: []
            }
            let children = params[`room${index}.agekids`];
            if (children) {
                let childrenSplit = children.split(",");

                for (var c = 0; c < childrenSplit.length; c++) {
                    pax.children.push({
                        year: +(childrenSplit[c]) || 0
                    })
                }
            }
            paxes.push(pax);
        }
        return getPaxesTrivago(paxes);
    }
    function getPaxesTrivago(box) {
        let boxLength = box && box.length;
        let paxes = {
            adults: 0,
            children: 0,
            paxesFormat: []
        };
        for (let i = 0; box && i < boxLength; i++) {
            const pax = box[i];
            let paxFormat = "";
            paxes.adults += pax.adults;
            paxFormat += `${pax.adults}`;

            if (pax.children && pax.children.length) {
                paxes.children += pax.children.length;
                paxFormat += `|${pax.children.map(it => it.year).join(',')}`;
            }

            paxes.paxesFormat.push(paxFormat);
        }
        return paxes;
    }
    //Get rates rooms
    function loadRoomRates() {

        if (_user && _user.firebaseId) {
            _campaignToken = config.campaignTokenUser;
        } else {
            initialCampaignToken();
        }

        vm.loading = true;
        vm.hasCancellationFree = false;
        vm.hasPayLater = false;
        vm.roomsAvailables = 0;
        vm.recommendedDates = null;
        vm.checkIn = paramsRoomRates.checkin;
        vm.checkOut = paramsRoomRates.checkout;

        paramsRoomRates.rooms = newPax.paxesFormat;
        paramsRoomRates.CampaignToken = _campaignToken;
        paramsRoomRates.userKey = _userKey;
        paramsRoomRates.mobile = isMobile;
        paramsRoomRates.login = _isLogin;
        paramsRoomRates.culture = cultureData.internalCultureCode;
        paramsRoomRates.responseTimeout = 20000;
        paramsRoomRates.source = box.source ? box.source : "SPA-Hotel-List";
        paramsRoomRates.channelId = isMobile ? config.chkSourceMobile : config.chkSourceDesktop;
        paramsRoomRates.isOnlyA = vm.onlyAdults;

        if(!!providers)
            paramsRoomRates.providers = providers;

        vm.datesRecommended = [];

        $http.get(vm.appicableExperiment ? config.endPoints.detailQuoteUrlFam : config.endPoints.detailQuoteUrl, { params: paramsRoomRates, headers: getHeaders() }).then(onLoadRatesRequote, (err) => {
            getAvailabilityReasons();
            onError(err);
        });
    }

    function loadRatesFromLogin() {

        if (!settings.rb) {
            let paramsLogin = { ...paramsRoomRates };
            paramsLogin.rooms = newPax.paxesFormat;
            paramsLogin.userKey = null;
            paramsLogin.CampaignToken = null;
            paramsLogin.login = !_isLogin;
            paramsLogin.channelId = isMobile ? (config.channelConfigDefault?.mobileLogin.channelId != 0 ? config.channelConfigDefault.mobileLogin.channelId : paramsLogin.channelId) : (config.channelConfigDefault?.desktopLogin.channelId != 0 ? config.channelConfigDefault.desktopLogin.channelId : paramsLogin.channelId);
            paramsLogin.isOnlyA = vm.onlyAdults;
            
            if(!!providers)
                paramsLogin.providers = providers;

            $http.get(vm.appicableExperiment ? config.endPoints.detailQuoteUrlFam : config.endPoints.detailQuoteUrl, { params: paramsLogin, headers: getHeaders() }).then(onLoadRatesFromLogin, (err) => { });
        }


    }
    //REVALIDATE
    function loadRevalidate(rate, pax, roomId) {
        paramnsRevalidate.rooms = pax;
        paramnsRevalidate.mobile = isMobile;
        paramnsRevalidate.responseTimeout = 20000;
        paramnsRevalidate.channelId = isMobile ? config.chkSourceMobile : config.chkSourceDesktop;
        paramnsRevalidate.roomId = roomId;
        paramnsRevalidate.isUnique = rate.provider == 109;
        paramnsRevalidate.CampaignToken = _campaignToken;
        $http.get(config.endPoints.revalidateUrl, { params: paramnsRevalidate, headers: getHeaders() }).then(function (params) {
            rate.loadingAvailibles = false;
            if (params.data.roomsAvailibles > 0) {
                rate.limitAvailibles = params.data.roomsAvailibles;
            } else {
                rate.limitAvailibles = 1;
            }

        }, (err) => {
            rate.loadingAvailibles = false;
            rate.limitAvailibles = 1;
        });
    }
    vm.toggleDropdown = (rate, room) => {
        rate.showDron = !rate.showDron;
        if (rate.showDron) {
            rate.loadingAvailibles = true;
            loadRevalidate(rate, rate.paxFam, room.roomId);
        }
    };
    vm.selectedRateRoom = (rate, selectedRoom, room) => {

        vm.hasErrorHandle = false;
        if (selectedRoom.value > 1 && rate.loadingAvailibles) {
            return;
        }
        rate.showDron = !rate.showDron;
        rate.selectedRoomValue = selectedRoom.value;
        if (selectedRoom.value !== 0) {
            const existingRateIndex = vm.stickyRates.findIndex(stickyRate => stickyRate.rateId === rate.rateId && stickyRate.paxFam === rate.paxFam);

            if (existingRateIndex !== -1) {
                // Si ya existe en vm.stickyRates, actualiza solo selectedRoomValue
                vm.stickyRates[existingRateIndex].selectedRoomValue = selectedRoom.value;
            } else {
                // Si no existe, agrega el objeto al array
                rate.name = room.name;
                rate.roomId = room.roomId;
                rate.img = room.picture.cloudUri;
                vm.stickyRates.push(rate);
            }
        } else {
            const index = vm.stickyRates.findIndex(stickyRate => stickyRate.rateId === rate.rateId && stickyRate.paxFam === rate.paxFam);
            if (index !== -1) {
                // Si existe y selectedRoom.value es 0, elimínalo de vm.stickyRates
                vm.stickyRates.splice(index, 1);
            }
        }
    };
    vm.incrementQuantity = (rate) => {
        if (rate.selectedRoomValue + 1 < rate.roomsSelectection) {
            const existingRateIndex = vm.stickyRates.findIndex(stickyRate => stickyRate.rateId === rate.rateId && stickyRate.paxFam === rate.paxFam);
            if (existingRateIndex !== -1) {
                vm.stickyRates[existingRateIndex].selectedRoomValue += 1;
            }
            return rate.selectedRoomValue += 1;
        }
    };
    vm.decrementQuantity = (rate, room) => {
        const existingRateIndex = vm.stickyRates.findIndex(stickyRate => stickyRate.rateId === rate.rateId && stickyRate.paxFam === rate.paxFam);
        if (rate.selectedRoomValue - 1 == 0) {
            if (existingRateIndex !== -1) {
                vm.stickyRates.splice(existingRateIndex, 1);
            }
            return rate.selectedRoomValue = 0;
        } else {
            if (existingRateIndex !== -1) {
                vm.stickyRates[existingRateIndex].selectedRoomValue -= 1;
            }
            return rate.selectedRoomValue -= 1;
        }

    };
    vm.setValueModalRoom = (roomSelect) => {
        vm.selectModalRoom = roomSelect;
        var valueToLoop = vm.assigRateSet.roomsSelectection
        valueToLoop.forEach(item => {
            item.active = false;
            if (item.value == vm.selectModalRoom) {
                item.active = true;
            }

        })
    };
    vm.deleteStickyDetail = (rate) => {
        const roomRateIndex = vm.stickyRates.findIndex(roomRate => roomRate.rateId === rate.rateId && roomRate.paxFam === rate.paxFam);
        if (roomRateIndex !== -1) {
            vm.stickyRates.splice(roomRateIndex, 1);
            const targetRoom = vm.rooms.find(r => r.roomId === rate.roomId);
            if (targetRoom && targetRoom.rate) {
                const roomRateIndexInTargetRoom = targetRoom.rate.rate.findIndex(roomRate => roomRate.rateId === rate.rateId && roomRate.paxFam === rate.paxFam);

                if (roomRateIndexInTargetRoom !== -1) {
                    targetRoom.rate.rate[roomRateIndexInTargetRoom].selectedRoomValue = 0;
                }
            }
        }
    };
    vm.selectedRateRoomModal = (rate, room = {}) => {
        let targetRoom;
        if (Object.keys(room).length === 0) {
            // Si room es un objeto vacío, busca la habitación directamente por rate.roomId
            targetRoom = vm.rooms.find(r => r.roomId === rate.roomId);
        } else {
            // Si room tiene valores, úsalo directamente
            targetRoom = room;
        }
        if (targetRoom && targetRoom.rate) {
            const roomRateIndex = targetRoom.rate.rate.findIndex(roomRate => roomRate.rateId === rate.rateId && roomRate.paxFam === rate.paxFam);

            if (roomRateIndex !== -1) {
                const selectedRoomValue = vm.selectModalRoom;
                const existingStickyRateIndex = vm.stickyRates.findIndex(stickyRate => stickyRate.rateId === rate.rateId && stickyRate.paxFam === rate.paxFam);

                targetRoom.rate.rate[roomRateIndex].selectedRoomValue = selectedRoomValue;

                if (existingStickyRateIndex !== -1) {
                    // Si ya existe en vm.stickyRates, actualiza solo selectedRoomValue
                    vm.stickyRates[existingStickyRateIndex].selectedRoomValue = selectedRoomValue;
                    if (vm.selectModalRoom === 0) {
                        const indexInStickyRates = vm.stickyRates.indexOf(targetRoom.rate.rate[roomRateIndex]);
                        if (indexInStickyRates !== -1) {
                            vm.stickyRates.splice(indexInStickyRates, 1);
                        }
                    }
                } else {
                    // Si no existe, agrega el objeto al array
                    if (selectedRoomValue !== 0) {
                        targetRoom.rate.rate[roomRateIndex].name = room.name;
                        targetRoom.rate.rate[roomRateIndex].roomId = room.roomId;
                        var clonedRate = angular.copy(targetRoom.rate.rate[roomRateIndex]);
                        vm.stickyRates.push(clonedRate);
                    }
                }
            }
        }
    };
    $scope.$watch('vm.stickyRates', function (newStickyRates, oldStickyRates) {
        if (!angular.equals(newStickyRates, oldStickyRates)) {
            updateStickyData(newStickyRates);
        }
    }, true);

    function updateStickyData(stickyRates) {
        vm.totalSticky = 0;
        vm.totalImpSticky = 0;
        vm.totalRoomSticky = 0;
        vm.totalPromoDiscount = 0;
        vm.totalTotalSticky = 0;
        vm.imgSticky = "";
        let checkIn = fn.getDate(paramsRoomRates.checkin);
        let checkOut = fn.getDate(paramsRoomRates.checkout);
        const nightCounts = fn.diffBetweenDays(checkIn, checkOut);
        let totalAdulst = 0;
        let totalChildrens = 0;
        vm.totalHasTaxSticky = false;
        vm.getRecomedationLabelSticky = "";
        vm.sumTotalSticky = 0;
        vm.applicableRAPDSticky = true;
        vm.applicableMonthsIntereses = true;
        vm.totalFeesIncludes = 0;
        vm.dateRAPDSticky = "";
        vm.isCancelation = false;
        vm.AgesMenors = "";
        vm.totalChildrens = 0;
        const childrenCount = {};
        for (const rate of stickyRates) {
            if (rate.selectedRoomValue) {

                vm.totalRoomSticky += rate.selectedRoomValue;
                vm.totalSticky += (rate.taxes.totalRoomRatePerNight) * rate.selectedRoomValue;
                vm.totalTotalSticky += rate.taxes.totalRoomRate * rate.selectedRoomValue;
                vm.totalHasTaxSticky = rate.taxes.hasTaxes;
                if (rate.taxes.totalTaxesPerRoomPerNight > 0) {
                    vm.totalImpSticky += rate.taxes.totalTaxes * rate.selectedRoomValue;

                }
                vm.imgSticky = rate.img;
                let feesAmountTotal = rate.taxes.feesAmountTotal;
                vm.totalFeesIncludes += feesAmountTotal;
                vm.isDiscount = rate.taxes.discount;
                vm.totalPromoDiscount += rate.taxes.totalRoomRateBeforePromoExclusive * rate.selectedRoomValue;
                const paxFamParts = rate.paxFam.split('|');
                const adults = paxFamParts[0] ? parseInt(paxFamParts[0]) : 0;
                const childs = paxFamParts[1] ? parseInt(paxFamParts[1].split(',').length) : 0;
                totalAdulst += adults * rate.selectedRoomValue;
                totalChildrens += childs * rate.selectedRoomValue;
                vm.AgesMenors += paxFamParts[1] ? vm.AgesMenors ? `,${paxFamParts[1]}` : paxFamParts[1] : "";

                const childrenAges = paxFamParts[1] ? paxFamParts[1].split(',').map(Number) : [];
                childrenAges.forEach(age => {
                    if (childrenCount.hasOwnProperty(age)) {
                        childrenCount[age] += rate.selectedRoomValue;
                    } else {
                        childrenCount[age] = rate.selectedRoomValue;
                    }
                });

                if (rate.collectType == 2) {
                    vm.applicableMonthsIntereses = false;
                }
                if (!(!vm.isBlockRAPD && rate.bookNowPayLaterTimeLimit && rate.isBookNowPayLaterApplicable && rate.collectType != 2)) {
                    vm.applicableRAPDSticky = false;
                } else {
                    vm.dateRAPDSticky = rate.bookNowPayLaterTimeLimit;
                    vm.isCancelation = !rate.isNonRefundable && rate.cancellationPolicies.freeCancellationExpire;
                }
            }
        }

        const nightTitle = nightCounts == 1 ? lang.night : lang.nights;
        const adultsTitle = totalAdulst == 1 ? lang.adult : lang.adults;
        const childrenPart = totalChildrens > 0
            ? `, ${totalChildrens} ${totalChildrens == 1 ? lang.child : lang.children}`
            : '';
        const agesPart = vm.AgesMenors.split(',').length == 1 ? vm.AgesMenors.split(',') == 1 ? lang.year : lang.years : lang.years;
        vm.sumTotalSticky = totalAdulst + totalChildrens;
        const children = totalChildrens > 0 && totalChildrens > 2 ? `${childrenPart}` : totalChildrens > 0 ? `${childrenPart} (${vm.AgesMenors} ${agesPart})` : "";
        vm.getRecomedationLabelSticky = `${vm.totalRoomSticky} ${vm.totalRoomSticky > 1 ? lang.rooms : lang.room}, ${vm.getNights()} ${nightTitle} <br>${lang.for} ${totalAdulst} ${adultsTitle}${children}`;
        //compara huespedes
        // Mensaje que indica la selección y compara con la búsqueda
        const totalInSearch = newPax.paxes.adults + newPax.paxes.children.length;
        const totalSelected = totalAdulst + totalChildrens;
        vm.totalChildrens = totalChildrens;
        // Generación del mensaje final
        vm.isIncompletePax = totalSelected < totalInSearch ? formatString(lang.yourSelectionIncludes, totalSelected, totalSelected == 1 ? lang.person : lang.persons, totalInSearch) : "";
        const ageDescriptions = Object.entries(childrenCount)
            .map(([age, count]) => `${count} ${count > 1 ? lang.children : lang.child} ${lang.the} ${age} ${age != 1 ? lang.years : lang.year}`)
            .join(', <br>');
        vm.overViewMinors = ageDescriptions;
        //Procesar totalSelections y roomsSelectection solo si stickyRates tiene datos
        if (vm.stickyRates && vm.stickyRates.length > 0) {
            let totalSelections = 0;
            let totalWhatSelected = 0;
            for (const room of vm.rooms) {
                for (const rate of room.rate.rate) {
                    totalSelections += rate.selectedRoomValue || 0;
                    totalWhatSelected += rate.selectedRoomValue && rate.selectedRoomValue != 0 ? 1 : 0;
                }
            }
            const remainingSelections = 8 - totalSelections;
            for (const room of vm.rooms) {
                room.rate.rate.forEach((rate, index) => {
                    rate.roomsSelectection = Array.from({ length: rate.selectedRoomValue > 0 ? totalWhatSelected == 1 ? 9 : rate.selectedRoomValue + 1 + remainingSelections : remainingSelections + 1 }, (_, index) => ({
                        value: index,
                        selected: index <= room.selectedRoomValue,
                        title: index <= 1 ? lang.room : lang.rooms,
                        total: 0,
                        selectable: index > room.selectedRoomValue || room.selectedRoomValue === 0
                    }));
                });
            }
        } else {
            getRestMobile(false);
            for (const room of vm.rooms) {
                room.rate?.rate?.forEach((rate, index) => {
                    rate.roomsSelectection = Array.from({ length: 9 }, (_, index) => ({
                        value: index,
                        selected: true,
                        title: index <= 1 ? lang.room : lang.rooms,
                        total: 0
                    }));
                });
            }
        }

    }

    $scope.$watch('vm.stickyRates', function (newStickyRates, oldStickyRates) {
        if (!angular.equals(newStickyRates, oldStickyRates)) {
            updateStickyData(newStickyRates);
        }
    }, true);

    vm.cloneRate = function (rate) {
        return angular.copy(rate);
    };


    function onLoadRatesRequote(value) {
        let dataRates = value.data.rooms;
        let newPax = matchFamilyRule(box.pax, true, true);

        vm.isRequote = false;
        setInfoRooms(paramsRoomRates, newPax);

        if (value.data.rooms.length === 0 && !vm.appicableExperiment) {
            newPax = matchFamilyRule(box.pax, true, true);
            let copia_paramsRoomRates = fn.clone(paramsRoomRates);
            copia_paramsRoomRates.rooms = newPax.paxesFormat;
            setInfoRooms(copia_paramsRoomRates, newPax);
            $http.get(vm.appicableExperiment ? config.endPoints.detailQuoteUrlFam : config.endPoints.detailQuoteUrl, { params: copia_paramsRoomRates, headers: getHeaders() }).then(onSuccessGetRatesRequote, onError);
        } else if (value.data.rooms.length === 0 && vm.appicableExperiment) {
            onSuccessGetRatesRequote(value);

        } else {
            vm.isRequote = false;
            onSuccessRoomRates(value);
        }
    }

    function onSuccessDateRecommended(value) {
        const dataMetric = { ...box, ...hotel, ..._paxes };
        trackerMetric.metricDatesRecommended('show_suggested_dates', { data: dataMetric });
        const language = config.culture.split('-')[0];
        const { data } = value;
        data.forEach(reservation => {
            const formattedCheckin = formatDate(reservation.checkin, language);
            const formattedCheckout = formatDate(reservation.checkout, language);
            const dateDifference = calculateDateDifference(reservation.checkin, reservation.checkout);

            reservation.checkinDayOfWeek = formattedCheckin.dayOfWeek;
            reservation.checkinDayAndMonth = formattedCheckin.dayAndMonth;
            reservation.checkoutDayOfWeek = formattedCheckout.dayOfWeek;
            reservation.checkoutDayAndMonth = formattedCheckout.dayAndMonth;
            reservation.dateDifference = dateDifference;
        });
        vm.datesRecommended = data;
    }

    function onLoadRatesFromLogin(value) {
        roomsLogin = value.data.rooms;

        if (vm.rooms.length) {
            mergeNormalAndLoginRates();
        }

    }

    function mergeNormalAndLoginRates() {

        let factor = _isLogin ? -1 : 1;
        for (let x = 0; x < vm.rooms.length; x++) {
            let room = vm.rooms[x];
            let newRoom = roomsLogin.find(rml => "" + rml.roomId === "" + room.roomId);
            if (newRoom && room.rate) {
                for (let y = 0; y < room.rate.rate.length; y++) {
                    let newrate;
                    let rate = room.rate.rate[y];
                    newrate = newRoom.rate.find(nr => nr.paxFam == rate.paxFam && nr.mealPlanCode == rate.mealPlanCode);
                    if (newrate) {
                        let saved = (rate.taxes.totalRoomRate - newrate.taxes.totalRoomRate) * factor;
                        let discountPercentage = ((saved * 100) / rate.taxes.totalRoomRate);
                        let isValid = discountPercentage > 2 && saved >= fn.thresHold(config.code);
                        if (isValid && !vm.notMessage) {
                            rate.total_saved = saved;
                        }
                    }
                }
            }

        }
    }

    function onSuccessGetRatesRequote(valueRequote) {
        var { rooms } = valueRequote.data;

        let newPax2 = matchFamilyRule(box.pax, true, true);
        newPax2.paxes.rooms = newPax2.paxesFormat.length;
        valueRequote.data.isRequote = true;
        vm.isRequote = true;
        newPaxRequote = newPax2;

        if (!rooms.length) {

            const date = fn.getDate(paramsRoomRates.checkin);
            const params = {
                site: paramsRoomRates.site,
                mobile: paramsRoomRates.mobile,
                hotelId: paramsRoomRates.hotelid,
                year: date.getFullYear(),
                month: date.getMonth() + 1,
                type: 'json',
                channelId: isMobile ? config.chkSourceMobile : config.chkSourceDesktop,
                rooms: vm.appicableExperiment ? newPax.paxesFormat : newPax2.paxesFormat,
                checkin: paramsRoomRates.checkin,
                checkout: paramsRoomRates.checkout,
                userKey: _userKey,
                login: _isLogin,
                source: box.source ? box.source : "SPA-Hotel-List",
                applicable: vm.appicableExperiment
            }
            $http.get(vm.appicableExperiment ? config.endPoints.dateRecommended : config.endPoints.dateRecommended, { params: params, headers: getHeaders() }).then(onSuccessDateRecommended, (err) => { });
        }
        onSuccessRoomRates(valueRequote);

    }

    function setInfoRooms(paramsRoomRates, paxes) {

        let checkIn = fn.getDate(paramsRoomRates.checkin);
        let checkOut = fn.getDate(paramsRoomRates.checkout);
        let nights = fn.diffBetweenDays(checkIn, checkOut);
        let rooms = paramsRoomRates.rooms.length;
        let totalPax = paxes.adults + paxes.children;

        vm.boxData = { rooms, nights, totalPax };
    }


    //Get Modal Payments
    function onSearchPaymentMethods() {
        if (vm.responsePayment.fixedPayments.length || vm.responsePayment.monthInterestFree.length) { return; }
        let params = {
            channel: config.channel,
            language: cultureData.internalCultureCode,
            currency: cultureData.currency
        }

        $http.get(config.endPoints.paymentMethodUrl, {
            params: params
        }).then(onSuccessPaymentMethods)

    }

    //Get skillBase
    function getSkillBase() {
        paramsSkillBase.adultQuantity = newPax.adults;
        paramsSkillBase.kidQuantity = newPax.children;
        paramsSkillBase.checkInDate = params.checkIn;
        paramsSkillBase.checkOutDate = params.checkOut;
        $http.get(config.endPoints.skillBaseUrl, { params: paramsSkillBase }).then(onSuccessSkillBase);
    }

    //Get availability reasons
    function getAvailabilityReasons() {

        const query = {
            rid: getRommsIds(),
            checkin: params.checkIn,
            checkout: params.checkOut,
            p: getPaxToMessagesApi()
        };

        $http.get(config.endPoints.availabilityReasonsUrl, { params: query }).then(onSuccessAvailabilityReasons, onErrorAvailabilityReason);
    }
    vm.onBlur = function (rate) {
        $timeout(function () {
            rate.showDron = false;
        });
    };


    //Classifications
    function onSuccessClasifications({ data }) {
        if (!data.length) {
            vm.filteredImages = buildGridGallery(vm.gallery);
            return;
        }

        vm.clasifications = data.map(clasification => ({
            ...clasification,
            isSelected: clasification.name === 'all'
        }));

        vm.filteredImages = buildGridGallery(vm.clasifications[0].images);
        if (vm.clasifications && vm.clasifications.length > 0) {
            vm.selectedClasificationName = vm.clasifications[0].name;
            vm.selectedClasification(vm.clasifications[0], 0);

            if (vm.clasifications.length == 1) {
                trackCategory.sendEventTracker('Hotel Sin Categorias en Galeria', `fotos:${vm.clasifications[0].images.length}`)
            }
        }
    }


    vm.openSlider = (modal, selected, galleryIndex) => {
        vm.showModal(modal);
        vm.currentSlideIndex = selected + 1;
        let element = `${sliderClass}_${galleryIndex}`;
        if (!memorySlider[element]) {
            $(element).slick({
                lazyLoad: 'progressive',
                slickSetOption: true,
                initialSlide: selected,
                arrows: true,
                autoplay: false,
                responsive: [
                    {
                        breakpoint: 768,
                        settings: {
                            arrows: false,
                        }
                    }
                ]
            });
            memorySlider[element] = $(element);
        }
        memorySlider[element].slick('slickGoTo', selected);
        vm.currentSlideTitle = "";
        $(element).on('afterChange', function (event, slick, currentSlide) {
            const currentSlideElement = slick.$slides.get(currentSlide);
            const figureElement = currentSlideElement.querySelector('figure.slider-gallery-item');
            const prevButton = $(element).find('.btnIcon--left');

            if (currentSlide === 0) {
                prevButton.addClass('hidden-c');
            } else {
                prevButton.removeClass('hidden-c');
            }
            let title = "";
            if (figureElement) {
                title = figureElement.getAttribute('data-titles');
            }
            $scope.$applyAsync(() => {
                vm.currentSlideIndex = currentSlide + 1;
                vm.currentSlideTitle = title;
            });
        });

    }


    vm.selectClasification = (selectClasification) => {
        vm.selectedClasificationName = selectClasification;
        vm.isFilterMenuOpen = false;
        const index = vm.clasifications.findIndex(clasification => clasification.name === selectClasification);
        const clasification = vm.clasifications.find(clasification => clasification.name === selectClasification);

        if (index !== -1) {
            vm.clasificationIndex = index;
        }
        vm.selectedClasification(clasification, index);
    };

    vm.getDisplayClasificationName = () => {
        return vm.selectedClasificationName === 'all'
            ? vm.allImagesText
            : vm.selectedClasificationName;
    };

    vm.selectedClasification = (data, index) => {
        vm.clasifications = vm.clasifications.map(clasification => ({
            ...clasification,
            isSelected: clasification.name === data.name,
            loaded: false
        }));

        const foundClasification = vm.clasifications.find(f => f.name == data.name);
        vm.filteredImages = buildGridGallery(foundClasification.images);
        vm.clasificationIndex = index;
        vm.selectedClasificationName = foundClasification.name;
    }

    vm.getClasificationByName = (selectedName) => {
        return vm.clasifications.find(c => {
            return c.name === selectedName;
        });
    };


    vm.toggleFilterMenu = () => {
        vm.isFilterMenuOpen = !vm.isFilterMenuOpen;
    };
    vm.validPaxCompleted = () => {
        vm.validPax = vm.rooms.some(room => {
            if (room && !room.rate?.disabled) {
                return room.rate?.rate?.every(rateItem => {
                    const paxFamDistributions = rateItem.paxFam;
                    const [adults, children] = paxFamDistributions.split('|');
                    return !(adults == newPax.adults && (children ? children.split(",").length : 0) == newPax.children);
                });
            }
            return false;
        });
    };

    function hasMeaningfulContent(html) {
        if (!html) return false;

        var tempDiv = document.createElement('div');
        tempDiv.innerHTML = html;

        var text = tempDiv.textContent || tempDiv.innerText || '';

        return text.trim().length > 0;
    }
    function onSuccessRoomRatesUnique({ data }) {
        const gallery = hotel.gallery.find(pic => pic && pic.cloudUri && pic.cloudUri.includes(".jpg")) || {};
        const roomsLenght = hotel.rooms.length;
        let firstRoom = [];
        if (data.rooms && data.rooms.length > 0) {
            // Guardar la primera habitación
            firstRoom = [data.rooms[0]];
            const responseRoomIds = new Set(firstRoom.map(room => room.roomId));
            const processedRoomIdsTable = new Set();
            const processedRoomIdsTableDuplicate = new Set();
            let notAvailableImg = false;
            for (let i = 0; i < roomsLenght; i++) {
                const room = hotel.rooms[i];
                if (responseRoomIds.has(room.roomId.toString())) {
                    if (processedRoomIdsTable.has(room.roomId)) {
                        processedRoomIdsTableDuplicate.add(room.roomId);
                        roomRate = {
                            rate: [],
                            disabled: true
                        }
                        room.isRateSelectedMobile = false;
                        room.roomRateMobileSelected = {};
                        continue;
                    }
                    processedRoomIdsTable.add(room.roomId);

                    const roomImage = room.pictures ? room.pictures.find(pic => pic && pic.cloudUri && pic.cloudUri.length) || {} : {};
                    let roomRate = fn.findObject(firstRoom, 'roomId', room.roomId);

                    room.instructions = "";
                    room.capacityList = Array.from(Array(room.capacity).keys());
                    if (!notAvailableImg) {
                        notAvailableImg = !roomImage.cloudUri;
                    }
                    room.picture = room.picture || {};
                    room.picture.cloudUri = roomImage.cloudUri || gallery.cloudUri || "";
                    room.galleryPosition = 1;

                    if (!roomRate) {
                        roomRate = {
                            rate: [],
                            disabled: true
                        }
                        room.isRateSelectedMobile = false;
                        room.roomRateMobileSelected = {};
                    } else {
                        vm.roomsAvailables += 1;
                        room.instructions = roomRate.rate[0].externalSalesAdvisory || "";
                        roomRate.rate[0].selected = true;
                        initPaymentNow(roomRate);
                        let providerdot = setProviderIdentifier(roomRate.rate[0]);
                        room.expedia = providerdot.expedia;
                        room.derbysoft = providerdot.derbysoft;
                        room.hotelbeds = providerdot.hotelbeds;
                        room.synxis = providerdot.synxis;
                        room.unique = providerdot.unique;
                        room.isRateSelectedMobile = true;
                        room.limitRoom = vm.ratesLimit;
                        for (const roomRates of roomRate.rate) {
                            roomRates.selectedRoomValue = 0;
                            roomRates.roomsSelectection = Array.from({ length: 9 }, (_, index) => ({
                                value: index,
                                selected: true,
                                title: index <= 1 ? lang.room : lang.rooms,
                                total: 0
                            }));
                        }
                        room.showMoreRate = false;
                        room.roomRateMobileSelected = roomRate.rate[0]
                    }
                    for (const roomRateItem of roomRate.rate) {

                        roomRateItem.taxes.feesAmountTotal = config.feesIncludes ? roomRateItem.taxes?.breakdown?.breakDowns
                            .filter(fee => config.breakDownExcludedList.includes(fee.title))
                            .reduce((acc, fee) => acc + fee.amount, 0) : 0;
                        roomRateItem.taxes.feesAmountTotalForNight = config.feesIncludes ? roomRateItem.taxes.feesAmountTotal / vm.getNights() : 0;

                    }
                    room.rate = roomRate;

                    room.limitOriginal = room.rate.rate.length;
                }
            }
            const [nuevasHabitaciones, otrasHabitaciones] = hotel.rooms.reduce((acc, room) => {
                responseRoomIds.has(room.roomId.toString()) ? acc[0].push(room) : acc[1].push(room);
                return acc;
            }, [[], []]);

            let primerRateExistente = null;
            let paxFamExistente = null;
            let mealPlanExistente = null;

            // Primero, obtener el primer rate de las habitaciones existentes (no únicas)
            for (const room of otrasHabitaciones) {
                if (room.rate && room.rate.rate && room.rate.rate.length > 0 && !room.rate.disabled) {
                    const tarifasValidas = room.rate.rate.filter(rate =>
                        rate.taxes && typeof rate.taxes.totalRoomRate === 'number'
                    );
                    if (tarifasValidas.length > 0) {
                        primerRateExistente = tarifasValidas[0].taxes.totalRoomRate;
                        paxFamExistente = tarifasValidas[0].paxFam;
                        mealPlanExistente = tarifasValidas[0].mealPlanCode;
                        break; // Tomar el primer rate de habitación existente
                    }
                }
            }

            let tieneHabitacionUnica = false;

            // Ahora buscar en las habitaciones únicas un rate con el mismo paxFam y mealPlanCode
            if (primerRateExistente !== null && paxFamExistente !== null && mealPlanExistente !== null) {
                for (const room of nuevasHabitaciones) {
                    if (room.rate && room.rate.rate && room.rate.rate.length > 0 && !room.rate.disabled) {
                        // Buscar un rate con el mismo paxFam y mealPlanCode en la habitación única
                        const rateConMismasCaracteristicas = room.rate.rate.find(rate =>
                            rate.paxFam === paxFamExistente &&
                            rate.mealPlanCode === mealPlanExistente &&
                            rate.taxes &&
                            typeof rate.taxes.totalRoomRate === 'number'
                        );

                        if (rateConMismasCaracteristicas) {
                            const rateHabitacionUnica = rateConMismasCaracteristicas.taxes.totalRoomRate;

                            // Comparar: rate habitación única < rate habitación existente (mismo paxFam y mealPlan)
                            if (rateHabitacionUnica < primerRateExistente) {
                                tieneHabitacionUnica = true;
                            }
                            break; // Solo necesitamos encontrar una coincidencia
                        }
                    }
                }
            }

            if (tieneHabitacionUnica) {
                const habitacionesOrdenadas = [...nuevasHabitaciones, ...otrasHabitaciones];
                hotel.rooms = habitacionesOrdenadas;
                vm.rooms = [...habitacionesOrdenadas];
                //tracker.setHotelUniqueRooms();
            } else {
                vm.roomsAvailables -= 1;
                //tracker.setHotelNotUniqueRooms();
            }
        }
    }
    function onSuccessRoomRatesTrivago({ data }) {
        vm.recomendationsTrivago = data.rooms || vm.recomendationsTrivago;
        const gallery = hotel.gallery.find(pic => pic && pic.cloudUri && pic.cloudUri.includes(".jpg")) || {};
        const roomsLength = hotel.rooms.length;
        const recomendationsTrivagoCopy = [];
        const processedRoomIds = new Set();
        for (let i = 0; i < roomsLength; i++) {
            const room = hotel.rooms[i];
            if (processedRoomIds.has(room.roomId)) {
                continue;
            }
            processedRoomIds.add(room.roomId);
            const roomRate = fn.findObject(vm.recomendationsTrivago, 'roomId', room.roomId);
            if (roomRate) {
                const copiedRoom = angular.copy(room);
                const roomImage = copiedRoom.pictures ? copiedRoom.pictures.find(pic => pic && pic.cloudUri && pic.cloudUri.length) || {} : {};
                copiedRoom.instructions = "";
                copiedRoom.capacityList = Array.from({ length: copiedRoom.capacity }, (_, index) => index);
                copiedRoom.picture = copiedRoom.picture || {};
                copiedRoom.picture.cloudUri = isMobile ? roomImage.cloudUri || "" : roomImage.cloudUri || gallery.cloudUri || "";
                for (const roomRateItem of roomRate.rate) {

                    roomRateItem.taxes.feesAmountTotal = config.feesIncludes ? roomRateItem.taxes?.breakdown?.breakDowns
                        .filter(fee => config.breakDownExcludedList.includes(fee.title))
                        .reduce((acc, fee) => acc + fee.amount, 0) : 0;
                    roomRateItem.taxes.feesAmountTotalForNight = config.feesIncludes ? roomRateItem.taxes.feesAmountTotal / vm.getNights() : 0;
                }
                copiedRoom.rate = roomRate;
                recomendationsTrivagoCopy.push(copiedRoom);
            }
        }
        vm.recomendationsTrivago = recomendationsTrivagoCopy;
        vm.recomendationsTrivago.loading = false;
        let trivagoUrl = _paramQuery.room_search || null;
        if (vm.recomendationsTrivago.length > 0 && trivagoUrl && vm.isTrivagoRate) {
            let rateObject = trivagoUrl.split('|').length < 2 ? trivagoUrl.split('%') : trivagoUrl.split('|');
            let indexRoom = vm.recomendationsTrivago.findIndex(x => x.roomId == rateObject[0]);
            if (indexRoom == -1) {
                vm.isTrivagoRate = false;
            } else {
                let indexRate = indexRoom != -1 ? vm.recomendationsTrivago[indexRoom].rate.rate.findIndex(x => x.rateId == rateObject[1]) : null;
                if (indexRate == -1 || indexRate == null) {
                    vm.isTrivagoRate = false;
                } else {
                    var tempRate = JSON.stringify(vm.recomendationsTrivago);
                    tempRate = JSON.parse(tempRate);
                    tempRate = tempRate[indexRoom];
                    tempRate.rate.rate = tempRate.rate.rate.filter(x => x.rateId == rateObject[1]);
                    tempRate.rate.rate.selectedRoomValue = 1;
                    vm.featuredRoom = tempRate;
                    setTimeout(function () {
                        vm.goSection('roomSection', 320)
                    }, 500);
                }
            }
        }
    }
    //Get rates handler
    function onSuccessRoomRates({ data }) {
        vm.recomendationsList = data.recomendations || vm.recomendationsList;
        const imageExtensions = [".jpg", ".jpeg", ".png", ".gif", ".bmp", ".webp", ".svg"];
        const gallery = hotel.gallery.find(pic =>
            pic && pic.cloudUri && imageExtensions.some(ext => pic.cloudUri.toLowerCase().endsWith(ext))
        ) || {};
        const roomsLength = hotel.rooms.length;
        const recomendationsListCopy = [];
        const processedRoomIds = new Set();
        for (let i = 0; i < roomsLength; i++) {
            const room = hotel.rooms[i];
            if (processedRoomIds.has(room.roomId)) {
                continue;
            }
            processedRoomIds.add(room.roomId);
            const roomRate = fn.findObject(vm.recomendationsList.rooms, 'roomId', room.roomId);
            if (roomRate) {
                const copiedRoom = angular.copy(room);
                const roomImage = copiedRoom.pictures ? copiedRoom.pictures.find(pic => pic && pic.cloudUri && pic.cloudUri.length) || {} : {};
                copiedRoom.instructions = "";
                copiedRoom.capacityList = Array.from({ length: copiedRoom.capacity }, (_, index) => index);
                copiedRoom.picture = copiedRoom.picture || {};
                copiedRoom.picture.cloudUri = isMobile ? roomImage.cloudUri || "" : roomImage.cloudUri || gallery.cloudUri || "";
                roomRate.rate.taxes.feesAmountTotal = 0;
                roomRate.rate.taxes.feesAmountTotal += config.feesIncludes ? roomRate.rate.taxes?.breakdown?.breakDowns
                    .filter(fee => config.breakDownExcludedList.includes(fee.title))
                    .reduce((acc, fee) => acc + fee.amount, 0) : 0;
                roomRate.rate.taxes.feesAmountTotalForNight = config.feesIncludes ? roomRate.rate.taxes.feesAmountTotal / vm.getNights() : 0;
                copiedRoom.rate = roomRate;
                recomendationsListCopy.push(copiedRoom);
            }
        }

        vm.recomendationsList.rooms = recomendationsListCopy;
        vm.recomendationsList.feesAmountTotal = 0;
        if (vm.recomendationsList.rooms && vm.recomendationsList.rooms.length > 0) {
            vm.recomendationsList.feesAmountTotal = config.feesIncludes ? vm.recomendationsList.breakdown?.breakDowns
                .filter(fee => config.breakDownExcludedList.includes(fee.title))
                .reduce((acc, fee) => acc + fee.amount, 0) : 0;
        }


        const allConditionsMet = Object.values(vm.recomendationsList.rooms).every(room => {
            const rate = room.rate.rate;
            if (rate && rate.bookNowPayLaterTimeLimit && rate.isBookNowPayLaterApplicable && !vm.isBlockRAPD && rate.collectType != 2) {
                return true; // La condición se cumple
            } else {
                return false; // La condición no se cumple
            }
        });
        vm.isRAPDRecomendation = allConditionsMet
        getAvailabilityReasons();
        vm.loading = false;
        vm.recomendationsList.loading = false;
        vm.ratesResponse = data;
        vm.cheaperRoomRate = getCheaperRoomRate(vm.ratesResponse.rooms);
        vm.RateForRAPD = vm.cheaperRoomRate;
        vm.roomsForRAPD = vm.cheaperRoomRate;
        const roomsLenght = hotel.rooms.length;
        const mealPlanCodes = new Set();
        let notAvailableImg = false;
        const processedRoomIdsTable = new Set();
        const processedRoomIdsTableDuplicate = new Set();
        for (let i = 0; i < roomsLenght; i++) {
            const room = hotel.rooms[i];
            if (processedRoomIdsTable.has(room.roomId)) {
                processedRoomIdsTableDuplicate.add(room.roomId);
                roomRate = {
                    rate: [],
                    disabled: true
                }
                room.isRateSelectedMobile = false;
                room.roomRateMobileSelected = {};
                continue;
            }
            processedRoomIdsTable.add(room.roomId);

            const roomImage = room.pictures ? room.pictures.find(pic => pic && pic.cloudUri && pic.cloudUri.length) || {} : {};
            let roomRate = fn.findObject(vm.ratesResponse.rooms, 'roomId', room.roomId);

            room.instructions = "";
            room.capacityList = Array.from(Array(room.capacity).keys());
            // Only update notAvailableImg if it is still false
            if (!notAvailableImg) {
                notAvailableImg = !roomImage.cloudUri;
            }
            room.picture = room.picture || {};
            room.picture.cloudUri = roomImage.cloudUri || gallery.cloudUri || "";
            room.galleryPosition = 1;

            if (!roomRate) {
                roomRate = {
                    rate: [],
                    disabled: true
                }
                room.isRateSelectedMobile = false;
                room.roomRateMobileSelected = {};
            } else {
                room.recommended = roomRate.recommended;
                room.recommendedRate = roomRate.recommendedRate;
                room.recommendedRates = roomRate.recommendedRates;
                vm.roomsAvailables += 1;
                room.instructions = roomRate.rate[0].externalSalesAdvisory || "";
                roomRate.rate[0].selected = true;
                initPaymentNow(roomRate);
                let providerdot = setProviderIdentifier(roomRate.rate[0]);
                room.expedia = providerdot.expedia;
                room.derbysoft = providerdot.derbysoft;
                room.hotelbeds = providerdot.hotelbeds;
                room.synxis = providerdot.synxis;
                room.isRateSelectedMobile = true;
                room.limitRoom = vm.ratesLimit;
                for (const roomRates of roomRate.rate) {
                    roomRates.selectedRoomValue = 0;
                    roomRates.roomsSelectection = Array.from({ length: 9 }, (_, index) => ({
                        value: index,
                        selected: true,
                        title: index <= 1 ? lang.room : lang.rooms,
                        total: 0
                    }));
                }
                room.showMoreRate = false;
                room.roomRateMobileSelected = roomRate.rate[0]
            }
            room.rate = roomRate;

            for (const roomRateItem of roomRate.rate) {

                roomRateItem.taxes.feesAmountTotal = config.feesIncludes ? roomRateItem.taxes?.breakdown?.breakDowns
                    .filter(fee => config.breakDownExcludedList.includes(fee.title))
                    .reduce((acc, fee) => acc + fee.amount, 0) : 0;
                roomRateItem.taxes.feesAmountTotalForNight = config.feesIncludes ? roomRateItem.taxes.feesAmountTotal / vm.getNights() : 0;
                mealPlanCodes.add(roomRateItem.mealPlanCode);

            }

            room.limitOriginal = room.rate.rate.length;
        }

        vm.isMsiAvailable = !(data.isMsiAvailable && data.isMsiAvailableMonths.length > 0);
        vm.msiMonthsAllow = data.isMsiAvailableMonths;
        if (vm.isGoSection) {
            setTimeout(function () {
                vm.goSection('recomendations', 350)
            }, 500);
        }

        const tieneCoincidenciaPaxFam = (room) => {
            return room.rate?.rate?.some(r => r.paxFam == newPax.paxesFormat[0]);
        };

        // Obtener las habitaciones del hotel
        let habitacionesFiltradas = { ...hotel };

        // Separar habitaciones con coincidencia y sin coincidencia
        let habitacionesConCoincidencia = habitacionesFiltradas.rooms.filter(tieneCoincidenciaPaxFam);
        let habitacionesSinCoincidencia = habitacionesFiltradas.rooms.filter(room => !tieneCoincidenciaPaxFam(room));
        // Ordenar habitaciones con coincidencia por `lessRate`
        habitacionesConCoincidencia.sort(ordenarPorLessRate);
        // Ordenar habitaciones sin coincidencia por `lessRate`
        habitacionesSinCoincidencia.sort(ordenarPorLessRate);

        // Combinar habitaciones con y sin coincidencia, poniendo las coincidencias al inicio
        habitacionesFiltradas.rooms = [...habitacionesConCoincidencia, ...habitacionesSinCoincidencia];

        // Actualizar las habitaciones del hotel
        hotel.rooms = habitacionesFiltradas.rooms;
        vm.rooms = hotel.rooms;


        vm.isBlockRAPD = data.isBlockRAPD;
        if (data.campaignToken && data.campaignToken.length) {
            $location.search('ctInternal', data.campaignToken);
        }
        vm.customLimit = _isBot ? 2 : vm.rooms.length;
        _campaignToken = data.campaignToken;
        let trivagoUrl = _paramQuery.room_search || null;
        if (vm.rooms.length > 0 && trivagoUrl && vm.isTrivagoRate) {
            if (!vm.appicableExperiment) {
                let rateObject = trivagoUrl.split('|').length < 2 ? trivagoUrl.split('%') : trivagoUrl.split('|');
                let indexRoom = vm.rooms.findIndex(x => x.roomId == rateObject[0]);
                if (indexRoom == -1) {
                    vm.isTrivagoRate = false;
                } else {
                    let indexRate = indexRoom != -1 ? vm.rooms[indexRoom].rate.rate.findIndex(x => x.rateId == rateObject[1]) : null;
                    if (indexRate == -1 || indexRate == null) {
                        vm.isTrivagoRate = false;
                    } else {
                        var tempRate = JSON.stringify(vm.rooms);
                        tempRate = JSON.parse(tempRate);
                        tempRate = tempRate[indexRoom];
                        tempRate.rate.rate = tempRate.rate.rate.filter(x => x.rateId == rateObject[1]);
                        tempRate.rate.rate.selectedRoomValue = 1;
                        vm.featuredRoom = tempRate;

                        setTimeout(function () {
                            vm.goSection('highligth_rooms', 320)
                        }, 500);
                    }
                }
            } else {
                setTimeout(function () {
                    vm.goSection('highligth_rooms', 320)
                }, 500);
            }

        }

        tracker = new DetailTracker(newPax.paxes, vm.responseroomsAvailables, vm.rooms);
        tracker.initialLayers(vm.ratesResponse);
        tracker.unavailableHotel(errorMessagesStatus, newPax.paxesFormat);
        tracker.setNotCoordinates(vm.hotel);
        if (vm.rooms.length == 0) {
            vm.roomsAvailLoading = true;
            tracker.setHotelNotRooms();
        }
        if (vm.roomsAvailables > 0) {
            tracker.viewItemListTB([hotel]);
        }
        if (notAvailableImg) {
            tracker.unavailableHotelImg(vm.roomsAvailables, hotel.hotelId);
        }
        if (processedRoomIdsTableDuplicate && processedRoomIdsTableDuplicate.size > 0) {
            tracker.roomDuplicate(processedRoomIdsTableDuplicate, hotel.hotelId);
        }
        removeElementsSkeleton('hotel_price');
        if (roomsLogin.length) {
            mergeNormalAndLoginRates();
        }
        vm.validPaxCompleted();
        loadRoomRatesUnique();
    }


    function setProviderIdentifier(rate) {

        const providers = {
            expedia: '',
            derbysoft: '',
            hotelbeds: '',
            synxis: '',
            unique: ''
        }

        if (rate.provider == 4) {
            providers.expedia = '.';
        } else if (rate.provider == 97) {
            providers.derbysoft = '.';
        } else if (rate.provider == 100) {
            providers.hotelbeds = '.';
        } else if (rate.provider == 130) {
            providers.synxis = '.';
        } else if (rate.provider == 109) {
            providers.unique = '.';
        }
        return providers;
    }

    let onSuccessSharedRequest = async (data, destino) => {
        sharedGlobalKey = data.data.key;
        sharedGlobalUrl = data.data.url;
        if (destino === "facebook") {
            window.open(config.uriFacebook + config.siteUrl + "/r?data=" + sharedGlobalKey);
        } else if (destino === "whatsapp") {
            window.open(config.uriwhatsapp + config.siteUrl + "/r?data=" + sharedGlobalKey);
        } else {
            const body = document.querySelector('body');
            const area = document.createElement('textarea');
            body.appendChild(area);
            area.value = config.siteUrl + "/r?data=" + sharedGlobalKey;
            area.select();
            document.execCommand('copy');
            body.removeChild(area);
            $(".copied-link").show("fast");
            await fn.sleep(1500);
            $(".copied-link").hide("fast");
        }

    }

    //Payment methods handler
    function onSuccessPaymentMethods(response) {
        const data = response.data || [];
        vm.monthInterestData = data.monthInterestFree;
        const monthInterestFree = data.monthInterestFree ? data.monthInterestFree.options : [];
        const groupedPayments = monthInterestFree.reduce((groups, item) => {
            const maxMonths = Math.max(...item.paymentPlans);
            if (!groups[maxMonths]) {
                groups[maxMonths] = [];
            }
            groups[maxMonths].push(item);
            return groups;
        }, {});
        const sortedEntries = Object.entries(groupedPayments).sort(([keyA], [keyB]) => Number(keyB) - Number(keyA));
        const paymentMethods = {
            fixedPayments: data.fixedPayments
                .map(fixedPayments => fixedPayments.options)
                .reduce((prev, curr) => prev.concat(curr), []),
            monthInterestFree: sortedEntries,
            quotasPayments: data.quotasPayments ? data.quotasPayments.options : [],
            otherPayments: data.otherPayments ? data.otherPayments.options : [],
            loading: false,
            title: data.quotasPayments ? data.quotasPayments.title : "",
            maxmsi: data.monthInterestFree ? data.monthInterestFree.title : ""
        };
        vm.responsePayment = paymentMethods;
    }

    function onErrorShared(response, type = null) {
        vm.pageError.has = true;
        vm.pageError.type = type;
        vm.loading = false;
        vm.reviewsLoadingModal = false;

    }
    function onSuccessReviewsRequest(data) {
        var datosReviews = data.data;
        if (datosReviews.verified != null) {
            var sortReviews = data.data.verified.recent.sort((a, b) => new Date(b.date_submitted).getTime() - new Date(a.date_submitted).getTime());
            var filterSinREviews = sortReviews.filter(x => x.text == "" && x.title == "");
            sortReviews = sortReviews.filter(x => x.text != "" || x.title != "");
            sortReviews = sortReviews.concat(filterSinREviews);
            datosReviews.verified.recent = sortReviews;
        }
        if (datosReviews.ratings != null) {
            setTimeout(() => { printScoresReview(datosReviews) }, 300)
        }
        vm.reviewsLoadingModal = false;
        vm.listReviews = datosReviews;
    }
    //skillBase handler
    function onSuccessSkillBase(data) {
        let response = data.data;
        if (response.message == _successSkillBase) {
            skillBase = response.result;
            vm.phoneBase = skillBase.did;
            $(headerPhoneId).attr('href', `tel:${vm.phoneBase}`);
            $('.skillbase_p').text($filter('tel')(vm.phoneBase));
        }
    }

    //AvailabilityReasons handler
    function onSuccessAvailabilityReasons({ data }) {

        const list = data && data.length ? data : [];

        for (let i = 0; i < vm.rooms.length; i++) {
            const room = vm.rooms[i];
            const infoAvailability = fn.findObject(list, 'roomId', room.roomId);
            vm.rooms[i].unavailableStatus = setAvailabilityMessages(infoAvailability);
        }
        if (list.length)
            errorMessagesStatus = list[0].availabilityStatus
        vm.responseroomsAvailables = data;

        if (!vm.isRequote && vm.ratesResponse.rooms && !vm.ratesResponse.rooms.length && config.recommenderDatesActive) {
            getRecommendedDates(list);
        }
    }

    function getRecommendedDates(reasons) {

        let defaultReason = {
            roomID: vm.rooms[0].roomId,
            availabilityStatus: "None",
            availabilityValue: "",
            explanation: "No known problems"
        };

        let data = {
            checkIn: box.checkIn,
            checkOut: box.checkOut,
            availabilityReasons: reasons.length ? reasons : [defaultReason],
            idHotel: hotel.hotelId,
            channelId: config.channelFac,
            token: paramsRoomRates.CampaignToken ? paramsRoomRates.CampaignToken : "",
            adults: _paxes.adults,
            kids: _paxes.children,
            providerId: 1,
            placeIdDestination: place.placeId,
            kidsAge: getAgeKids()
        };

        $http.post(config.endPoints.recommenderDatesUrl, data).then(renderRecomendedDates, errorMessagesStatus);
    }

    function renderRecomendedDates(res) {
        if (res.data) {
            if (res.data.status === 'OK') {
                hotel.checkIn = box.checkIn;
                hotel.checkOut = box.checkOut;
                vm.recommendedDates = res.data.message;
            }
        }
    }

    /*** EN EL HTML ***/
    function transformDescription() {
        if (vm.hotel.description) {
            if (vm.hotel.description.length <= vm.sizeDescriptionString) {
                vm.hotel.shortDescription = vm.hotel.description;
                return;
            }

            vm.hotel.description = vm.hotel.description ? vm.hotel.description.split('\n').map(prf => (prf == "\r" ? '<p></p>' : prf)).join('') : vm.hotel.description;
            vm.hotel.shortDescription = vm.hotel.description.substring(0, vm.sizeDescriptionString) + ' ...';
        }
    }

    function printScoresReview(datosReviews) {
        $(".condition").css({ 'width': datosReviews.ratings?.guest?.condition * 10 + "%" });
        $(".cleanliness").css({ 'width': datosReviews.ratings?.guest?.cleanliness * 10 + "%" });
        $(".service").css({ 'width': datosReviews.ratings?.guest?.service * 10 + "%" });
        $(".comfort").css({ 'width': datosReviews.ratings?.guest?.comfort * 10 + "%" });
        $(".amenities").css({ 'width': datosReviews.ratings?.guest?.amenities * 10 + "%" });
        $(".neighborhood").css({ 'width': datosReviews.ratings?.guest?.neighborhood * 10 + "%" });
    }

    function initialCampaignToken() {
        let campaignTokenApi = _paramQuery.campaignTokenApi || _paramQuery.CampaignTokenApi || _paramQuery.campaigntokenapi || null;
        _campaignToken = _paramQuery.CampaignToken || _paramQuery.campaignToken || campaignTokenApi;
        _campaignId = _paramQuery.CampaignId || _paramQuery.campaignid || null;

        if (_user && _user.firebaseId) {
            _campaignToken = config.campaignTokenUser;
        }
    }

    function onErrorCategories(response) {
        vm.filteredImages = buildGridGallery(vm.gallery);
        trackCategory.sendEventTracker(`Hotel Sin Categorias en Galeria`, `fotos: 0 | Error API`)
    }


    function onError(response, type = null) {
        vm.pageError.has = true;
        vm.pageError.type = type;
        vm.recomendationsList = {
            loading: true,
            rooms: []
        }
        vm.recomendationsListCopy = {
            rooms: []
        }
        vm.recomendationsList.loading = false;
        vm.loading = false;
        vm.cheaperRoomRate.loading = false;
        vm.rooms = vm.rooms.map(room => {
            room.rate = { disabled: true };
            return room;
        });
        removeElementsSkeleton('hotel_price');
    }

    function onErrorAvailabilityReason(response) {
        console.log(response);
        /*vm.rooms = vm.rooms.map(room => {
            room.unavailableStatus = setAvailabilityMessages(null);
            return room;
        });*/
    }

    function caruosel() {

        let options = {
            dots: false,
            infinite: false,
            speed: 300,
            initialSlide: 0,
            centerMode: false,
            variableWidth: false,
            lazyLoad: 'ondemand',
            slidesToShow: 3,
            slidesToScroll: 3,
            responsive: [
                {
                    breakpoint: 1023,
                    settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1
                    }
                },
                {
                    breakpoint: 700,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        centerMode: true,
                        variableWidth: true,

                    }
                },
                {
                    breakpoint: 432,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        variableWidth: true,
                    }
                },
                {
                    breakpoint: 375,
                    settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                        variableWidth: true,

                    }
                }
            ]
        };
        slickOptionsFire(options, "#carousel-restaurants");
    }

    function slickOptionsFire(options, div) {
        if ($(div).length == 0) {
            return;
        }
        var triggerAtY = $(div).offset().top - $(window).outerHeight();
        if (triggerAtY < $(window).scrollTop()) {
            $(div).slick(options);
            $(div).removeClass("visibility-none")
            return;
        }
        $(window).scroll(function (event) {
            if (triggerAtY > $(window).scrollTop()) {
                return;
            }
            $(div).slick(options);
            $(div).removeClass("visibility-none")
            $(this).off(event);
        });
    }

    vm.getPathToList = () => {
        let uri = "";
        const queryString = { ...$location.search() };

        if (!queryString.checkout) queryString.checkout = box.checkOut;
        if (!queryString.checkin) queryString.checkin = box.checkIn;

        if (hotel.hotelId) {
            queryString.profileId = hotel.hotelId.toString();
        }

        if (placeContainer && placeContainer.uri) {
            uri = `${config.siteUrl}${config.pathHoteles}${placeContainer.uri}_d${placeContainer.id}?${$.param(queryString)}`;
        } else {
            const displayText = hotel.location.state
                ?.normalize('NFD')
                .replace(/[^a-zA-Z0-9 ]/g, '')
                .replace(/\s+/g, '-')
                .toLowerCase();

            uri = `${config.siteUrl}${config.pathHoteles}${displayText}_d0?${$.param(queryString)}`;
        }

        return uri;
    };

    function getPaxes() {
        let boxLength = box.pax && box.pax.length;
        let paxes = {
            adults: 0,
            children: 0,
            paxesFormat: []
        };
        for (let i = 0; box.pax && i < boxLength; i++) {
            const pax = box.pax[i];
            let paxFormat = "";
            paxes.adults += pax.adults;
            paxFormat += `${pax.adults}`;

            if (pax.children && pax.children.length) {
                paxFormat += '|';
                paxes.children += pax.children.length;
                paxFormat += `${pax.children.map(it => it.year).join(',')}`;
            }

            paxes.paxesFormat.push(paxFormat);
        }
        return paxes;
    }

    function getPaxesRates() {
        let boxLength = box.pax && box.pax.length;
        let paxes = {
            adults: 0,
            children: 0,
            paxesFormat: []
        };
        for (let i = 0; box.pax && i < boxLength; i++) {
            const pax = box.pax[i];
            let paxFormat = "";
            paxes.adults += pax.adults;
            paxFormat += `${pax.adults}`;

            if (pax.children && pax.children.length) {
                paxes.children += pax.children.length;
                paxFormat += `|${pax.children.map(it => it.year).join(',')}`;
            }

            paxes.paxesFormat.push(paxFormat);
        }
        return paxes;
    }

    function orderByRate(room) {
        let value = null;

        if (room && room.rate && !room.rate.disabled) {
            value = room.rate.lessRate;
        }

        return value;
    }

    function initPaymentNow(roomRate) {
        const ratesLenght = roomRate.rate.length
        for (var i = 0; i < ratesLenght; i++) {
            const rate = roomRate.rate[i];
            rate.paymentNow = false;

            if (!vm.hasCancellationFree) {
                vm.hasCancellationFree = !rate.isNonRefundable;
            }

            if (!vm.hasPayLater && rate.isBookNowPayLaterApplicable) {
                vm.hasPayLater = rate.isBookNowPayLaterApplicable;
            }
        }
    }

    function getCheaperRoomRate(rooms) {
        let rate = {
            messages: "",
            disabled: true,
            loading: false
        }
        let rateParamns = _paramQuery.room_search || null;
        if (rateParamns && vm.isTrivagoRate) {
            let rateObject = rateParamns.split('|').length < 2 ? rateParamns.split('%') : rateParamns.split('|');
            let preRate = rooms.filter(x => x.roomId == rateObject[0]);
            let rateObt = preRate && preRate.length > 0 ? preRate[0].rate.filter(x => x.rateId == rateObject[1]) : null;
            rate = rateObt && rateObt.length > 0 ? rateObt[0] : rooms[0].rate[0];
            rate.disabled = false;
            rate.loading = false;
        }
        else if (rooms && rooms.length && rooms[0].rate && rooms[0].rate.length) {
            rate = rooms[0].rate[0];
            rate.disabled = false;
            rate.loading = false;
        }

        return rate;
    }

    function getValuesDefaults() {
        return {
            CampaignToken: _campaignToken,
            CampaignId: _campaignId,
        }
    }

    function getRommsIds() {
        return hotel.rooms.map(room => room.roomId);
    }

    function setAvailabilityMessages(statusData) {

        const messagesObj = ln.availabilityMessages;
        try {


            if (!statusData) {
                return {
                    explanation: '',
                    availabilityValue: '',
                    availabilityStatus: 'None',
                    messages: messagesObj.none
                };
            }

            let status = statusData.availabilityStatus.toLocaleLowerCase();

            if (status == 'roomcapacity' && box && box.pax) {
                const roomCapacity = JSON.parse(statusData.availabilityValue);

                if (box.pax.some(room => room.adults + room.children.length > roomCapacity.Max)) {
                    status = "peoplemax";
                } else if (box.pax.some(room => room.adults > roomCapacity.A)) {
                    status = "adultsmax";
                } else if (box.pax.some(room => room.children.length > roomCapacity.K)) {
                    status = roomCapacity.K < 1 ? "kidszero" : "kidsmax";
                } else {
                    status = "peoplemax";
                }
            }

            const newStatus = {
                ...statusData,
                messages: messagesObj[`${status}`]
            }

            if (status != 'none') {
                newStatus.messages.title2 = newStatus.messages.title2.replace('{{days}}', +statusData.availabilityValue || messagesObj.defaults[status]);
            }

            return newStatus;

        } catch (error) {
            return {
                explanation: '',
                availabilityValue: '',
                availabilityStatus: 'None',
                messages: messagesObj.none
            };
        }

    }

    function getPaxToMessagesApi() {
        return newPax.paxesFormat.map(rm => {
            const parts = rm.split('|');
            const adults = parts[0];
            const children = parts.length > 1 ? parts[1].split(',').join('_') : '';
            return `${adults}${children ? '_' : ''}${children}`;
        });
    }

    function initStickySearchBox() {
        $(window).scroll(function () {
            var top = $(window).scrollTop();
            var offsetSb = $("#container-detail").offset()
            if (offsetSb) {
                if (offsetSb.top < top) {
                    $("#searchbox").addClass("sticky");
                } else {
                    $("#searchbox").removeClass("sticky");
                }
            }
        });
    }

    function handleScroll() {
        $(window).on('scroll', () => {
            if (fn.isInViewport(vm.headTabs.amenities)) {
                vm.headTabs.current = vm.headTabs.amenities;
            } else if (fn.isInViewport(vm.headTabs.rooms, 295)) {
                vm.headTabs.current = vm.headTabs.rooms;
            } else if (fn.isInViewport(vm.headTabs.map)) {
                vm.headTabs.current = vm.headTabs.map;
            } else {
                vm.headTabs.current = '';
            }
            $scope.$apply();
        })
    }

    function setRecentDestination(hotel) {
        let registry = StorageService.get(settings.recentDestinations) || [];
        const exists = registry.findIndex(item => item.place.id == place.id);
        if (exists > -1) {
            const temp = registry[exists];
            temp.box = box;
            temp.campaignTokenApi = _campaignToken;
            registry.splice(exists, 1);
            registry.unshift(temp);
        } else {
            registry.unshift({
                box: box,
                place: place,
                IdG: hotel.hotelId,
                campaignTokenApi: _campaignToken,
                title: ln.hotel_lang,
                icon: config.cloudCdn + "/assets/img/Hotel_icon.svg",
                origin: "H",
                image: hotel.gallery && hotel.gallery.length > 0 ? hotel.gallery[0].cloudUri : (hotel.gallery[0] || "")
            });
        }

        if (registry.length > settings.limitDestinations) {
            registry = registry.slice(0, settings.limitDestinations);
        }

        StorageService.set(settings.recentDestinations, registry);
    }

    function removeElementsSkeleton(className) {
        let elements = $(`.${className}`);
        if (elements.length) {
            elements.remove();
        }
    }

    function smoothZoom(map, max, cnt) {
        if (cnt >= max) {
            return;
        }
        else {
            let listener = google.maps.event.addListener(map, 'zoom_changed', function (event) {
                google.maps.event.removeListener(listener);
                smoothZoom(map, max, cnt + 1);
            });
            setTimeout(function () { map.setZoom(cnt) }, 80);
        }
    }

    function getAgeKids() {
        return (box.pax[0].children.map(item => item.year)).join(',');
    }

    init();
    function filterServicesByCharge(services) {
        var servicesHasCharge = services && services.filter(item => item.hasExtraCharge === true);
        var servicesNotHasCharge = services && services.filter(item => item.hasExtraCharge === false);

        return {
            servicesHasCharge: servicesHasCharge,
            servicesNotHasCharge: servicesNotHasCharge
        };
    }
    function getRestMobile(overflow) {
        if (overflow) {
            document.body.classList.add("modal-open");
        }
        else {
            document.body.classList.remove("modal-open");
        }
    }
    function formatString(template, ...args) {
        return template.replace(/{(\d+)}/g, (match, index) => args[index] || '');
    }
    vm.toggleServicesSection = function (sectionId) {
        vm.openServicesSections[sectionId] = !vm.openServicesSections[sectionId];
    };
    vm.isServicesSectionOpen = function (sectionId) {
        return !vm.openServicesSections[sectionId];
    };
    vm.selectNearbyPlacesCategory = function (index) {
        vm.nearbyPlacesCategory = index;
    };
}


const ordenarPorLessRate = (a, b) => {
    const lessRateA = a.rate?.lessRate;
    const lessRateB = b.rate?.lessRate;
    const tieneLessRateA = lessRateA !== undefined && !a.rate.disabled;
    const tieneLessRateB = lessRateB !== undefined && !b.rate.disabled;
    if (tieneLessRateA && !tieneLessRateB) {
        return -1;
    } else if (!tieneLessRateA && tieneLessRateB) {
        return 1;
    } else if (tieneLessRateA && tieneLessRateB) {
        return lessRateA - lessRateB;
    } else {
        return 0;
    }
};
const styleArray = [
    {
        "featureType": "poi.business",
        "stylers": [
            {
                "visibility": "off"
            }
        ]
    }
];

<template>
    <div class="card card-info-list card-summary-mobile mb-3" role="complementary" :aria-label="__('messages.complementary_summary_payment')">
        <div class="card-body pt-0">
            <p class="title font-md">{{ summary.name }}</p>
            <p>{{ summary.location.city }}, {{ summary.location.country }}</p>
            <p class="title">{{ summary.roomsTotal }} {{summary.roomsTotal>1?__('messages.rooms'):__('messages.room')}}, {{ nights }} {{nights > 1 ? __('messages.nights'):__('messages.night')}} </p>
            <div class="item-columns">
                
            </div>

            <a href="#" type="button" class="btn-link float-right" data-toggle="modal" data-target="#summaryModal">{{__('messages.summary_reservation')}}
                <span class="icons-angle-right" aria-hidden="true"></span></a>

        </div>
    </div>
</template>

<script>

export default {
    props: {
        summary: Object
    },
    data() {
        return {
            nights: 0
        }
    },
    mounted() {
        this.nights = this.getNights(this.summary.checkIn , this.summary.checkOut);
    },
    methods: {
        getNights(startDate, endDate) {
            const inicio = new Date(startDate);
            const fin = new Date(endDate);
            const diferenciaMilisegundos = fin - inicio;
            const noches = diferenciaMilisegundos / (1000 * 60 * 60 * 24);
            return noches;
        },
        paymentMessage() {
            if (this.summary.rate.collectType == 2) {
                return this.__('messages.pay-accommodation-title');
            }
            return this.__('messages.total');
        }
    }
}

</script>
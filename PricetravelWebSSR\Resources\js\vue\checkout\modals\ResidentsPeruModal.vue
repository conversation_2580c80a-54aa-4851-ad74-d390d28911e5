<template>
    <div class="modal fade show" id="staticfax" data-backdrop="static" data-keyboard="false" tabindex="-1" 
        aria-labelledby="staticBackdropLabel" style="backdrop-filter: brightness(0.5);" aria-modal="true" role="dialog">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content modal-mobile-big ">
                <div class="modal-header">
                    <p class="modal-title" id="staticBackdropLabel font-16">{{ __('messages.information_important') }}</p>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="product-detail-item" style="align-items: self-end;">
                        <div>
                            <i class="icon-flag icon-peru" aria-hidden="true"></i>
                        </div>
                        <div>
                            <p class="product-detail-item-title font-14 font-strong" v-html="__('messages.impuest_local')"></p>
                        </div>
                    </div>
                    <p class="title font-14">{{ __('messages.redesidentes_peru') }}</p>
                    <p class="mb-3">{{ __('messages.redesidentes_peru_detail') }}</p>

                    <p class="title font-14">{{ __('messages.redesidentes_extran') }}</p>
                    <p>{{ __('messages.redesidentes_extran_detail') }}</p>
                    <button type="button" data-dismiss="modal"
                        class="btn btn-primary btn-sm but noPrint float-right mt-3"
                        aria-label="Continue">{{ __('messages.close_modal_peru') }}</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {

    methods: {

    },
    mounted() {
        $(document).on('hidden.bs.modal', function (event) {
            if ($('.modal:visible').length) {
                $('body').addClass('modal-open');
            }
        });
    }
}
</script>

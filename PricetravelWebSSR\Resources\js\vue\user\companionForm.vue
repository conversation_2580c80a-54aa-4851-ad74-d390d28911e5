﻿<template>
    <!-- Add new person Form -->
    <form class="mx-auto" style="max-width: 600px;">
        <h2 v-if="isNew" class="mb-3">{{__("profile.new_companion")}}</h2>
        <p v-if="isNew" class="mb-32 d-flex g-base">
            <i class="icons-info font-blue font-18"></i>
            {{__('profile.message_important')}}
        </p>
        <ValidationObserver :ref="isNew ? 'paxeNew' : `paxeNew${index}`" v-slot="{ invalid }" tag="form" @submit.prevent="onSubmit(isNew)">
            <!------------- NAME -------------->
            <fieldset class="my-32 d-flex flex-column g-32 flex-sm-row">
                <ValidationProvider rules="required|alpha_spaces" v-slot="{ errors }" ref="first_name" class="d-block flex-grow-1">
                    <label for="first_name">{{ __('profile.first_name') }}</label>
                    <input type="text" id="first_name" name="first_name" class="input-text"
                           :class="{ 'is-invalid': errors && errors.length }" v-model="data.firstName">
                    <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                </ValidationProvider>
                <ValidationProvider rules="required|alpha_spaces" v-slot="{ errors }" ref="last_name" class="d-block flex-grow-1">
                    <label for="last_name">{{ __('profile.last_name') }}</label>
                    <input type="text" id="last_name" name="last_name" class="input-text"
                           :class="{ 'is-invalid': errors && errors.length }" v-model="data.lastName">

                    <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                </ValidationProvider>
            </fieldset>
            <!------------- BIRTHDATE -------------->
            <fieldset class="my-32">
                <legend class="label">{{ __('profile.birthdate') }}</legend>
                <div class="d-flex g-16 dropup">
                    <ValidationProvider rules="required|is_not:0" v-slot="{ errors }" ref="day_user" class="d-block flex-grow-1">
                        <label for="day_user" id="day_user" class="font-14">
                            {{__('profile.day') }}
                        </label>
                        <select class="selectpicker" data-container="body" data-none-selected-text="" v-model="data.day_user" id="month_user" name="day_user">
                            <option v-for="option in day_options" :value="option.value">
                                {{ option.text }}
                            </option>
                        </select>
                        <span v-if="errors[0]" class="d-block text-danger font-12 mt-1">{{ errors[0] }}</span>
                    </ValidationProvider>
                    <ValidationProvider rules="required|is_not:0" v-slot="{ errors }" ref="month_user" class="d-block flex-grow-1">
                        <label for="month_user" id="month_user" class="font-14">
                            {{__('profile.month') }}
                        </label>
                        <select class="selectpicker" data-dropup-auto="false" data-container="body" data-none-selected-text="" v-model="data.month_user" id="month_user"  name="month_user">
                            <option v-for="option in month_options" :value="option.value">
                                {{ option.text }}
                            </option>
                        </select>
                        <span v-if="errors[0]" class="d-block text-danger font-12 mt-1">{{ errors[0] }}</span>
                    </ValidationProvider>
                    <ValidationProvider rules="required|is_not:0" v-slot="{ errors }" ref="year_user" class="d-block flex-grow-1">
                        <label for="year_user" id="year_user" class="font-14">
                            {{__('profile.year') }}
                        </label>
                        <select class="selectpicker" data-dropup-auto="false" data-container="body" data-none-selected-text="" v-model="data.year_user" id="year_user" name="year_user">
                            <option v-for="option in createYearsOptions()" :value="option.value">
                                {{ option.text }}
                            </option>
                        </select>
                        <span v-if="errors[0]" class="d-block text-danger font-12 mt-1">{{ errors[0] }}</span>
                    </ValidationProvider>
                </div>
            </fieldset>
            <fieldset class="d-flex flex-column g-32 flex-sm-row">
            <!------------- NATIONALITY -------------->
            <ValidationProvider rules="required" v-slot="{ errors }" ref="nationality-select" class="d-block flex-grow-1" style="flex-basis: 50%;">
                <label for="nationality">{{ __('profile.nationality') }}</label>
                <select  v-model="nationalityTemp"
                        id="nationality-select"
                        name="nationality-select"
                        class="selectpicker" data-dropup-auto="false" data-container="body" data-none-selected-text="">
                    <option v-for="(item, index) in nations"
                            :value="item.value"
                            :key="item.value">
                        {{ item.text }}
                    </option>
                </select>
                <span v-if="errors[0]" class="d-block text-danger font-12 mt-1">{{ errors[0] }}</span>
            </ValidationProvider>
                <!------------- PASSPORT -------------->
                <ValidationProvider v-slot="{ errors }" ref="passport" class="d-block flex-grow-1" style="flex-basis: 50%;">
                    <label for="passport" class="d-block">
                        <span class="d-flex align-items-end justify-content-between g-base">
                            {{ __('profile.passport') }}
                            <span class="font-14 font-subtle">{{ __('profile.optional') }}</span>
                        </span>
                    </label>
                    <input type="text" id="passport" name="passport" class="input-text"
                           :class="{ 'is-invalid': errors && errors.length }" v-model="data.passport">
                </ValidationProvider>
            </fieldset>
            <!------------- GENRE -------------->
            <fieldset class="my-32">
                <legend class="label">{{__('profile.genre')}}</legend>
                <ValidationProvider rules="required" v-slot="{ errors }" ref="genre-select">
                    <div class="d-flex g-24">
                        <label class="font-14" v-for="n in genrer_options">
                            <input v-model="data.gender" class="input--radio" type="radio" :name="`genre-${n.value}`" :id="`genre-${n.value}`" :value="n.value">
                            {{ n.text }}
                        </label>
                    </div>
                    <span v-if="errors[0]" class="d-block text-danger font-12 mt-1">{{ errors[0] }}</span>
                </ValidationProvider>
            </fieldset>

            <hr class="d-none d-sm-block my-32">

            <fieldset class="d-flex flex-column g-32 flex-sm-row align-items-sm-center">
                <!------- TERMS AND CONDITIONS --->
                <ValidationProvider v-if="isNew" rules="required:true" v-slot="{ errors }" ref="terms-option">
                    <div class="d-flex align-items-start g-8">
                        <input type="checkbox" :value="data.accepted" id="acceptData" v-model="data.accepted">
                        <label for="acceptData" class="font-14" style="line-height: 1em;">
                            {{__('profile.terms')}}.
                            <a class="link d-inline font-14" :href="'/'+ culture + __('url.url_terms')" target="_blank">
                                {{ __('messages.privacy_terms_and_reservation_police') }}
                            </a>
                            <span v-if="errors[0]" class="d-block text-danger font-12 mt-1">{{ errors[0] }}</span>
                        </label>

                    </div>
                </ValidationProvider>
                <!------- SAVE --->
                <div class="d-flex align-items-center g-16">
                    <button type="button" class="btnTertiary btnTertiary--md flex-grow-1" @click="cancelAction()">{{ __('profile.cancel') }}</button>
                    <button type="submit" class="btnPrimary btnPrimary--md flex-grow-1">{{ __('profile.save') }}</button>
                </div>
            </fieldset>
        </ValidationObserver>
    </form>
</template>

<script>
    import { ValidationObserver, ValidationProvider } from 'vee-validate';
    import intlTelInput from 'intl-tel-input';
    import { storeToRefs } from 'pinia';
    import { useUserStore } from '../stores/user';
    import GlobalUtil from '../../utils/pricetravel/shared/globalUtil';
    import { accountAnalytics } from '../analytics/AccountAnalytics';

    const _paramQuery = window.__pt.fn.search();
    const userLocation = window.__pt.userLocation;
    const siteconfig = window.__pt.settings.site;
    const cultureData = window.__pt.cultureData || {};
    const userData = window.__pt.user || {};
    const _globalUtil = new GlobalUtil(_paramQuery, siteconfig);
    export default {
        data() {
            return {
                nations: [],
                site: window.__pt.settings.site,
                culture: __pt.cultureData.cultureCode,
                day_options: [
                    { "text": "--", "value": "0" },
                    { "text": "1", "value": "01" },
                    { "text": "2", "value": "02" },
                    { "text": "3", "value": "03" },
                    { "text": "4", "value": "04" },
                    { "text": "5", "value": "05" },
                    { "text": "6", "value": "06" },
                    { "text": "7", "value": "07" },
                    { "text": "8", "value": "08" },
                    { "text": "9", "value": "09" },
                    { "text": "10", "value": "10" },
                    { "text": "11", "value": "11" },
                    { "text": "12", "value": "12" },
                    { "text": "13", "value": "13" },
                    { "text": "14", "value": "14" },
                    { "text": "15", "value": "15" },
                    { "text": "16", "value": "16" },
                    { "text": "17", "value": "17" },
                    { "text": "18", "value": "18" },
                    { "text": "19", "value": "19" },
                    { "text": "20", "value": "20" },
                    { "text": "21", "value": "21" },
                    { "text": "22", "value": "22" },
                    { "text": "23", "value": "23" },
                    { "text": "24", "value": "24" },
                    { "text": "25", "value": "25" },
                    { "text": "26", "value": "26" },
                    { "text": "27", "value": "27" },
                    { "text": "28", "value": "28" },
                    { "text": "29", "value": "29" },
                    { "text": "30", "value": "30" },
                    { "text": "31", "value": "31" },

                ],
                month_options: [
                    { "text": "--", "value": "0" },
                    { "text": this.__(`months.01`), "value": "01" },
                    { "text": this.__(`months.02`), "value": "02" },
                    { "text": this.__(`months.03`), "value": "03" },
                    { "text": this.__(`months.04`), "value": "04" },
                    { "text": this.__(`months.05`), "value": "05" },
                    { "text": this.__(`months.06`), "value": "06" },
                    { "text": this.__(`months.07`), "value": "07" },
                    { "text": this.__(`months.08`), "value": "08" },
                    { "text": this.__(`months.09`), "value": "09" },
                    { "text": this.__(`months.10`), "value": "10" },
                    { "text": this.__(`months.11`), "value": "11" },
                    { "text": this.__(`months.12`), "value": "12" },
                ],
                genrer_options: [
                    { "text": this.__(`profile.fem`), "value": "F" },
                    { "text": this.__(`profile.man`), "value": "M" },
                ],
                addedNewPassenger: false,
                editedPaxe: false,
                editingIndex: null,
                deteleIndex: null,
                nationalityTemp: this.getNewPaxe?.Nationality?.toLowerCase() || this.data.nationality,
            }
        },
        props: {
            data: null,
            isNew: false,
            index:0
        },
        components: {
            ValidationObserver,
            ValidationProvider
        },
        computed: {
        },
        setup() {
            const useUser = useUserStore();
            const { setNewPassenger, setNationalityNewPaxe, setBirthdayNewPaxe, addNewPaxe, disablePassenger, setUserProfile } = useUser;
            const { getNewPaxe, getPassenger, getUserProfile, resetNewPaxe } = storeToRefs(useUser);

            return { setNewPassenger, getNewPaxe, setNationalityNewPaxe, setBirthdayNewPaxe, getPassenger, resetNewPaxe, getUserProfile, resetNewPaxe, addNewPaxe, disablePassenger, setUserProfile }
        },
        beforeMount() {
            const countries = window.intlTelInputGlobals.getCountryData();
            this.createNationsOptions(countries);
        },
        mounted() {
            this.editingIndex = this.index;
            $('.selectpicker').selectpicker();
        },
        watch: {
            nationalityTemp(newVal) {
                this.setNationalityNewPaxe(newVal);
            }
        },
        methods: {
            createNationsOptions(countries) {
                for (let index = 0; index < countries.length; index++) {
                    this.nations.push({ "text": countries[index].name, "value": countries[index].iso2 });
                }
            },
            createYearsOptions(age = 0) {
                const today = new Date();
                const year = today.getFullYear();
                const yearUntil = age == 0 ? 1900 : (year - age - 2);

                return [
                    { text: "--", value: "0" },
                    ...Array.from({ length: year - yearUntil }, (_, i) => {
                        const y = year - i;
                        return { text: String(y), value: String(y) }
                    })
                ];
            },
            async onSubmit(isNew) {
                const refName = this.isNew ? "paxeNew" : `paxeNew${this.index}`
                let observer = this.$refs[refName];
                if (Array.isArray(observer) && !this.isNew) observer = observer[0];

                if (!this.isNew) {
                    if (observer && typeof observer.validate === 'function') {
                        const isValid = await observer.validate();
                        if (isValid) {
                            const passenger = this.getUserProfile.passengers[this.index];
                            if (passenger.day_user && passenger.month_user && passenger.year_user) {
                                passenger.birthdate = `${passenger.day_user.padStart(2, '0')}/${passenger.month_user.padStart(2, '0')}/${passenger.year_user}`;
                            }

                            delete passenger.day_user;
                            delete passenger.month_user;
                            delete passenger.year_user;

                            const user = { ...this.getUserProfile };

                            const options = {
                                headers: {
                                    "X-CSRF-TOKEN": _globalUtil.getCSFRToken()
                                }
                            };

                            const response = await axios
                                .post(siteconfig.siteUrl + "/hotel/api/update-account", user, options)
                                .catch((data) => this.onError(data, 'error'));

                            if (response && response.status === 200) {
                                this.$emit('reset-form');
                                accountAnalytics.editCompanions("Edicion", userData.type);
                            } else {
                                this.onError();
                            }
                        }
                    }
                } else {
                    if (observer) {
                        const isValid = await observer.validate();
                        if (isValid) {
                            this.addNewPaxe();
                            let user = { ...this.getUserProfile };
                            let options = {
                                headers: {
                                    "X-CSRF-TOKEN": _globalUtil.getCSFRToken()
                                }
                            }

                            let response = await axios.post(siteconfig.siteUrl + "/hotel/api/update-account", user, options).catch((data) => this.onError(data, 'error'));

                            if (response && response.status == 200) {
                                this.$emit('reset-form');
                                accountAnalytics.editCompanions("Nuevo", userData.type);
                            } else {
                                this.onError();
                            }
                        }
                    }
                }
            },
            cancelCreationPaxe() {
                this.resetNewPaxe();
                this.addedNewPassenger = false;
            },
            cancelAction() {
                this.$emit('reset-form');
            },
            
        }
    }
</script>


﻿@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Types;

@inject IOptions<SettingsOptions> settingOptions

@inject ViewHelper viewHelper
@inject StaticHelper staticHelper
@{
    var resolution = viewHelper.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = viewHelper.IsRobot();
    var cultureConf = ViewData["cultureData"] as Culture;
    var faqContent = ViewData["FaqContent"] as FaqContentResponse;


    var page = (string)ViewData["PageRoot"];
    var pathName = (string)ViewData["PathName"];

    var imgPath = viewHelper.Localizer("img",@settingOptions.Value.AppName.ToLower());

    ViewData["Page"] = "info";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot } })

<div class="container" id="app-main">
    <faq :content='@Json.Serialize(faqContent)'></faq>
</div>

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml", new ViewDataDictionary(ViewData) {  { "login", isRobot } })

@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
}


@section Preload {
<link rel="preconnect" href="@settingOptions.Value.CloudCdn">
<link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/home.css")"
      as="style">

}

@section Css {
<link type="text/css" rel="stylesheet"
      href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/home.css")">
<link type="text/css" rel="stylesheet"
      href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/pages.css")">
}

@section Scripts {

    <script type="text/javascript">
        window.__pt = window.__pt || {};
        window.__pt.faqContent = @Html.Raw(viewHelper.ToJsonString(faqContent));
    </script>


    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_pdv.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_profile.min.js")"></script>
    <script src="@staticHelper.GetVersion($"/assets/js/vue/lang/{cultureConf.CultureCode}.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/controllers/login-controllers.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/vue/app.js")"></script>
}

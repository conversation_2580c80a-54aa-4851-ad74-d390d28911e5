.card-footer:last-child {
	border-radius: 0;
}
.card-body{
	.form-group:last-child{
		margin-bottom: 0;
	}
	h4{
		font-size: 1.25rem;
	}
}
.card-price { 
    cursor: pointer;
    transition: all .2s ease-in-out;
	position: relative;
	display: flex;
	flex-direction: column;
	min-width: 0;
	word-wrap: break-word;
	background-color: #fff;
	background-clip: border-box;
	border: none;
	border-radius: 10px;
	overflow: hidden;
	box-shadow: 0 0 0 1px #e5e5e5;
}

.card-price:hover {
	box-shadow: 0px 5px 10px 2px rgba(0, 0, 0, 0.20);
}

.card-price > .row {
	min-height: 220px;
}


//NUEVOS ESTILOS


//MENSAJE CON ICONO
.card-messagge-icon{
	width: 100%;
	display: block;
	border: var(--border-card) ;
	border-radius: 0.5rem;
    padding:1rem 0.5rem;
	margin: 1rem 0;
	background-color: $white;
	font-size: 14px;
	.icon{
	  width: auto;
	  text-align: center;
	  display: inline-block;
	  vertical-align: top;
	  padding: 0 0.5rem;
	}
	.txt{
	  width: 88%;
	  display: inline-block;
	}
	p{
		margin: 0;
	}
	.title{
		font-size: 16px;
	}
	i{
		color: $green;
	}
  }
  @media(max-width: 767px){
	.card-messagge-icon{
		.txt{
			width: 83%;
			display: inline-block;
		  }
	}

}
  //card info
  .card-info {
	background-color: var(--background-fill-0);
	border: var(--border-card);
	border-radius: var(--border-radius-1);
	font-size: var(--text-xs);
	padding: var(--spacing-inset-md);
	gap:.5rem;
  }
  .card-info p{
    margin-bottom: 0;
}
  //card info two columns
.card-info-list{
	border-radius: 0.5rem;
	font-size: 14px;
    border: none;
    box-shadow: var(--shadow-100);
	padding: var(--space-16);
    .card-header{
        
		padding: 0 0 var(--space-8) 0;
    }
    .card-body{
		padding: var(--space-8) 0 0 0;
		p{
			color: var(--text-subtle);
		}
    }
	p.title{
		font-size: 0.875rem;
        font-weight: 500;
        color: var(--text-main);
        margin: 0;
	}
	p.font-md{
		font-size: 1rem;
	}
	.title-success{
		font-size: 14px;
    }
    hr{
        margin: 10px 0;
    }
    h4{
        font:var(--body-bold);
        color: var(--text-main);
        margin: 0;
    }

}
.item-columns{
    display: flex;
    width: 100%;
}
.item {
	span{
		font-weight: 500;
	}
  }
.item-right {
	padding: 0.125rem 0;
	flex: 1;
	text-align: right;
}
.item-columns.title{
	.item{
		font-weight: 600;
		font-size: 1rem;

	small{
		font-weight: 600;
	}
	}
	.item-right{
		font-size: 1.125rem;
		font-weight: 600;

	small{
		font-weight: 600;
	}
	}
}
.card-gray{
	border-radius: 0.5rem;
	padding: 1rem;
	background-color: var( --background-fill-100);
	margin: 1.5rem 0;
	.card-body{
		padding:1rem  0;
	}
}

  //card detail
  .card-detail{
	background-color: hsl(196, 85%, 95%);
	border-radius:  0.5rem;
	margin-bottom: 1.5rem;
	p{
		margin: 0;
	}	
	small{
		display: block;
		margin-bottom:1rem;
	}
  }

  //card logo info
  .card-logo-info{
	display: flex;
	flex-direction: column; 
	margin-bottom: 1.5rem;
	.card-content-logo-info{
		display: flex;
		margin: 10px 0;
		padding-bottom: 15px;
		border-bottom: 1px solid var( --background-fill-200);
	}
	.logo-card{
		width: 30%;
		display: flex;
		gap: 5px;
		vertical-align: middle;
		align-items: center;
	}
	.info-card{
		width: 69%;
		display: inline-block;
		padding-left: 20px;
	}
	p{
		margin: 0;
	}

  }

.card-voucher {
    border-radius: 0.5rem;
    background-color: var(--background-fill-100);
    margin: 1.5rem 0;

	.txt-secondary{
		color:var(--neutral-gray-700);
		font-size: 14px;
	}
	.card-body{
		border-bottom: 1px solid hsl(240, 7%, 79%);

	}
	.card-body:last-child{
		border: none;
	}
	p{
		margin: 0;
		display: block;
	}
	.title{
		color: var(--text-color-600);
	}
}
.card-voucher-title{
    padding: 1rem;
	border-bottom: 1px solid var(--stroke-gray-200);
	span{
		float: right;
	}
}

.card-green{
	background-color:#F2F9EC;
	border:1px solid #D9ECC5!important;
	p{
		font-size: 14px;
	}
}
.card-purple{
	background-color:#F7F5FC!important;
	border:1px solid #E1DCEF!important;
	p{
		font-size: 14px;
	}
	.text-title{
		color: #3E2F6A;
	}
}
.card-content{
	background: $white;
	border-radius: 0.5rem;
	border: 1px solid rgba(0, 0, 0, 0.125);
  }

.card-content-tooltip{
	padding: var(---space-16);
	background: none;
	border-radius: 0.5rem;
	border: none;
  }
 .card-content-tooltip{
	padding: 0.5rem;
	div{
		margin-bottom: var(--spacing-stack-xxs);
		float: right;
	}
	div:last-child{
		margin-bottom: 0;
	}
	 p{
		margin-bottom: 0;
	 }
   }
   a.card{
	padding: var(--space-16);
	color: var(--text-global-text-strong);
	// margin-bottom:var(--space-16);
	p{
		margin: 0;
	}
	h6{
	font: var(--title-xxs);
	}
	.btn-link{
		color: var(--text-color-primary);
		text-align: right;
		margin-top: 0.5rem;
		width: 100%;
	}
	.title{
		font-size: 1rem;
		font-weight: 600;
	}
   }
   a.card:hover{
	text-decoration: none;
	.btn-link{
		text-decoration: underline;
	}
   }
   .card-room{
	margin: var(--space-16) 0;
	box-shadow: var(--shadow-100);
	padding: 0 !important;
	position: relative;
	border-radius: 0.5rem;

	.btn-link{
		position:absolute;
		top:55px;
		right: 20px;
	}
	.title-sm{
		font-size: 0.875rem;
		font-weight: 600;
	}
	.two-col-large{
		display: inline-block;
		width: 60%;
	}
	.two-col-small{
		display: inline-block;
		width: 29%;
	}
   }

	a.card-hotel{
		padding: var(--space-16) 0;
		color: var(--text-global-text-main);
		margin-bottom:var(--space-16) ;
		box-shadow: none;
		border: none;
		border-bottom:1px solid var(--border-global-border-subtle) ;
		position: relative;
		button.btn-link{
			position: absolute;
			top: 0;
			right: 0;
			span{
			  font-size: 1.5rem;
			  vertical-align: middle;
			}
		  }
		  h3{
			font: var(--title-xs);
			
		  }
}
a.card-hotel:last-child{
	border-bottom: 0px;
}
	
.badge-header-card{
	padding: var(--space-12);
	font-weight: 500;
	border-radius: 0.5rem 0.5rem 0 0;
}
.confirm{
	.tag{
		color: var(--text-success);
	}
	.badge-header-card{
		color: var(--text-success-strong);
		background-color:var(--bg-success-subtle) ;
	}
}
.pending{
	.tag{
		color: var(--text-semantic-text-warning-strong)
	}
	.badge-header-card{
		background-color:var(--bg-warning-subtle) ;
		color: var( --text-semantic-text-warning-strong);
	}
	.payModal.card-header{
		background-color: var();
	}
	.card-header{
		background-color:var(--bg-warning-subtle)!important ;
	}
}

.cancelled{
	p{
		text-decoration:line-through;
	}
	.tag{
		color: var(--text-error);
	}
	.btn-link, .tag, .title-sm{
		text-decoration:none ;
	}
	.badge-header-card{
		background-color:var(--bg-error-subtle) ;
		color: var(--text-error-strong);
	}
}
// .modal-body{
// 	.card-instructions{
// 		width: auto;
// 	}
// }


   @media(max-width: 767px){
	.card-content-tooltip{
			background: none;
			border-radius: 0.5rem;
			border: 1px solid rgba(0, 0, 0, 0.125);
		  }
		  a.card{
			box-shadow: var(--shadow-100);
			margin-bottom: var(--space-16);
		  }
		button.btn-link {
			position: inherit!important;
		}
		// .card-info-list{
		//   .card-body{
		// 	  padding: 0;
		//   }
		// }
}
@media(min-width: 768px) and (max-width: 990px){
	.card-content-tooltip{
			background: $white;
			border-radius: 0.5rem;
			border: 1px solid rgba(0, 0, 0, 0.125);
		  }
		  a.card{
			box-shadow: var(--shadow-100);
		  }
		  .card-info-list{
			.card-body{
				padding: 0;
			}
		  }
}
@media (min-width: 991px) { 
	.card-content-tooltip{
		padding: 0;
		float: right;
	}

a.card{
	h6{
		font: var(--title-xxs);
		}
}
}
.card-instructions{
	padding: var(--space-16);
	background-color: var(--bg-level1);
	width: 100%;
	border-radius: 0.5rem;
	margin-bottom: var(--space-16);
	
	h3{
		font:var(--body-bold);
		font-weight: 600;
	}
	p{
		margin: 0;
	}
	.title{
		font-weight: 600;
	}
	.title-sm{
		font: var(--body-sm-bold);
		padding-bottom: var(--space-4);
	}
	.column{
		width: 49%;
		display: inline-block;
		vertical-align: top;
		padding-bottom: var(--space-12);
	}
	.alert{
		padding: var(--spacing-inset-unit);
		font-size:var(--body-sm-bold); 
	}
}
.card-services{
	border-radius:0.5rem;
.badge-header-card{
	background-color:var(--bg-semantic-bg-info-subtle);
}
ul{
	padding: 0;
	list-style: none;
	span{
		font: var(--body-sm);
	}
}
}
.text-subtle{
	color: var(--text-subtle);
}
.shadow-300{
	box-shadow: var(--shadow-300);
}
.title-card{
	padding: var(--space-32);
}
.card-info-collumns{
	padding: var(--space-16) var(--space-32) var(--space-32) var(--space-32);
	display: flex;
	flex-direction: row;
	gap: 2rem;
	
	.two-collumns {
		h2{
			margin-bottom: 0.75rem;
		}
		h3{
			font-weight: 600;
			font-size: 1.125rem;
			margin: 0;
		}
		h4{
		margin-top: 1.5rem;
		}
		p{
		text-align: left;
		margin-bottom:0;
		}
		.btn{
			height: 48px;
			font-size: 0.875rem;
		}
		.image{
			border-radius: 8px;
			height: auto;
			img {
				height: 133px;
				width: 168px;
				object-fit: cover;
			}
		}
	}
	.border-left {
		border-left: 1px solid var(--border-strong);
		padding-left: 1.5rem;
	}
}
.btn-middle{
	width: 100%;
	max-width: 250px;
	margin: 0 auto 2rem;
	display: block;
}
@media(max-width: 767px){

	.card-instructions{
		width: auto;
		.column{
			width: 100%;
			.column{
				width: 49%;
			}
		}
	}
	.card-info-collumns{
		display: block;
		padding: 1rem;
	}
	
	.title-card, .card-info-collumns{
		padding: 1.5rem;
	}
	.btn-middle{
		margin:var(--space-12) auto;
		width: 100%;
		max-width: 300px;
	}
	.two-collumns:first-child {
        padding-bottom: 1.25rem;
        border-bottom: 1px solid var(--border-subtle) !important;
    }
	.two-collumns.border-left {
		border: none !important;
		padding-left: 0;
	}
	.two-collumns {
        margin-bottom: 1rem;
        width: 100%;
    }
	.card-info-collumns{
		h3{
			margin-top: 0;
		}
	}
	.line-bottom{
		border-bottom: 1px solid var(--border-strong);
		padding-bottom: 1rem;
	}
}
.info-gray p {
    color: var(--text-subtle);
}
.content-border {
    border: 1px solid var(--border-subtle);
    margin: 16px 0px;
    padding: 16px;
}
.content-border p span {
    color: var(--text-main);
}
.content-border h3 span {
    color: var(--text-subtle);
    font-size: 1.125rem;
}
.content-border p {
    color: var(--text-subtle);
    padding-bottom: 0px !important;
}
.card-info-collumns .two-collumns p {
    margin-bottom: 0px;
    text-align: left;
}
.container {
	max-width: 1280px !important;
}


.print {
	display: none;
}
@media print {
    header,main {
        display: none!important
    }

    .print {
        display: block;
    }
}

.header-print {
    display: block;
    height: 60px;
    width: 100%
}

.content-print .image {
    width: 150px
}



.content-print .separeta {
    color: var(--text-subtle);
    font-size: .875rem;
    padding-bottom: 10px
}

.content-print h3 {
    font-size: 1rem !important;
}

.content-print .two-collumns {
    border-bottom: 1 solid var(--border-strong);
    display: flex;
    gap: 1rem
}

.content-print .info-product,.content-print .two-collumns p {
    text-align: left
}

.content-print .border {
    margin-bottom: 16px;
    padding: 16px
}

.content-print .border h4 {
    font-size: 1.125rem !important;
    margin-bottom: 6px
}

.content-print .border h4 span {
    color: var(--text-subtle)
}

.content-print .border h4 p {
    color: var(--text-subtle);
    font-size: .875rem
}

.content-print .list-info-icons ul {
    margin: 0 0 1rem;
    max-width: 100%;
    padding: 0;
    width: 100%
}

.content-print .list-info-icons ul li {
    padding: .5rem 0
}

.content-print .list-info-icons ul span {
    display: inline;
    padding-left: 1rem
}

.content-print .list-number li {
    list-style: decimal;
    margin-bottom: .5rem
}

.content-print .alert {
    padding: 12px
}

.content-print .alert p {
    font-size: .875rem
}
.title-list-bank {
    background-color: var(--bg-level2);
    border-radius: 12px 12px 0 0;
    padding: 5px 5px 0
}

.title-list-bank .price-form {
    color: var(--text-subtle);
    font-size: 1rem!important;
    font-weight: 500
}

.title-list-bank .payment-form {
    color: var(--text-subtle);
    font-weight: 500;
    padding-right: 25px;
    width: 100%
}

.modal-small-mobile {
    margin: 30% auto 0;
    width: 80%
}

.item-columns-text small {
    color: var(--text-subtle)
}

.item-columns-text p {
    color: var(--text-main)!important;
    font-size: 1rem;
    font-weight: 600
}

.list-info-icons {
    font-size: .875rem;
    padding-top: 1rem
}

.list-info-icons div {
    display: inline-block;
    width: auto
}

.list-info-icons ul {
    margin: 0 0 0 4%;
    max-width: 600px;
    padding: 2rem 0 0;
    width: auto
}

.list-info-icons li {
    border-bottom: 1px solid var(--border-subtle);
    list-style: none;
    margin: 0;
    padding: 1rem 0
}

.list-info-icons strong {
    display: inline-block;
    min-width: 110px;
    padding: 0 1rem 0 .5rem;
    width: auto
}

@media (max-width: 767px) {
    .list-info-icons span {
        display:block
    }

    .title-center h1 {
        font-size: 1.5rem
    }

    .title-center h1 span {
        font-size: 2.5rem
    }

}
.banks-icons {
    background-position: 50%;
    background-repeat: no-repeat;
    background-size: 100%;
    background-size: 33px auto;
    border: 1px solid #e6e6e6;
    border-radius: 4px;
    display: inline-block;
    height: 24px;
    margin-bottom: 2px;
    margin-right: 2px;
    vertical-align: top;
    width: 35px
}

.bank8 {
    background-image: url(https://static.cdnpth.com/assets-core/img/payment/Bank8.svg)
}
.bank4 {
    background-image: url(https://static.cdnpth.com/assets-core/img/payment/Bank4.svg)
}
.bank6 {
    background-image: url(https://static.cdnpth.com/assets-core/img/payment/Bank6.svg)
}
.bank23 {
    background-image: url(https://static.cdnpth.com/assets-core/img/payment/Bank23.svg)
}
.bank5 {
    background-image: url(https://static.cdnpth.com/assets-core/img/payment/Bank5.svg)
}
.alert-warning {
    background-color: var(--bg-warning-subtle);
    border-color: var(--border-warning-subtle);
    color: var(---text-warning-strong);
    font-weight: 400;
}

.content-border h3 {
    font-size: 1rem !important;
    color: var(--text-strong);
	margin-bottom: 4px !important;
}
.content-border p{
    font-size: 1rem;
	margin-bottom: 4px !important;
    color: var(--text-strong);
	font-weight: 500;
}
.content-border p span {
    color: var(--text-subtle);
	font-weight: 400;
}
.content-border h3 span {
    color: var(--text-main);
    font-size: 1rem;
}
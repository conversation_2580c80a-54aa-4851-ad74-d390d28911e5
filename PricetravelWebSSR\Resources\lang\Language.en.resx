<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="about_us" xml:space="preserve">
    <value>About us</value>
  </data>
  <data name="add_room" xml:space="preserve">
    <value>Add another room</value>
  </data>
  <data name="adults" xml:space="preserve">
    <value>Adults</value>
  </data>
  <data name="apply" xml:space="preserve">
    <value>Apply</value>
  </data>
  <data name="arrival" xml:space="preserve">
    <value>Check-in</value>
  </data>
  <data name="author" xml:space="preserve">
    <value>{0}</value>
  </data>
  <data name="cancel_booking" xml:space="preserve">
    <value>Cancel reservation</value>
  </data>
  <data name="check_booking" xml:space="preserve">
    <value>Check Itinerary</value>
  </data>
  <data name="children" xml:space="preserve">
    <value>Children</value>
  </data>
  <data name="choose_destination" xml:space="preserve">
    <value>Choose your destination</value>
  </data>
  <data name="choose_origin" xml:space="preserve">
    <value>Choose your airport of origin</value>
  </data>
  <data name="choose_your_guests" xml:space="preserve">
    <value>Choose your guests</value>
  </data>
  <data name="contact_us" xml:space="preserve">
    <value>Contact us</value>
  </data>
  <data name="customer_button" xml:space="preserve">
    <value>Contact us</value>
  </data>
  <data name="customer_description" xml:space="preserve">
    <value>Attention 24 hours, 365 days a year</value>
  </data>
  <data name="customer_services" xml:space="preserve">
    <value>Customer service</value>
  </data>
  <data name="customer_title" xml:space="preserve">
    <value>Our experts at your service</value>
  </data>
  <data name="departure" xml:space="preserve">
    <value>Check-out</value>
  </data>
  <data name="description_app" xml:space="preserve">
    <value>Get inspired, choose the best hotels for your trip and enjoy the discounts we have in each destination. Get to know the new image and a more intuitive design. Faster and easier to use. Find better results by applying the filters you need. More complete galleries and descriptions to learn more about the hotels and their amazing rooms. Pay your reservations easily and safely. Take your itinerary wherever you go. Access your reservations without an internet connection. Create your account and access the history of your reservations. Personalized attention service available 24/7.</value>
  </data>
  <data name="destination_hotel" xml:space="preserve">
    <value>Destination, hotel, point of interest</value>
  </data>
  <data name="destination" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="error_pax" xml:space="preserve">
    <value>Choose the age of the children</value>
  </data>
  <data name="flight" xml:space="preserve">
    <value>Flights</value>
  </data>
  <data name="flights" xml:space="preserve">
    <value>Flights</value>
  </data>
  <data name="flight_title" xml:space="preserve">
    <value>Find the best flight</value>
  </data>
  <data name="flight_arrival" xml:space="preserve">
    <value>Check-out</value>
  </data>
  <data name="flight_return" xml:space="preserve">
    <value>Return</value>
  </data>
  <data name="fligth_description" xml:space="preserve">
    <value>Compare multiple airlines at the same time</value>
  </data>
  <data name="get_booking" xml:space="preserve">
    <value>Check reservation</value>
  </data>
  <data name="guests" xml:space="preserve">
    <value>Travelers</value>
  </data>
  <data name="travelers" xml:space="preserve">
    <value>Travelers</value>
  </data>
  <data name="header_call_us" xml:space="preserve">
    <value>Call us to reserve</value>
  </data>
  <data name="header_covid_information" xml:space="preserve">
    <value>COVID information</value>
  </data>
  <data name="header_title" xml:space="preserve">
    <value>Reservations and flexibility in changes.</value>
  </data>
  <data name="help" xml:space="preserve">
    <value>Help</value>
  </data>
  <data name="high_standard" xml:space="preserve">
    <value>High standard of cleanliness</value>
  </data>
  <data name="hotel" xml:space="preserve">
    <value>Hotels</value>
  </data>
  <data name="hoteles_title_destination" xml:space="preserve">
    <value>in {0}</value>
  </data>
  <data name="hotels" xml:space="preserve">
    <value>Hotels</value>
  </data>
  <data name="hotel_description" xml:space="preserve">
    <value>Reserve the best options of {0}. Book now and pay later at PriceTravel.com</value>
  </data>
  <data name="hotels_path" xml:space="preserve">
    <value>{0}/hotels-in-{1}_d{2}{3}</value>
  </data>
  <data name="hotels_title_header" xml:space="preserve">
    <value> {0}</value>
  </data>
  <data name="hotels_title_main" xml:space="preserve">
    <value>Hotels</value>
  </data>
  <data name="hotels_title_secondary" xml:space="preserve">
    <value> - {0}</value>
  </data>
  <data name="hotels_title_zone" xml:space="preserve">
    <value> - {0}</value>
  </data>
  <data name="hotel_path" xml:space="preserve">
    <value>{0}/{1}/hotel/{2}</value>
  </data>
  <data name="vacation-rental_path" xml:space="preserve">
    <value>{0}/{1}/vacation-rental/{2}</value>
  </data>
  <data name="hotel_register" xml:space="preserve">
    <value>Register hotel</value>
  </data>
  <data name="hotel_title" xml:space="preserve">
    <value>Hotel {0}</value>
  </data>
  <data name="how_old_are_you" xml:space="preserve">
    <value>How old are you?</value>
  </data>
  <data name="img" xml:space="preserve">
    <value>/assets/img/logo_{0}.png.webp</value>
  </data>
  <data name="img_home" xml:space="preserve">
    <value>https://s3.amazonaws.com/prod-single-spa.pricetravel.com.mx/assets/1.3.7/img/jumbotron_hotel.jpgg</value>
  </data>
  <data name="invoices" xml:space="preserve">
    <value>Electronic billing</value>
  </data>
  <data name="kid" xml:space="preserve">
    <value>Children</value>
  </data>
  <data name="legals" xml:space="preserve">
    <value>© Price Res, SAPI de CV. {0}. All rights reserved.</value>
  </data>
  <data name="links_co_country" xml:space="preserve">
    <value>Colombia</value>
  </data>
  <data name="links_co_phone" xml:space="preserve">
    <value>+52 (1) ************</value>
  </data>
  <data name="links_co_phone_format" xml:space="preserve">
    <value>8554378999</value>
  </data>
  <data name="links_mx_country" xml:space="preserve">
    <value>Mexico</value>
  </data>
  <data name="links_mx_phone" xml:space="preserve">
    <value>****** 437 8999</value>
  </data>
  <data name="links_mx_phone_format" xml:space="preserve">
    <value>18554378999</value>
  </data>
  <data name="links_oc_country" xml:space="preserve">
    <value>Others</value>
  </data>
  <data name="links_oc_phone" xml:space="preserve">
    <value>+52 (*************</value>
  </data>
  <data name="links_oc_phone_format" xml:space="preserve">
    <value>529988818879</value>
  </data>
  <data name="mail_contact_pricetravel" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="mail_contact_tiquetesbaratos" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="meta_description" xml:space="preserve">
    <value>Discover the best hotels at the best price on {0}.com. Our wide selection of hotels allows you to find the perfect option for your needs and budget. Plan your next trip with us and save money!</value>
  </data>
  <data name="meta_title" xml:space="preserve">
    <value>{0} - Travel easy, without excuses</value>
  </data>
  <data name="meta_title_login" xml:space="preserve">
    <value>{0} - Travel easy, without excuses</value>
  </data>
  <data name="meta_description_login" xml:space="preserve">
    <value>Discover the best hotels at the best price on {0}.com. Our wide selection of hotels allows you to find the perfect option for your needs and budget. Plan your next trip with us and save money!</value>
  </data>
  <data name="bank_deposit_transfer" xml:space="preserve">
    <value>Bank Deposit or Transfer</value>
  </data>
  <data name="bank_deposit_transfer_description" xml:space="preserve">
    <value>After completing your purchase, we'll provide instructions for making the payment at your preferred bank.</value>
  </data>
  <data name="pay_at_pdv" xml:space="preserve">
    <value>Payment at {0} point of sale</value>
  </data>
  <data name="pay_at_pdv_description" xml:space="preserve">
    <value>If you prefer, you can pay directly at any of our </value>
  </data>
  <data name="pdv_locations" xml:space="preserve">
    <value>physical point-of-sale locations</value>
  </data>
  <data name="modal_payment_aviable" xml:space="preserve">
    <value>Payment plans available</value>
  </data>
  <data name="modal_payment_bank" xml:space="preserve">
    <value>Participating banks</value>
  </data>
  <data name="modal_payment_card_description" xml:space="preserve">
    <value>A single payment with Visa and MasterCard</value>
  </data>
  <data name="modal_payment_card_title" xml:space="preserve">
    <value>Debit cards</value>
  </data>
  <data name="modal_payment_cash_attention" xml:space="preserve">
    <value>Places of attention {0}</value>
  </data>
  <data name="modal_payment_cash_description" xml:space="preserve">
    <value>With bank deposit / wire or in our:</value>
  </data>
  <data name="modal_payment_cash_title" xml:space="preserve">
    <value>Cash</value>
  </data>
  <data name="modal_payment_digital_wallet" xml:space="preserve">
    <value>Digital Wallets</value>
  </data>
  <data name="modal_payment_payments_online" xml:space="preserve">
    <value>Make payments online</value>
  </data>
  <data name="modal_payment_restriction" xml:space="preserve">
    <value>Restrictions apply</value>
  </data>
  <data name="modal_payment_month" xml:space="preserve">
    <value>Monthly</value>
  </data>
  <data name="modal_payment_other_description" xml:space="preserve">
    <value>We have more forms of payment:</value>
  </data>
  <data name="modal_payment_other_title" xml:space="preserve">
    <value>Don't you have a credit card?</value>
  </data>
  <data name="modal_payment_title" xml:space="preserve">
    <value>Payment Methods</value>
  </data>
  <data name="modal_payment_until" xml:space="preserve">
    <value>Up to </value>
  </data>
  <data name="modal_payment_warning" xml:space="preserve">
    <value>Payment options will depend on the service selected and the value of the purchase. Minimum purchase may apply.</value>
  </data>
  <data name="account" xml:space="preserve">
    <value>Account</value>
  </data>
  <data name="no_results_list" xml:space="preserve">
    <value>No results</value>
  </data>
  <data name="origin" xml:space="preserve">
    <value>Origin</value>
  </data>
  <data name="our_history" xml:space="preserve">
    <value>Our history</value>
  </data>
  <data name="packages" xml:space="preserve">
    <value>Hotel + Flight</value>
  </data>
  <data name="packages_name" xml:space="preserve">
    <value>Packages</value>
  </data>
  <data name="package_description" xml:space="preserve">
    <value>Exclusive prices and exceptional discounts</value>
  </data>
  <data name="package_title" xml:space="preserve">
    <value>Hotels at special prices</value>
  </data>
  <data name="passengers" xml:space="preserve">
    <value>Passengers</value>
  </data>
  <data name="payment_button" xml:space="preserve">
    <value>Check participating cards</value>
  </data>
  <data name="consult_tyc" xml:space="preserve">
    <value>Refer to terms and conditions.</value>
  </data>
  <data name="payment_description" xml:space="preserve">
    <value>Months without interest with a credit card</value>
  </data>
  <data name="payment_title" xml:space="preserve">
    <value>Pay with bank cards</value>
  </data>
  <data name="phone" xml:space="preserve">
    <value>55 8663 8825</value>
  </data>
  <data name="phone_phone" xml:space="preserve">
    <value>58 663-8843</value>
  </data>
  <data name="phone_phone_format" xml:space="preserve">
    <value>************</value>
  </data>
  <data name="phone_phone_whatsapp" xml:space="preserve">
    <value>************</value>
  </data>
  <data name="press_room" xml:space="preserve">
    <value>Press room</value>
  </data>
  <data name="pricetravel_magazine" xml:space="preserve">
    <value>{0} Magazine</value>
  </data>
  <data name="privacy_notice" xml:space="preserve">
    <value>Notice of Privacy</value>
  </data>
  <data name="privacy_terms" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="providers" xml:space="preserve">
    <value>Providers</value>
  </data>
  <data name="rapd_button" xml:space="preserve">
    <value>Discover how it works</value>
  </data>
  <data name="rapd_description" xml:space="preserve">
    <value>Earn time to pay for your accommodation</value>
  </data>
  <data name="rapd_title" xml:space="preserve">
    <value>Book now, pay later</value>
  </data>
  <data name="ratingCount" xml:space="preserve">
    <value>619</value>
  </data>
  <data name="remove" xml:space="preserve">
    <value>Remove</value>
  </data>
  <data name="room" xml:space="preserve">
    <value>Room</value>
  </data>
  <data name="roundtrip_tiquetesbaratos" xml:space="preserve">
    <value>Roundtrip</value>
  </data>
  <data name="search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="select_date_arrival" xml:space="preserve">
    <value>Choose your departure date</value>
  </data>
  <data name="select_date_departure" xml:space="preserve">
    <value>Choose your return date</value>
  </data>
  <data name="select_destination" xml:space="preserve">
    <value>Select your destination</value>
  </data>
  <data name="select_destination_equals" xml:space="preserve">
    <value>The origin is the same as the destination</value>
  </data>
  <data name="select_origin" xml:space="preserve">
    <value>Select your airport of origin</value>
  </data>
  <data name="separator" xml:space="preserve">
    <value> - </value>
  </data>
  <data name="separator_b" xml:space="preserve">
    <value> | </value>
  </data>
  <data name="sic_resolution" xml:space="preserve">
    <value>SIC Resolution</value>
  </data>
  <data name="terms_conditions" xml:space="preserve">
    <value>Terms and Conditions</value>
  </data>
  <data name="coupon_message" xml:space="preserve">
    <value>Coupon</value>
  </data>
  <data name="travel_agency" xml:space="preserve">
    <value>Travel agency</value>
  </data>
  <data name="update_booking" xml:space="preserve">
    <value>Modify reservation</value>
  </data>
  <data name="up_to_17_years" xml:space="preserve">
    <value>(Up to 17 years)</value>
  </data>
  <data name="pax_up_to_17_years" xml:space="preserve">
    <value>Age (0-17)</value>
  </data>
  <data name="title_error_paxes" xml:space="preserve">
    <value>Maximum 9 passengers per reservation.</value>
  </data>
  <data name="error_paxes" xml:space="preserve">
    <value>To add more passengers, make another reservation or contact customer service.</value>
  </data>
  <data name="view" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="years_old" xml:space="preserve">
    <value>years</value>
  </data>
  <data name="zone" xml:space="preserve">
    <value>Zone</value>
  </data>
  <data name="flight_title_banner_tiquetesbaratos" xml:space="preserve">
    <value>Discover your next destination in one place</value>
  </data>
  <data name="flight_title_banner_pricetravel" xml:space="preserve">
    <value>Discover your next destination in one place</value>
  </data>
  <data name="hotel_title_banner" xml:space="preserve">
    <value>Book now, pay later | flexible changes | free cancellation at most hotels</value>
  </data>
  <data name="current_offers" xml:space="preserve">
    <value>Consult terms and conditions at pricetravel.com/en/deals/current-offers/</value>
  </data>
  <data name="packages_title_banner" xml:space="preserve">
    <value>Book now, pay later | flexible changes | free cancellation at most hotels</value>
  </data>
  <data name="check_reservation" xml:space="preserve">
    <value>Check Itinerary</value>
  </data>
  <data name="check_reservationtb" xml:space="preserve">
    <value>Check your reservation</value>
  </data>
  <data name="confirm_password" xml:space="preserve">
    <value>Confirm Password</value>
  </data>
  <data name="create_account" xml:space="preserve">
    <value>Create Account</value>
  </data>
  <data name="create_yout_password" xml:space="preserve">
    <value>Create your password</value>
  </data>
  <data name="email_string" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="enter_email" xml:space="preserve">
    <value>Enter your email</value>
  </data>
  <data name="enter_email2" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="enter_email2Valid" xml:space="preserve">
    <value>Use <NAME_EMAIL></value>
  </data>
  <data name="enter_email2ValidEmpty" xml:space="preserve">
    <value>Please enter your email.</value>
  </data>
  <data name="enter_your_password" xml:space="preserve">
    <value>Enter your password</value>
  </data>
  <data name="forgot_password" xml:space="preserve">
    <value>I forgot my password</value>
  </data>
  <data name="forgot_password_string_1" xml:space="preserve">
    <value>Don't worry, provide your email and we'll tell you how to reset it.</value>
  </data>
  <data name="login_continue" xml:space="preserve">
    <value>Continue</value>
  </data>
  <data name="no_received_yet" xml:space="preserve">
    <value>Still not receiving the email?</value>
  </data>
  <data name="password_set_string_1" xml:space="preserve">
    <value>{0} password for the account associated with the email</value>
  </data>
  <data name="password_string" xml:space="preserve">
    <value>Password</value>
  </data>
  <data name="resend" xml:space="preserve">
    <value>Resend email</value>
  </data>
  <data name="send" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="start_session_or_create" xml:space="preserve">
    <value>Sign in or create an account</value>
  </data>
  <data name="use_other_login_methods" xml:space="preserve">
    <value>or sign in with</value>
  </data>
  <data name="min_8_letters_required" xml:space="preserve">
    <value>Must have a minimum of 8 alphanumeric characters</value>
  </data>
  <data name="password_weak" xml:space="preserve">
    <value>Very weak password</value>
  </data>
  <data name="same_as" xml:space="preserve">
    <value>https://www.instagram.com/pricetravel/,https://www.facebook.com/PriceTravel/,https://www.youtube.com/user/PriceTravel,https://twitter.com/pricetravel,https://mx.linkedin.com/company/pricetravel</value>
  </data>
  <data name="customer_service" xml:space="preserve">
    <value>customer service</value>
  </data>
  <data name="operating_system" xml:space="preserve">
    <value>Windows 7, Windows 8, Windows 10, Windows 11, OSX, ANDROID, IOS</value>
  </data>
  <data name="rating_value" xml:space="preserve">
    <value>4.5</value>
  </data>
  <data name="covid_information" xml:space="preserve">
    <value>COVID-19 information</value>
  </data>
  <data name="title_banner_pricetravel" xml:space="preserve">
    <value>Save big on your next trip</value>
  </data>
  <data name="title_banner_tiquetesbaratos" xml:space="preserve">
    <value>Colombia, I will travel for you.</value>
  </data>
	<data name="payment-option-depend_tiquetesbaratos" xml:space="preserve">
    <value>*Installment payment options depend on the selected service and the purchase amount.</value>
  </data>
	<data name="payment-option-depend_pricetravel" xml:space="preserve">
    <value>*Installment payment options depend on the service selected and the value of the purchase</value>
  </data>
	<data name="payment-title_pricetravel" xml:space="preserve">
    <value>Secure Online Payment - PriceTravel</value>
  </data>
	<data name="payment-title_tiquetesbaratos" xml:space="preserve">
    <value>Secure Online Payment - Tiquetes Baratos</value>
  </data>
	<data name="secure-payments" xml:space="preserve">
    <value>Convenient and secure payments</value>
  </data>
	<data name="payOnline" xml:space="preserve">
    <value>Online payment</value>
  </data>
	<data name="check-methods-pay" xml:space="preserve">
    <value>Check our payment methods</value>
  </data>
  <data name="all_world" xml:space="preserve">
    <value>Rest of the world</value>
  </data>
  <data name="call_us_edit_reservation" xml:space="preserve">
    <value>For changes to your reservation, have your reservation number ready</value>
  </data>
  <data name="colombia" xml:space="preserve">
    <value>Colombia</value>
  </data>
  <data name="header_covid_url" xml:space="preserve">
    <value>https://info.pricetravel.com/informacion-viaje/</value>
  </data>
  <data name="hi" xml:space="preserve">
    <value>Hello</value>
  </data>
  <data name="legals_login_one" xml:space="preserve">
    <value>By signing in or creating an account, you agree with our</value>
  </data>
  <data name="legals_login_three" xml:space="preserve">
    <value>by {0}</value>
  </data>
  <data name="legals_login_two" xml:space="preserve">
    <value>and the</value>
  </data>
  <data name="links_us_phone" xml:space="preserve">
    <value>(*************</value>
  </data>
  <data name="links_us_phone_format" xml:space="preserve">
    <value>************</value>
  </data>
  <data name="login_passwords_error" xml:space="preserve">
    <value>Passwords do not match</value>
  </data>
  <data name="log_out" xml:space="preserve">
    <value>Sign out</value>
  </data>
  <data name="mexico" xml:space="preserve">
    <value>México</value>
  </data>
  <data name="rapd_benefits_one" xml:space="preserve">
    <value>Secure your accommodation and pay before your travel date</value>
  </data>
  <data name="rapd_benfits_three" xml:space="preserve">
    <value>Flexibility to cancel</value>
  </data>
  <data name="rapd_modal_title" xml:space="preserve">
    <value>A new benefit for you</value>
  </data>
  <data name="rapd_step_one_part" xml:space="preserve">
    <value>Book now with</value>
  </data>
  <data name="rapd_step_two" xml:space="preserve">
    <value>You will receive the confirmation of your reservation with a {0}, which will help you make your payments.</value>
  </data>
  <data name="rapd_find" xml:space="preserve">
    <value>Where and how to pay?</value>
  </data>
  <data name="rapd_pay_to_online" xml:space="preserve">
    <value>Pay online</value>
  </data>
  <data name="rapd_pay_to_acept" xml:space="preserve">
    <value>We accept all cards:</value>
  </data>
  <data name="rapd_pay_online" xml:space="preserve">
    <value>Pay by phone</value>
  </data>
  <data name="rapd_point_sales_location" xml:space="preserve">
    <value>Locate your point of sale</value>
  </data>
  <data name="rapd_point_sales" xml:space="preserve">
    <value>Price Travel Points of Sale</value>
  </data>
  <data name="rapd_title_pay" xml:space="preserve">
    <value>We will send you a link so you can make your secure payment online. You can make one or more payments until you settle the total of your reservation.</value>
  </data>
  <data name="rapd_step_two_localizer" xml:space="preserve">
    <value>locator number</value>
  </data>
  <data name="rapd_zero" xml:space="preserve">
    <value> and pay later</value>
  </data>
  <data name="to_modify" xml:space="preserve">
    <value>To modify</value>
  </data>
  <data name="usa" xml:space="preserve">
    <value>E.U.A / Canadá</value>
  </data>
  <data name="detail_location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="gallery_location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="detail_rooms" xml:space="preserve">
    <value>Rooms</value>
  </data>
  <data name="detail_services" xml:space="preserve">
    <value>Service</value>
  </data>
  <data name="hotel_services_more" xml:space="preserve">
    <value>Show all the services</value>
  </data>
  <data name="important_services" xml:space="preserve">
    <value>Policies</value>
  </data>
  <data name="important_services_label" xml:space="preserve">
    <value>Need-to-know information for guests at this property</value>
  </data>
  <data name="hotel_services_less" xml:space="preserve">
    <value>Show less services</value>
  </data>
  <data name="read_more" xml:space="preserve">
    <value>Read more</value>
  </data>
  <data name="list_free_cancel" xml:space="preserve">
    <value>FREE cancellation</value>
  </data>
  <data name="list_hotels" xml:space="preserve">
    <value>Hotels</value>
  </data>
  <data name="list_init_text" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="list_room_per_night" xml:space="preserve">
    <value>Room per night</value>
  </data>
  <data name="list_search_best_options" xml:space="preserve">
    <value>Looking for the best options</value>
  </data>
  <data name="list_unavailability_message_one" xml:space="preserve">
    <value>No availability for these dates.</value>
  </data>
  <data name="list_unavailability_message_one_detail" xml:space="preserve">
    <value>Try checking other dates</value>
  </data>
  <data name="list_unavailability_btn_detail" xml:space="preserve">
    <value>See upcoming dates</value>
  </data>
  <data name="rapd_more_info" xml:space="preserve">
    <value>More information</value>
  </data>
  <data name="list_pagination_range" xml:space="preserve">
    <value>(from) - (to) of (total) hotels</value>
  </data>
  <data name="list_taxes_included" xml:space="preserve">
    <value>Taxes included</value>
  </data>
  <data name="edit_search" xml:space="preserve">
    <value>Search</value>
  </data>
  <data name="home" xml:space="preserve">
    <value>Home</value>
  </data>
  <data name="hotels_in" xml:space="preserve">
    <value>Hotels in {0}</value>
  </data>
  <data name="hotels_description" xml:space="preserve">
    <value>Reserve the best options of :title. Book now and pay later months without interest at {0}.com</value>
  </data>
  <data name="meta.hoteles_title_destination" xml:space="preserve">
    <value> in {0}</value>
  </data>
  <data name="meta.hotels_description" xml:space="preserve">
    <value>Reserve the best options of {0}. Book now and pay later months without interest at {1}.com</value>
  </data>
  <data name="meta.hotels_title_header" xml:space="preserve">
    <value> {0}</value>
  </data>
  <data name="meta.hotels_title_main" xml:space="preserve">
    <value>Hotels</value>
  </data>
  <data name="meta.hotels_title_secondary" xml:space="preserve">
    <value> - {0}</value>
  </data>
  <data name="meta.hotels_title_zone" xml:space="preserve">
    <value> - {0}</value>
  </data>
  <data name="meta.view" xml:space="preserve">
    <value>View</value>
  </data>
  <data name="meta.zone" xml:space="preserve">
    <value>Zone</value>
  </data>
  <data name="by_night_since" xml:space="preserve">
    <value>Room per night</value>
  </data>
  <data name="detail_taxes_included" xml:space="preserve">
    <value>Taxes included</value>
  </data>
  <data name="see_active_banks" xml:space="preserve">
    <value>See participating banks</value>
  </data>
  <data name="hotel_has_free_cancel" xml:space="preserve">
    <value>This hotel has FREE cancellation</value>
  </data>
  <data name="about_hotel" xml:space="preserve">
    <value>About hotel</value>
  </data>
  <data name="all_inclusive_plan" xml:space="preserve">
    <value>Hotel all-inclusive meal plan</value>
  </data>
  <data name="all_inclusive_plan_detail" xml:space="preserve">
    <value>All inclusive details:</value>
  </data>
  <data name="hotel_benefits" xml:space="preserve">
    <value>Hotel Benefits</value>
  </data>
  <data name="hotel_location" xml:space="preserve">
    <value>Location of the hotel</value>
  </data>
  <data name="hotel_services" xml:space="preserve">
    <value>Hotel services</value>
  </data>
  <data name="included_services" xml:space="preserve">
    <value>Included services</value>
  </data>
  <data name="it_has" xml:space="preserve">
    <value>It has</value>
  </data>
  <data name="restaurant_bars" xml:space="preserve">
    <value>Restaurants &amp; Bars</value>
  </data>
  <data name="restaurant" xml:space="preserve">
    <value>restaurant</value>
  </data>
  <data name="restaurants" xml:space="preserve">
    <value>restaurants</value>
  </data>
  <data name="services_with_extra_charge" xml:space="preserve">
    <value>Services with extra charge</value>
  </data>
  <data name="show_all_hotels_in" xml:space="preserve">
    <value>See all hotels in</value>
  </data>
  <data name="show_map" xml:space="preserve">
    <value>Map</value>
  </data>
  <data name="show_list" xml:space="preserve">
    <value>Show on list</value>
  </data>
  <data name="no_results_map" xml:space="preserve">
    <value>No results found.</value>
  </data>
  <data name="change_criteria_map" xml:space="preserve">
    <value>We suggest you change your search criteria.</value>
  </data>
  <data name="show_more_info" xml:space="preserve">
    <value>View more information</value>
  </data>
  <data name="hotel_notices" xml:space="preserve">
    <value>Hotel announcements</value>
  </data>
  <data name="detail_search_rooms" xml:space="preserve">
    <value>Loading rooms</value>
  </data>
  <data name="show_less_info" xml:space="preserve">
    <value>Show less information</value>
  </data>
  <data name="amenities_and_checkin" xml:space="preserve">
    <value>Room detail</value>
  </data>
  <data name="what_inclusive" xml:space="preserve">
    <value>What includes?</value>
  </data>
  <data name="beds" xml:space="preserve">
    <value>Beds</value>
  </data>
  <data name="capacity" xml:space="preserve">
    <value>Capacity</value>
  </data>
  <data name="foods" xml:space="preserve">
    <value>Meals</value>
  </data>
  <data name="free_cancellation" xml:space="preserve">
    <value>Free cancellation</value>
  </data>
  <data name="kids_free" xml:space="preserve">
    <value>Kids free</value>
  </data>
  <data name="no_refundable" xml:space="preserve">
    <value>Not refundable</value>
  </data>
  <data name="pay_after" xml:space="preserve">
    <value>Pay later</value>
  </data>
  <data name="pay_now" xml:space="preserve">
    <value>Pay now</value>
  </data>
  <data name="room_by_night" xml:space="preserve">
    <value>Room per night</value>
  </data>
  <data name="recomendation_section" xml:space="preserve">
    <value>BEST PRICE ACCORDING TO YOUR SEARCH</value>
  </data>
  <data name="view_more_rooms" xml:space="preserve">
    <value>View all rooms</value>
  </data>
  <data name="reservation_now" xml:space="preserve">
    <value>Book now</value>
  </data>
  <data name="room_table_header_price_by_night" xml:space="preserve">
    <value>Price per night</value>
  </data>
  <data name="room_table_header_options" xml:space="preserve">
    <value>Your options</value>
  </data>
  <data name="room_table_header_rooms_select" xml:space="preserve">
    <value>Select rooms</value>
  </data>
  <data name="room_table_recommeded_for" xml:space="preserve">
    <value>Recommended for</value>
  </data>
  <data name="room_table_rapd_title" xml:space="preserve">
    <value>You can pay later</value>
  </data>
  <data name="room_table_selection_rapd_option" xml:space="preserve">
    <value>With option to</value>
  </data>
  <data name="room_table_save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="room_table_saved" xml:space="preserve">
    <value>You saved</value>
  </data>
  <data name="room_table_save_sesion" xml:space="preserve">
    <value>when logging in</value>
  </data>
  <data name="room_table_selection" xml:space="preserve">
    <value>Choose your desired room types</value>
  </data>
  <data name="room_table_selection_confir" xml:space="preserve">
    <value>Instant confirmation</value>
  </data>
  <data name="room_table_selection_package" xml:space="preserve">
    <value>Your package:</value>
  </data>
  <data name="room_table_header_room_type" xml:space="preserve">
    <value>Room type</value>
  </data>
  <data name="room_table_header_number_of_persons" xml:space="preserve">
    <value>Travelers</value>
  </data>
  <data name="select_when_want_pay" xml:space="preserve">
    <value>Choose when you want to pay</value>
  </data>
  <data name="since_to" xml:space="preserve">
    <value>Until the </value>
  </data>
  <data name="thats_include" xml:space="preserve">
    <value>What includes?</value>
  </data>
  <data name="taxes_no_included" xml:space="preserve">
    <value>Taxes not included</value>
  </data>
  <data name="amenities" xml:space="preserve">
    <value>About</value>
  </data>
  <data name="book" xml:space="preserve">
    <value>Reserve</value>
  </data>
  <data name="choose_when_you_prefer_to_pay" xml:space="preserve">
    <value>Choose when you want to pay</value>
  </data>
  <data name="hotel.do_you_need_help" xml:space="preserve">
    <value>Need help? call us</value>
  </data>
  <data name="hotel.per_night" xml:space="preserve">
    <value>Room per night</value>
  </data>
  <data name="more_photos" xml:space="preserve">
    <value>more photos</value>
  </data>
  <data name="not_available" xml:space="preserve">
    <value>Not available for these dates</value>
  </data>
  <data name="paylater" xml:space="preserve">
    <value>Pay later</value>
  </data>
  <data name="paynow" xml:space="preserve">
    <value>Pay now</value>
  </data>
  <data name="rooms_in_hotel" xml:space="preserve">
    <value>Choose your room</value>
  </data>
  <data name="rooms_in_hotel_mobile" xml:space="preserve">
    <value>Choose your room</value>
  </data>
  <data name="room_per_night" xml:space="preserve">
    <value>Room per night</value>
  </data>
  <data name="show_more_rooms" xml:space="preserve">
    <value>See more rooms</value>
  </data>
  <data name="taxes_and_charges" xml:space="preserve">
    <value>taxes</value>
  </data>
     <data name="taxes_and_charges_fee" xml:space="preserve">
    <value>taxes and fees</value>
  </data>
  <data name="try_checking_other_dates" xml:space="preserve">
    <value>Try changing the dates or call toll free</value>
  </data>
  <data name="free" xml:space="preserve">
    <value>Free</value>
  </data>
  <data name="hotel_free_cancellation" xml:space="preserve">
    <value>This hotel has</value>
  </data>
  <data name="more_information" xml:space="preserve">
    <value>More information</value>
  </data>
  <data name="radp_create_book" xml:space="preserve">
    <value>Create your itinerary paying $0.00</value>
  </data>
  <data name="didnt_find_the_right_hotel" xml:space="preserve">
    <value>Didn't find the right hotel?</value>
  </data>
  <data name="see_more_option" xml:space="preserve">
    <value>See more hotel options</value>
  </data>
  <data name="try_looking_for" xml:space="preserve">
    <value>Try looking for more hotel options in</value>
  </data>
  <data name="list_call_us" xml:space="preserve">
    <value>Call us</value>
  </data>
  <data name="list_card_tax" xml:space="preserve">
    <value>taxes</value>
  </data>
  <data name="list_card_tax_no_include" xml:space="preserve">
    <value>Taxes not included</value>
  </data>
  <data name="list_filter_btn_search" xml:space="preserve">
    <value>ej: city express</value>
  </data>
  <data name="list_filter_input_search" xml:space="preserve">
    <value>By accommodation name</value>
  </data>
  <data name="list_noresult_call_us" xml:space="preserve">
    <value>You can also call us toll-free to check availability</value>
  </data>
  <data name="list_noresult_change_search" xml:space="preserve">
    <value>Try changing your search from:</value>
  </data>
  <data name="list_noresult_dates" xml:space="preserve">
    <value>Dates</value>
  </data>
  <data name="list_noresult_destination" xml:space="preserve">
    <value>Destination</value>
  </data>
  <data name="list_noresult_not_found" xml:space="preserve">
    <value>We did not find results with the selected filters</value>
  </data>
  <data name="list_noresult_not_found_search" xml:space="preserve">
    <value>No results for your search</value>
  </data>
  <data name="list_noresult_remove_filter" xml:space="preserve">
    <value>Remove all filters</value>
  </data>
  <data name="list_noresult_update_filter" xml:space="preserve">
    <value>Adjust the selected filters to update the list.</value>
  </data>
  <data name="list_paginator_next" xml:space="preserve">
    <value>Next</value>
  </data>
  <data name="list_paginator_prev" xml:space="preserve">
    <value>Previous </value>
  </data>
  <data name="list_show_method_payment" xml:space="preserve">
    <value>See our payment methods</value>
  </data>
  <data name="error_fly" xml:space="preserve">
    <value>Flights</value>
  </data>
  <data name="error_hotel" xml:space="preserve">
    <value>Hotels</value>
  </data>
  <data name="error_internal_error" xml:space="preserve">
    <value>Internal server error</value>
  </data>
  <data name="error_main_page" xml:space="preserve">
    <value>Homepage</value>
  </data>
  <data name="error_not_found" xml:space="preserve">
    <value>Page not found</value>
  </data>
  <data name="error_offer" xml:space="preserve">
    <value>offers</value>
  </data>
  <data name="error_package" xml:space="preserve">
    <value>Packages</value>
  </data>
  <data name="error_sections" xml:space="preserve">
    <value>Visit other sections</value>
  </data>
  <data name="rapd_benefits_three" xml:space="preserve">
    <value>Flexibility to cancel</value>
  </data>
  <data name="filter" xml:space="preserve">
    <value>Filters</value>
  </data>
  <data name="edit" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="alert_country_name" xml:space="preserve">
    <value>México</value>
  </data>
  <data name="alert_country_phone" xml:space="preserve">
    <value>55 8663 8833</value>
  </data>
  <data name="alert_information_message" xml:space="preserve">
    <value>We want to help you find options that fit your plans and budget</value>
  </data>
  <data name="alert_long_stay_message" xml:space="preserve">
    <value>Call us for stays longer than 30 nights</value>
  </data>
  <data name="stay" xml:space="preserve">
    <value>Stay</value>
  </data>
  <data name="alert_rest_of_world_name" xml:space="preserve">
    <value>Rest of the world</value>
  </data>
  <data name="alert_rest_of_world_phone" xml:space="preserve">
    <value>(*************</value>
  </data>
  <data name="contact_us_since" xml:space="preserve">
    <value>Contact us from</value>
  </data>
  <data name="something_wrong" xml:space="preserve">
    <value>Oh no. Something went wrong</value>
  </data>
  <data name="something_wrong_information" xml:space="preserve">
    <value>The information for this hotel is not available, please try again later or contact us at:</value>
  </data>
  <data name="congratulations" xml:space="preserve">
    <value>Congratulations!</value>
  </data>
  <data name="loading_benefits" xml:space="preserve">
    <value>Loading benefits...</value>
  </data>
  <data name="login_congrats_description" xml:space="preserve">
    <value>You have obtained preferential rates, &lt;strong&gt;explore now and enjoy the special prices we have for you!&lt;/strong&gt;</value>
  </data>
  <data name="login_loading_description" xml:space="preserve">
    <value>You are accessing the &lt;span class="text-primary"&gt; exclusive rates &lt;/span&gt;for {0} members</value>
  </data>
  <data name="see_hotels" xml:space="preserve">
    <value>See hotels</value>
  </data>
  <data name="unavailability_by_the_moment" xml:space="preserve">
    <value>At the moment we do not have availability</value>
  </data>
  <data name="we_suggest_more_options" xml:space="preserve">
    <value>We recommend you to see more options in this destination.</value>
  </data>
  <data name="see_all_hotels" xml:space="preserve">
    <value>See all hotels in</value>
  </data>
  <data name="filter_results" xml:space="preserve">
    <value>Filter by</value>
  </data>
  <data name="close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="close_feat_fil" xml:space="preserve">
    <value>Show</value>
  </data>
  <data name="close_feat_fil_res" xml:space="preserve">
    <value>results</value>
  </data>
  <data name="select_kid_age" xml:space="preserve">
    <value>Choose your age</value>
  </data>
  <data name="link_covid" xml:space="preserve">
    <value>COVID information</value>
  </data>
  <data name="age" xml:space="preserve">
    <value>Age</value>
  </data>
  <data name="kid_age" xml:space="preserve">
    <value>Child age</value>
  </data>
  <data name="label_more_x_pax" xml:space="preserve">
    <value>On trips of more than 5 people, we recommend adding another room to have more lodging options.</value>
  </data>
  <data name="where_you_want_travel" xml:space="preserve">
    <value>Where do you want to travel?</value>
  </data>
  <data name="rapd_title_invalid" xml:space="preserve">
    <value>Book now, pay later </value>
  </data>
  <data name="recent_search" xml:space="preserve">
    <value>Recent searches</value>
  </data>
  <data name="most_popular_origin" xml:space="preserve">
    <value>Most popular origins</value>
  </data>
  <data name="most_popular_destination" xml:space="preserve">
    <value>Most popular destinations</value>
  </data>
  <data name="choose_your_passengers" xml:space="preserve">
    <value>Choose your passengers</value>
  </data>
  <data name="notificationKids" xml:space="preserve">
    <value>There must be at least 1 passenger (Adult) for each baby in arms.</value>
  </data>
  <data name="benefits" xml:space="preserve">
    <value>Benefits</value>
  </data>
  <data name="how" xml:space="preserve">
    <value>How does it work?</value>
  </data>
  <data name="your_search" xml:space="preserve">
    <value>Your search</value>
  </data>
  <data name="copied_link" xml:space="preserve">
    <value>Link copied!</value>
  </data>
  <data name="show_less_rooms" xml:space="preserve">
    <value>See fewer rooms</value>
  </data>
  <data name="appled_filters" xml:space="preserve">
    <value>Applied filters</value>
  </data>
  <data name="continue_with_searchpricetravel" xml:space="preserve">
    <value>Continue with your search</value>
  </data>
  <data name="continue_with_searchtiquetesbaratos" xml:space="preserve">
    <value>Continue with your recent searches</value>
  </data>
  <data name="people" xml:space="preserve">
    <value>People</value>
  </data>
  <data name="find_that_search" xml:space="preserve">
    <value>Discover your ideal destination</value>
  </data>
  <data name="with_credit_card" xml:space="preserve">
    <value> with credit card.</value>
  </data>
  <data name="get_our_payment_methods" xml:space="preserve">
    <value>Check our payment methods</value>
  </data>
  <data name="get_our_payment_methodstb" xml:space="preserve">
    <value>Check out our payment methods</value>
  </data>
  <data name="get_until" xml:space="preserve">
    <value>Get up to</value>
  </data>
  <data name="pay_in_msi" xml:space="preserve">
    <value>Pay MONTHS WITHOUT INTEREST</value>
  </data>
  <data name="pay_best_options" xml:space="preserve">
    <value>Book with the best payment options</value>
  </data>
  <data name="pay_best_optionstb" xml:space="preserve">
    <value>Comfortable and secure payments</value>
  </data>
  <data name="we_accept_all_cards" xml:space="preserve">
    <value>We accept all credit cards and debit cards</value>
  </data>
  <data name="msi_terms_conditions" xml:space="preserve">
    <value>*msi=Months without interest, see terms and conditions</value>
  </data>
  <data name="promotion_description" xml:space="preserve">
    <value>Save big with our great deals</value>
  </data>
  <data name="promotion_title" xml:space="preserve">
    <value>Save now, Travel any time</value>
  </data>
  <data name="titleReviews" xml:space="preserve">
    <value>Reviews</value>
  </data>
  <data name="titleMdlReviews" xml:space="preserve">
    <value>Rating</value>
  </data>
  <data name="promotion_header_home" xml:space="preserve">
    <value>¡Festeja el mes patrio viajando con nosotros!</value>
  </data>
  <data name="msi_text_pricetravel" xml:space="preserve">
    <value>Flexible changes</value>
  </data>
  <data name="msi_text_tiquetesbaratos" xml:space="preserve">
    <value>Pay in installments</value>
  </data>
  <data name="free_cancel_in_hotels" xml:space="preserve">
    <value>Free cancellation at most hotels</value>
  </data>
  <data name="features" xml:space="preserve">
    <value>Room</value>
  </data>
  <data name="aditional_info" xml:space="preserve">
    <value>Additional Information</value>
  </data>
  <data name="featured" xml:space="preserve">
    <value>Amenities</value>
  </data>
  <data name="enter_txt" xml:space="preserve">
    <value>Check in time</value>
  </data>
  <data name="exit_txt" xml:space="preserve">
    <value>Departure time</value>
  </data>
  <data name="check_in" xml:space="preserve">
    <value>Check-in</value>
  </data>
  <data name="important_info" xml:space="preserve">
    <value>Important information</value>
  </data>
  <data name="phone_phone_whatsapp_format" xml:space="preserve">
    <value>55 7969 3165</value>
  </data>
  <data name="what_papd" xml:space="preserve">
    <value>How does it works?</value>
  </data>
  <data name="or_back_to" xml:space="preserve">
    <value>or go back to</value>
  </data>
  <data name="of_connector" xml:space="preserve">
    <value>of</value>
  </data>
  <data name="share_txt" xml:space="preserve">
    <value>Share</value>
  </data>
  <data name="best_price_guarent" xml:space="preserve">
    <value>Best price guarantee</value>
  </data>
  <data name="share_hotel" xml:space="preserve">
    <value>Share hotel</value>
  </data>
  <data name="share_fb" xml:space="preserve">
    <value>Share on facebook</value>
  </data>
  <data name="share_whats" xml:space="preserve">
    <value>Share on WhatsApp</value>
  </data>
  <data name="copy_link" xml:space="preserve">
    <value>Copy link</value>
  </data>
  <data name="cancellation" xml:space="preserve">
    <value>cancellation</value>
  </data>
  <data name="plan_txt" xml:space="preserve">
    <value>Meal Plan</value>
  </data>
  <data name="from_txt" xml:space="preserve">
    <value>from</value>
  </data>
  <data name="detail_search_reviews" xml:space="preserve">
    <value>Loading reviews</value>
  </data>
  <data name="location_text" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="show_rooms_text" xml:space="preserve">
    <value>View rooms</value>
  </data>
  <data name="titleMeta" xml:space="preserve">
    <value>{0}</value>
  </data>
  <data name="show_more_info_reviews" xml:space="preserve">
    <value>View more</value>
  </data>
  <data name="cleanliness" xml:space="preserve">
    <value>Cleanliness</value>
  </data>
  <data name="confort" xml:space="preserve">
    <value>Comfort</value>
  </data>
  <data name="location" xml:space="preserve">
    <value>Location</value>
  </data>
  <data name="property_conditions" xml:space="preserve">
    <value>Property conditions &amp; facilities</value>
  </data>
  <data name="services" xml:space="preserve">
    <value>Services</value>
  </data>
  <data name="show_less_info_reviews" xml:space="preserve">
    <value>View less</value>
  </data>
  <data name="staff_&amp;_service" xml:space="preserve">
    <value>Staff &amp; service</value>
  </data>
  <data name="h1_home" xml:space="preserve">
    <value>Book flights and hotels at the best prices at {0}, your travel agency.</value>
  </data>
  <data name="h1_hotels" xml:space="preserve">
    <value>Offers on hotels with {0}, your travel agency.</value>
  </data>
  <data name="h1_fllights" xml:space="preserve">
    <value>Flights at the best prices at {0}, your travel agency.</value>
  </data>
  <data name="h1_packages" xml:space="preserve">
    <value>Book flights and hotels at the best prices at {0}, your travel agency.</value>
  </data>
  <data name="meta_title_home" xml:space="preserve">
    <value>{0} | Your online travel agency for booking hotels at the best price</value>
  </data>
  <data name="meta_title_flights" xml:space="preserve">
    <value>Travel agency, booking flights, hotels and packages</value>
  </data>
  <data name="meta_descriptionhoteles" xml:space="preserve">
    <value>Discover the best trips and tour packages at the best price in {0}.com. Our wide selection of hotels, flights and travel packages will allow you to find the perfect option for your needs and budget. Plan your next trip with us and save money!</value>
  </data>
  <data name="meta_title_hotels" xml:space="preserve">
    <value>Travel agency, book hotels, flights and packages</value>
  </data>
  <data name="meta_descriptionpaquetes" xml:space="preserve">
    <value>{0} is your online travel agency to find the best packages and offers on your next trips. Book with us and save on your holidays!</value>
  </data>
  <data name="meta_title_packages" xml:space="preserve">
    <value>Travel agency, book packages, flights and hotels</value>
  </data>
  <data name="meta_descriptionvuelos" xml:space="preserve">
    <value>Book cheap flights for your unforgettable holiday in {0}.com. With flexible dates and the option of payment in monthly installments, you will find the best prices on air tickets to the most popular destinations. Don't miss this opportunity to save on your next trip!</value>
  </data>
  <data name="meta_descriptionlogin" xml:space="preserve">
    <value>Book cheap flights for your unforgettable vacations at {0}.com. With flexible dates and monthly payment options, you'll find the best prices on airline tickets to the most popular destinations. Don't miss this opportunity to save on your next trip!</value>
  </data>
  <data name="search_trivago" xml:space="preserve">
    <value>Your selected option on Trivago</value>
  </data>
  <data name="search_kayak" xml:space="preserve">
    <value>Your selected option on Kayak</value>
  </data>
  <data name="total_amount" xml:space="preserve">
    <value>Total</value>
  </data>
  <data name="total_amount_list" xml:space="preserve">
    <value>Total {0} </value>
  </data>
  <data name="best_option_pax" xml:space="preserve">
    <value>Best option for your search</value>
  </data>
  <data name="list_room_per_night_from" xml:space="preserve">
    <value>Room per night from</value>
  </data>
  <data name="list_room_per_night_low" xml:space="preserve">
    <value>Previous price</value>
  </data>
  <data name="room_card" xml:space="preserve">
    <value>Rooms</value>
  </data>
  <data name="hotel_unavailable_by_date" xml:space="preserve">
    <value>The hotel is not available on the selected dates</value>
  </data>
  <data name="hotel_found_date" xml:space="preserve">
    <value>We found these available dates</value>
  </data>
  <data name="list_rooms" xml:space="preserve">
    <value>rooms</value>
  </data>
  <data name="list_rooms_abre" xml:space="preserve">
    <value>rooms</value>
  </data>
  <data name="best_option_pax_detail" xml:space="preserve">
    <value>Due to the number of guests, you may need to book more than one room in this hotel</value>
  </data>
  <data name="date" xml:space="preserve">
    <value>Date</value>
  </data>
  <data name="dates" xml:space="preserve">
    <value>Dates</value>
  </data>
  <data name="best_opt_rm" xml:space="preserve">
    <value>Mejor opcion para tu busqueda</value>
  </data>
  <data name="detail_room" xml:space="preserve">
    <value>Room</value>
  </data>
  <data name="no_dispo_in_dates" xml:space="preserve">
    <value>This hotel has no availability on your selected dates</value>
  </data>
  <data name="to" xml:space="preserve">
    <value>to</value>
  </data>
  <data name="we_have_this_dates" xml:space="preserve">
    <value>Hotel not available, we suggest other dates</value>
  </data>
  <data name="we_recommend_these_dates" xml:space="preserve">
    <value>Suggested nearby dates</value>
  </data>
  <data name="night" xml:space="preserve">
    <value>Nigth</value>
  </data>
  <data name="nights" xml:space="preserve">
    <value>Nights</value>
  </data>
  <data name="people_lower" xml:space="preserve">
    <value>people</value>
  </data>
  <data name="people_lower_1" xml:space="preserve">
    <value>people</value>
  </data>
  <data name="link_pdv" xml:space="preserve">
    <value>{0} points of sale</value>
  </data>
  <data name="link_pdv_url" xml:space="preserve">
    <value>points-of-sale</value>
  </data>
  <data name="lang_en-us" xml:space="preserve">
    <value>English</value>
  </data>
  <data name="meta_pdv_description_state" xml:space="preserve">
    <value>{3} Service Points in {0}. Book your trip at {1} Customer Service Points located in {2} shopping malls and pay in up to 12</value>
  </data>
  <data name="lang_es-us" xml:space="preserve">
    <value>Español</value>
  </data>
  <data name="lang_es-mx" xml:space="preserve">
    <value>Español</value>
  </data>
  <data name="meta_pdv_description_main" xml:space="preserve">
    <value>{0} Service Points in Mexico. Book your trip at the 109 service points in Aguascalientes, Campeche, Chihuahua, Mexico City, Coahuila, Col.</value>
  </data>
  <data name="meta_pdv_description_place" xml:space="preserve">
    <value>{3} Service Points {0}: {1} {2}</value>
  </data>
  <data name="meta_description_destination" xml:space="preserve">
    <value>Destinations in {0}</value>
  </data>
  <data name="_pay" xml:space="preserve">
    <value>Pay</value>
  </data>
  <data name="all_airports" xml:space="preserve">
    <value>All airports</value>
  </data>
  <data name="capacity_list" xml:space="preserve">
    <value>Capacity per room: </value>
  </data>
  <data name="destinations" xml:space="preserve">
    <value>Destinations</value>
  </data>
  <data name="explore_the_destination" xml:space="preserve">
    <value>Explore the destinations of each country</value>
  </data>
  <data name="url_destination" xml:space="preserve">
    <value>destinations-by-country</value>
  </data>
  <data name="meta_url_country" xml:space="preserve">
    <value>{0}/destinations</value>
  </data>
  <data name="pdv_link" xml:space="preserve">
    <value>/puntos-de-venta</value>
  </data>
  <data name="pdv_breadcumb" xml:space="preserve">
    <value>Points of sale</value>
  </data>
  <data name="meal_plan_mobile" xml:space="preserve">
    <value>Select the plan</value>
  </data>
  <data name="until" xml:space="preserve">
    <value>until</value>
  </data>
  <data name="rapd_step_three" xml:space="preserve">
    <value>Pay online or by phone before {0} and get ready to travel at a better rate.</value>
  </data>
  <data name="rapd_step_three_default" xml:space="preserve">
    <value>Pay the total of your reservation before the {0} to preserve the rate and availability.</value>
  </data>
  <data name="rapd_step_deadline" xml:space="preserve">
    <value>deadline</value>
  </data>
  <data name="filter_new_appli" xml:space="preserve">
    <value>Applied filters ({0})</value>
  </data>
  <data name="filter_new_clear" xml:space="preserve">
    <value>Clean filters</value>
  </data>
  <data name="travel_to" xml:space="preserve">
    <value>Travel to</value>
  </data>
  <data name="with_pt" xml:space="preserve">
    <value>with {0}</value>
  </data>
  <data name="discover_beauty" xml:space="preserve">
    <value>Discover the beauty of</value>
  </data>
  <data name="find_best_opt_pt" xml:space="preserve">
    <value>Find the best options on {0}</value>
  </data>
  <data name="filter_by_price" xml:space="preserve">
    <value>Price per night</value>
  </data>
  <data name="order_by_list" xml:space="preserve">
    <value>Order by</value>
  </data>
  <data name="price_max" xml:space="preserve">
    <value>Max</value>
  </data>
  <data name="price_min" xml:space="preserve">
    <value>Min</value>
  </data>
  <data name="destination_title_0" xml:space="preserve">
    <value>Discover the main tourist destinations in {0}</value>
  </data>
  <data name="destination_title_1" xml:space="preserve">
    <value>Venture into {0}: Explore and discover fascinating places</value>
  </data>
  <data name="destination_title_2" xml:space="preserve">
    <value>{0}: Discover the charm of this tourist destination</value>
  </data>
  <data name="destination_title_3" xml:space="preserve">
    <value>Live {0} experience: An unforgettable trip</value>
  </data>
  <data name="destination_title_4" xml:space="preserve">
    <value>Delve into {0}: Exploring the heart of destiny</value>
  </data>
  <data name="session_email_placeholder" xml:space="preserve">
    <value><EMAIL></value>
  </data>
  <data name="session_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="session_last_name" xml:space="preserve">
    <value>Surname</value>
  </data>
  <data name="session_password" xml:space="preserve">
    <value>Enter your password</value>
  </data>
  <data name="session_create_account" xml:space="preserve">
    <value>Create Account</value>
  </data>
  <data name="session_your_password_be" xml:space="preserve">
    <value>Your password must have:</value>
  </data>
  <data name="session_almost_8" xml:space="preserve">
    <value>Minimum 8 characters</value>
  </data>
  <data name="session_almost_mayus" xml:space="preserve">
    <value>At least one capital letter</value>
  </data>
  <data name="session_almost_number" xml:space="preserve">
    <value>At least one number</value>
  </data>
  <data name="session_verify_email" xml:space="preserve">
    <value>Verify your email</value>
  </data>
  <data name="session_verify_help_secure" xml:space="preserve">
    <value>Help us keep your account safe.</value>
  </data>
  <data name="session_verify_instrucc" xml:space="preserve">
    <value>To verify your account, we've sent an email with instructions to:</value>
  </data>
  <data name="session_verify_instrucc2" xml:space="preserve">
    <value>To verify your email, we sent the instructions to:</value>
  </data>
  <data name="session_verify_spam" xml:space="preserve">
    <value>If the email does not appear, check your spam folder.</value>
  </data>
  <data name="session_continue_sailing" xml:space="preserve">
    <value> Continue browsing</value>
  </data>
  <data name="session_account_price" xml:space="preserve">
    <value>Log in to your {0} account</value>
  </data>
  <data name="forgot_review_email" xml:space="preserve">
    <value>Check your email</value>
  </data>
  <data name="forgot_review_email_instrucc" xml:space="preserve">
    <value>We've sent an email with instructions to reset your password to:</value>
  </data>
  <data name="forgot_review_valid" xml:space="preserve">
    <value>Once it arrives, it will be valid for 60 minutes.</value>
  </data>
  <data name="header_redirect" xml:space="preserve">
    <value>Go to home</value>
  </data>
  <data name="header_redirect_login" xml:space="preserve">
    <value>Back to sign in</value>
  </data>
  <data name="session_change_email" xml:space="preserve">
    <value>Change</value>
  </data>
  <data name="session_login_session" xml:space="preserve">
    <value>Sign in</value>
  </data>
  <data name="session_login_session_description" xml:space="preserve">
    <value>on your next trip by shopping with your {0} account</value>
  </data>
  <data name="session_login_session_description1" xml:space="preserve">
    <value>Save up to 10%</value>
  </data>
  <data name="session_login_session_description2" xml:space="preserve">
    <value>Get instant discounts</value>
  </data>
  <data name="session_name_error" xml:space="preserve">
    <value>Your {0} must be at least 2 characters long</value>
  </data>
  <data name="session_password_error" xml:space="preserve">
    <value>Your password is required</value>
  </data>
  <data name="session_recatcha_error" xml:space="preserve">
    <value>Recatcha is required</value>
  </data>
  <data name="session_error_gener" xml:space="preserve">
    <value>What's your {0}?</value>
  </data>
  <data name="session_error_password" xml:space="preserve">
    <value>password</value>
  </data>
  <data name="session_error_recatcha" xml:space="preserve">
    <value>recatcha</value>
  </data>
  <data name="session_password_verified" xml:space="preserve">
    <value>Your password is incorrect </value>
  </data>
  <data name="language_currency" xml:space="preserve">
    <value>Languages &amp; currency</value>
  </data>
  <data name="delete_filter" xml:space="preserve">
    <value>Clear filters</value>
  </data>
  <data name="year_old" xml:space="preserve">
    <value>0 to 23 months</value>
  </data>
  <data name="terms_conditions_coupon" xml:space="preserve">
    <value>See terms and conditions</value>
  </data>
  <data name="filter_stars_category" xml:space="preserve">
    <value>stars-category</value>
  </data>
  <data name="filter_mealplan_category" xml:space="preserve">
    <value>meal-plan</value>
  </data>
  <data name="filter_interest_category" xml:space="preserve">
    <value>interest</value>
  </data>
  <data name="filter_view_category" xml:space="preserve">
    <value>room-view</value>
  </data>
  <data name="filter_amenities_category" xml:space="preserve">
    <value>amenities</value>
  </data>
  <data name="filter_zones_category" xml:space="preserve">
    <value>zones</value>
  </data>
  <data name="no_prepayment" xml:space="preserve">
    <value>No prepayment needed</value>
  </data>
  <data name="pay_at_property" xml:space="preserve">
    <value>pay at the property</value>
  </data>
  <data name="meta_title_info_reservation-policies" xml:space="preserve">
    <value>Reservation policies</value>
  </data>
  <data name="meta_title_info_info" xml:space="preserve">
    <value>Site Information</value>
  </data>
  <data name="meta_descripcion_info_reservation-policies" xml:space="preserve">
    <value>Reservation, hotel and transportation cancelation policies. Payment and responsibility policy of BTC Americas Corporation</value>
  </data>
  <data name="info_reservation-policies" xml:space="preserve">
    <value>Reservation policies</value>
  </data>
  <data name="meta_title_info_privacy-policies" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="meta_title_info_profeco-contract" xml:space="preserve">
    <value>Profeco</value>
  </data>
  <data name="meta_descripcion_info_privacy-policies" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="info_privacy-policies" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="terms_and_coditions" xml:space="preserve">
    <value>Terms and Conditions</value>
  </data>
  <data name="url_privacy" xml:space="preserve">
    <value>https://us.pricetravel.com/help_us/privacy-policies</value>
  </data>
  <data name="url_sic" xml:space="preserve">
    <value>http://www.pricetravelholding.com/wp-content/uploads/2018/10/resolucion-60515-de-2018-condiciona-integracion-publica.pdf</value>
  </data>
  <data name="url_terms" xml:space="preserve">
    <value>https://us-help.pricetravel.com/info/reservation-policies</value>
  </data>
  <data name="footer_legal" xml:space="preserve">
    <value>In compliance with article 17 of law 679 of 2001, the agency rejects exploitation, pornography, sexual tourism, and other forms of sexual abuse of minors, which are penalized and administratively sanctioned under Colombian law.</value>
  </data>
  <data name="terms_and_cond" xml:space="preserve">
    <value>Terms and conditions</value>
  </data>
  <data name="popular_filters" xml:space="preserve">
    <value>Popular filters</value>
  </data>
  <data name="room_amenities" xml:space="preserve">
    <value>Room amenities</value>
  </data>
  <data name="rapd_message" xml:space="preserve">
    <value>Option to book now and pay later</value>
  </data>
  <data name="rapd_message_selection" xml:space="preserve">
    <value>Choose when you prefer to pay</value>
  </data>
  <data name="rapd_message_recommended" xml:space="preserve">
    <value>RECOMMENDED</value>
  </data>
  <data name="rapd_message_confirmation" xml:space="preserve">
    <value>Immediate confirmation</value>
  </data>
  <data name="rapd_message_coupon" xml:space="preserve">
    <value>You can use your {0} discount coupons</value>
  </data>
  <data name="rapd_message_total" xml:space="preserve">
    <value>Total:</value>
  </data>
  <data name="rapd_message_secure_esta" xml:space="preserve">
    <value>Secure your stay and price by paying </value>
  </data>
  <data name="rapd_message_ulti" xml:space="preserve">
    <value>You can pay until:</value>
  </data>
  <data name="go_back" xml:space="preserve">
    <value>Go Back</value>
  </data>
  <data name="add_to_favs" xml:space="preserve">
    <value>Add to favorites</value>
  </data>
  <data name="favorites" xml:space="preserve">
    <value>Favorites</value>
  </data>
  <data name="here_are_your_favs" xml:space="preserve">
    <value>Here you will find your saved accommodations!</value>
  </data>
  <data name="follow_these_steps" xml:space="preserve">
    <value>Follow these 2 simple steps to get started</value>
  </data>
  <data name="favorite_step_1" xml:space="preserve">
    <value>Search for accommodation of your preference</value>
  </data>
  <data name="favorite_step_2" xml:space="preserve">
    <value>During your search, tap the heart icon to save your favorite hotels</value>
  </data>
  <data name="some_hotels_cheaper" xml:space="preserve">
    <value>Some of your favorite hotels have dropped in price</value>
  </data>
  <data name="hotel_price_decreased" xml:space="preserve">
    <value>The price has dropped</value>
  </data>
  <data name="hotel_price_increased" xml:space="preserve">
    <value>The price has increased</value>
  </data>
  <data name="the_hotel" xml:space="preserve">
    <value>The hotel</value>
  </data>
  <data name="undo" xml:space="preserve">
    <value>Undo</value>
  </data>
  <data name="saved_in" xml:space="preserve">
    <value>has been saved to your</value>
  </data>
  <data name="deleted_from" xml:space="preserve">
    <value>has been removed from your</value>
  </data>
  <data name="fav_list" xml:space="preserve">
    <value>favorites list</value>
  </data>
  <data name="limit_reached" xml:space="preserve">
    <value>You have reached the limit of</value>
  </data>
  <data name="max_favs" xml:space="preserve">
    <value>20 favorites</value>
  </data>
  <data name="room_favs" xml:space="preserve">
    <value>room</value>
  </data>
  <data name="meta_title_favorites" xml:space="preserve">
    <value>{0} - Viaja fácil, sin pretextos</value>
  </data>
  <data name="meta_descriptionfavorites" xml:space="preserve">
    <value>Book cheap flights for your unforgettable vacations at {0}.com. With flexible dates and monthly payment options, you'll find the best prices on airline tickets to the most popular destinations. Don't miss this opportunity to save on your next trip!</value>
  </data>
  <data name="loading_favorites" xml:space="preserve">
    <value>Loading favorites</value>
  </data>
  <data name="destination_link" xml:space="preserve">
    <value>destination</value>
  </data>
  <data name="alternate_path_generic" xml:space="preserve">
    <value>{0}/{1}</value>
  </data>
  <data name="alternate_path_detail_hotel" xml:space="preserve">
    <value>{0}/hotel/{1}</value>
  </data>
  <data name="alternate_path_list_hotel" xml:space="preserve">
    <value>{0}/hotels-in-{1}</value>
  </data>
  <data name="alternate_path_detail_accommodation" xml:space="preserve">
    <value>{0}/accommodation/{1}</value>
  </data>
  <data name="alternate_path_list_accommodation" xml:space="preserve">
    <value>{0}/accommodations-in-{1}</value>
  </data>
  <data name="alternate_path_detail_vacation-rental" xml:space="preserve">
    <value>{0}/vacation-rental/{1}</value>
  </data>
  <data name="alternate_path_list_vacation-rental" xml:space="preserve">
    <value>{0}/vacations-rentals-in-{1}</value>
  </data>
  <data name="alternate_path_home_default" xml:space="preserve">
    <value>{0}</value>
  </data>
  <data name="alternate_flight" xml:space="preserve">
    <value>flights</value>
  </data>
  <data name="alternate_hotel" xml:space="preserve">
    <value>hotels</value>
  </data>
  <data name="alternate_packages" xml:space="preserve">
    <value>packages</value>
  </data>
  <data name="alternate_path_destinations_city" xml:space="preserve">
    <value>{0}/{1}/destinations</value>
  </data>
  <data name="alternate_path_destination" xml:space="preserve">
    <value>{0}/destination/{1}</value>
  </data>
  <data name="loading_recomendations" xml:space="preserve">
    <value>Loading recommendations</value>
  </data>
  <data name="title_important_infor" xml:space="preserve">
    <value>Good to know</value>
  </data>
  <data name="all_images" xml:space="preserve">
    <value>All images</value>
  </data>
  <data name="all_photos" xml:space="preserve">
    <value>All photos</value>
  </data>
  <data name="btn_paxes_cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="email_address_legal" xml:space="preserve">
    <value>© Price Res, SAPI de CV All rights reserved. Ave Kabah, LT 3-01 SMZ 13, MZ 1 CP 77500 Cancun, Quintana Roo, Mexico</value>
  </data>
  <data name="email_best_deals_hotels" xml:space="preserve">
    <value>The best deals on hotels, packages, and flights.</value>
  </data>
  <data name="email_discover_advantages" xml:space="preserve">
    <value>Discover the advantages of booking with us</value>
  </data>
  <data name="email_download_app" xml:space="preserve">
    <value>Download the {0} app for free</value>
  </data>
  <data name="email_download_on" xml:space="preserve">
    <value>Download on</value>
  </data>
  <data name="email_exclusive_discounts" xml:space="preserve">
    <value>Exclusive discounts when you book with your account.</value>
  </data>
  <data name="email_experts_your_service" xml:space="preserve">
    <value>Experts at your service with a direct line.</value>
  </data>
  <data name="email_follow_us" xml:space="preserve">
    <value>Follow us</value>
  </data>
  <data name="email_personalized_attention" xml:space="preserve">
    <value>Personalized attention 365 days a year by calling {0}</value>
  </data>
  <data name="email_privacy_notice" xml:space="preserve">
    <value>Privacy Notice</value>
  </data>
  <data name="email_see_offers" xml:space="preserve">
    <value>See Offers</value>
  </data>
  <data name="email_sign_save_more" xml:space="preserve">
    <value>Log in and save more! Starting now, you have access to special discounts on thousands of accommodations worldwide.</value>
  </data>
  <data name="email_start_planning" xml:space="preserve">
    <value>Start planning your next trip</value>
  </data>
  <data name="email_terms_conditions" xml:space="preserve">
    <value>Terms and Conditions</value>
  </data>
  <data name="email_welcome_user" xml:space="preserve">
    <value>Welcome to {0}!</value>
  </data>
  <data name="email_go_to_account" xml:space="preserve">
    <value>Go to your account</value>
  </data>
  <data name="meta_title_info_about-us" xml:space="preserve">
    <value>About us</value>
  </data>
  <data name="meta_title_info_payment-methods" xml:space="preserve">
    <value>Payment methods</value>
  </data>
  <data name="meta_description_info_about-us" xml:space="preserve">
    <value>About us Pricetravel</value>
  </data>
  <data name="meta_description_info_payment-methods" xml:space="preserve">
    <value>Payment methods Pricetravel</value>
  </data>
  <data name="meta_title_info_terms-and-conditions" xml:space="preserve">
    <value>Terms and conditions</value>
  </data>
  <data name="meta_description_info_terms-and-conditions" xml:space="preserve">
    <value>Pricetravel terms and conditions legals</value>
  </data>
  <data name="meta_title_info_privacy-policy" xml:space="preserve">
    <value>Privacy policy</value>
  </data>
  <data name="meta_description_info_privacy-policy" xml:space="preserve">
    <value>Pricetravel privacy policy</value>
  </data>
  <data name="meta_title_info_dviajeros-cuba" xml:space="preserve">
    <value>Affidavit of Travel to Cuba</value>
  </data>
  <data name="meta_description_info_dviajeros-cuba" xml:space="preserve">
    <value>Affidavit of Travel to Cuba</value>
  </data>
  <data name="meta_title_info_adhesion-contract" xml:space="preserve">
    <value>PROFECO Adhesion Contract</value>
  </data>
  <data name="meta_description_info_adhesion-contract" xml:space="preserve">
    <value>PROFECO Adhesion Contract Pricetravel</value>
  </data>
  <data name="contact_header_pt" xml:space="preserve">
    <value>Contact us!</value>
  </data>
  <data name="subtitle_landing_h" xml:space="preserve">
    <value>Book the best hotels in the most popular destinations at the best price.</value>
  </data>
  <data name="title_landing_h" xml:space="preserve">
    <value>Choose your next destination and live the experience</value>
  </data>
  <data name="title_landing_p" xml:space="preserve">
    <value>Discover the world, one trip at a time</value>
  </data>
  <data name="title_landing_v" xml:space="preserve">
    <value>Fly to your next destination</value>
  </data>
  <data name="leaving_from" xml:space="preserve">
    <value>Leaving from</value>
  </data>
  <data name="list_room" xml:space="preserve">
    <value>room</value>
  </data>
  <data name="meta_pdv_title" xml:space="preserve">
    <value>Point of sale {0} in</value>
  </data>
  <data name="know_our_paymentforms" xml:space="preserve">
    <value>Learn about all available payment options</value>
  </data>
  <data name="find_next_trip" xml:space="preserve">
    <value>Find your next trip</value>
  </data>
  <data name="meta_description_countries_destination" xml:space="preserve">
    <value>The destinations of {0}</value>
  </data>
  <data name="meta_title_countries_destination" xml:space="preserve">
    <value>The destinations of {0}</value>
  </data>
  <data name="email_profile" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="first_name" xml:space="preserve">
    <value>Name</value>
  </data>
  <data name="code_phone" xml:space="preserve">
    <value>Code phone</value>
  </data>
  <data name="birthdate" xml:space="preserve">
    <value>Birthdate</value>
  </data>
  <data name="country" xml:space="preserve">
    <value>Country</value>
  </data>
  <data name="profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="edit_perfil" xml:space="preserve">
    <value>Edit</value>
  </data>
  <data name="info_acerca-nosotros" xml:space="preserve">
    <value>About us</value>
  </data>
  <data name="info_politicas-reservacion" xml:space="preserve">
    <value>Reservation policies</value>
  </data>
  <data name="info_alto-estandar-limpieza" xml:space="preserve">
    <value>High standard of cleanliness</value>
  </data>
  <data name="info_politica-privacidad" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="info_por-que-reservar" xml:space="preserve">
    <value>Why book at {0}?</value>
  </data>
  <data name="meta_descripcion_info_por-que-reservar" xml:space="preserve">
    <value>Why book with {0}?</value>
  </data>
  <data name="meta_title_info_por-que-reservar" xml:space="preserve">
    <value>Why book with {0}?</value>
  </data>
  <data name="info_aviso-privacidad" xml:space="preserve">
    <value>Privacy notice</value>
  </data>
  <data name="alternate_package" xml:space="preserve">
    <value>package</value>
  </data>
  <data name="header_tb_promo" xml:space="preserve">
    <value>Deals</value>
  </data>
  <data name="header_tb_contact" xml:space="preserve">
    <value>Contact us!</value>
  </data>
  <data name="header_tb_check_in" xml:space="preserve">
    <value>Web Check-in</value>
  </data>
  <data name="header_tb_pay" xml:space="preserve">
    <value>
Online payments</value>
  </data>
  <data name="header_tb_group" xml:space="preserve">
    <value>Groups</value>
  </data>
  <data name="header_tb_write" xml:space="preserve">
    <value>Write us</value>
  </data>
  <data name="contact_us_title" xml:space="preserve">
    <value>Contact us!</value>
  </data>
  <data name="group" xml:space="preserve">
    <value>Groups</value>
  </data>
  <data name="payment_online" xml:space="preserve">
    <value>Online Payment</value>
  </data>
  <data name="web_check_in" xml:space="preserve">
    <value>Web Check-in</value>
  </data>
  <data name="write_us" xml:space="preserve">
    <value>Write us</value>
  </data>
  <data name="meta_title_info_anato" xml:space="preserve">
    <value>Anato</value>
  </data>
  <data name="meta_title_info_legal" xml:space="preserve">
    <value>Legal</value>
  </data>
  <data name="my_trips" xml:space="preserve">
    <value>Booking and Trips</value>
  </data>
  <data name="log_in" xml:space="preserve">
    <value>Login</value>
  </data>
  <data name="to_reservate" xml:space="preserve">
    <value>For booking</value>
  </data>
  <data name="modal_payment_TC_promotion" xml:space="preserve">
    <value>Credit card promotions</value>
  </data>
  <data name="modal_payment_cash_near" xml:space="preserve">
    <value>Pay in cash at your nearest bank or at our</value>
  </data>
  <data name="modal_payment_months" xml:space="preserve">
    <value>months</value>
  </data>
  <data name="modal_payment_months_interest" xml:space="preserve">
    <value>without interest</value>
  </data>
  <data name="trips_account" xml:space="preserve">
    <value>Trips and account</value>
  </data>
  <data name="login_and_get" xml:space="preserve">
    <value>Log in and get up to</value>
  </data>
  <data name="10_percent_dsc" xml:space="preserve">
    <value>10% off on hotels</value>
  </data>
  <data name="on_your_next_trip" xml:space="preserve">
    <value>on your next trip.</value>
  </data>
  <data name="trip_group" xml:space="preserve">
    <value>Group trips</value>
  </data>
  <data name="faq" xml:space="preserve">
    <value>Frequently Asked Questions</value>
  </data>
  <data name="write_to_us" xml:space="preserve">
    <value>Write to us</value>
  </data>
  <data name="need_call" xml:space="preserve">
    <value>Do you need us to call you?</value>
  </data>
  <data name="reserve_by_phone" xml:space="preserve">
    <value>Reservation by phone</value>
  </data>
  <data name="call_advisor" xml:space="preserve">
    <value>Call an advisor</value>
  </data>
  <data name="need_help_contact" xml:space="preserve">
    <value>Do you need help? Contact us</value>
  </data>
  <data name="by" xml:space="preserve">
    <value>By</value>
  </data>
  <data name="save_up_app" xml:space="preserve">
    <value>Save up to 10% extra on Hotels with our app!</value>
  </data>
  <data name="download_app" xml:space="preserve">
    <value>Download the {0} app</value>
  </data>
  <data name="need_help" xml:space="preserve">
    <value>Need help?</value>
  </data>
  <data name="call_us" xml:space="preserve">
    <value>Call us</value>
  </data>
  <data name="meta.vacation-rental_title_main" xml:space="preserve">
    <value>vacation rentals</value>
  </data>
  <data name="meta.accommodation_title_main" xml:space="preserve">
    <value>Accommodation</value>
  </data>
  <data name="select_lang_currency" xml:space="preserve">
    <value>Language and currency selection</value>
  </data>
  <data name="price_warning" xml:space="preserve">
    <value>Prices will be displayed in the currency you select. The currency you pay in may vary depending on the booking.</value>
  </data>
  <data name="language" xml:space="preserve">
    <value>Language</value>
  </data>
  <data name="currency" xml:space="preserve">
    <value>Currency</value>
  </data>
  <data name="active" xml:space="preserve">
    <value>Active</value>
  </data>
  <data name="login_banner_modal" xml:space="preserve">
    <value>Save up to 10% extra on hotels by logging in</value>
  </data>
  <data name="footer_follow_us" xml:space="preserve">
    <value>Follow us on our social networks!</value>
  </data>
  <data name="footer_contact_us" xml:space="preserve">
    <value>Contact us!</value>
  </data>
  <data name="footer_highlighted_airlines" xml:space="preserve">
    <value>Highlighted Airlines</value>
  </data>
  <data name="footer_most_viewed" xml:space="preserve">
    <value>Most Viewed</value>
  </data>
  <data name="footer_about_tiquetesbaratos" xml:space="preserve">
    <value>About tiquetesbaratos.com</value>
  </data>
  <data name="footer_resources" xml:space="preserve">
    <value>Resources</value>
  </data>
  <data name="footer_promotion" xml:space="preserve">
    <value>Promotions</value>
  </data>
  <data name="footer_promotions_destination" xml:space="preserve">
    <value>Destination Promotions</value>
  </data>
  <data name="footer_hotel_offers" xml:space="preserve">
    <value>Hotel and Package Deals</value>
  </data>
  <data name="footer_check_reservations" xml:space="preserve">
    <value>Check your Reservation</value>
  </data>
  <data name="footer_web_checkin" xml:space="preserve">
    <value>Web Check-in</value>
  </data>
  <data name="footer_contact" xml:space="preserve">
    <value>Contact us</value>
  </data>
  <data name="footer_faq" xml:space="preserve">
    <value>Frequently Asked Questions</value>
  </data>
  <data name="footer_online_payment" xml:space="preserve">
    <value>Online Payment</value>
  </data>
  <data name="footer_legal_tiquetes" xml:space="preserve">
    <value>In compliance with the provisions of Article 17 of Law 679 of 2001, the agency rejects exploitation, pornography, sexual tourism, and other forms of sexual abuse involving minors, which are penalized both criminally and administratively in accordance with Colombian laws.</value>
  </data>
  <data name="footer_company_info" xml:space="preserve">
    <value>TiquetesBaratos - PRICE RES S.A.S. NIT. 900.474.794-8 , Calle 99 # 10-19 Of 701 Bogotá D.C., Bogotá – Colombia, Phones: ************. All rights reserved. National Tourism Registry No. 44091.</value>
  </data>
  <data name="footer_hotel_package_offers" xml:space="preserve">
    <value>Hotel and Package Deals</value>
  </data>
  <data name="footer_about_us" xml:space="preserve">
    <value>About Us</value>
  </data>
  <data name="footer_about_anato" xml:space="preserve">
    <value>About Anato</value>
  </data>
  <data name="footer_privacy_policy" xml:space="preserve">
    <value>Privacy Policy</value>
  </data>
  <data name="footer_secure_purchase" xml:space="preserve">
    <value>Secure Purchase</value>
  </data>
  <data name="footer_superintendence_industry" xml:space="preserve">
    <value>Superintendence of Industry and Commerce</value>
  </data>
  <data name="footer_superintendence_transport" xml:space="preserve">
    <value>Superintendence of Transport</value>
  </data>
  <data name="footer_aerocivil" xml:space="preserve">
    <value>Aerocivil</value>
  </data>
  <data name="footer_child_pornography" xml:space="preserve">
    <value>Against Child Pornography</value>
  </data>
  <data name="footer_passenger_rights" xml:space="preserve">
    <value>Passenger Rights and/or Carrier Obligations</value>
  </data>
  <data name="footer_retract_law" xml:space="preserve">
    <value>Law of Withdrawal and/or Desistance</value>
  </data>
  <data name="footer_sustainability_policy" xml:space="preserve">
    <value>Sustainability Policy</value>
  </data>
  <data name="footer_terms_and_conditions" xml:space="preserve">
    <value>Terms and Conditions</value>
  </data>
  <data name="footer_ticket_bogota" xml:space="preserve">
    <value>Bogotá Tickets</value>
  </data>
  <data name="footer_pdv" xml:space="preserve">
    <value>Points of Sale</value>
  </data>
  <data name="footer_airline_tickets" xml:space="preserve">
    <value>Airline Tickets</value>
  </data>
  <data name="footer_flight_offers" xml:space="preserve">
    <value>Flight Offers</value>
  </data>
  <data name="footer_flights_promotion" xml:space="preserve">
    <value>Flights on Promotion</value>
  </data>
  <data name="footer_national_tickets" xml:space="preserve">
    <value>National Tickets</value>
  </data>
  <data name="footer_ticket_offers" xml:space="preserve">
    <value>Ticket Offers</value>
  </data>
  <data name="footer_aerial_tickets" xml:space="preserve">
    <value>Aerial Tickets</value>
  </data>
  <data name="footer_destinations" xml:space="preserve">
    <value>Destinations</value>
  </data>
  <data name="hotel_title_banner_pricetravel" xml:space="preserve">
    <value>Book now and pay later | Installment plans | FREE cancellation on thousands of hotels</value>
  </data>
  <data name="hotel_title_banner_tiquetesbaratos" xml:space="preserve">
    <value>Flexibility in changes | Pay in installments | FREE cancellation on thousands of hotels</value>
  </data>
  <data name="packages_title_banner_pricetravel" xml:space="preserve">
    <value>Book now and pay later | Installment plans | FREE cancellation on thousands of hotels</value>
  </data>
  <data name="packages_title_banner_tiquetesbaratos" xml:space="preserve">
    <value>Flexibility in changes | Pay in installments | FREE cancellation on thousands of hotels</value>
  </data>
  <data name="modal_call_title" xml:space="preserve">
    <value>Do you need us to call you?</value>
  </data>
  <data name="modal_call_name_placeholder" xml:space="preserve">
    <value>Your name</value>
  </data>
  <data name="modal_call_name_invalid" xml:space="preserve">
    <value>Please enter your contact name!</value>
  </data>
  <data name="modal_call_phone_placeholder" xml:space="preserve">
    <value>Phone or mobile (10 digits)</value>
  </data>
  <data name="modal_call_phone_invalid" xml:space="preserve">
    <value>Please enter your phone number!</value>
  </data>
  <data name="modal_call_recaptcha_invalid" xml:space="preserve">
    <value>Please verify the recaptcha!</value>
  </data>
  <data name="modal_call_submit_button" xml:space="preserve">
    <value>Send</value>
  </data>
  <data name="modal_call_schedule_title" xml:space="preserve">
    <value>Customer service hours</value>
  </data>
  <data name="modal_call_schedule_weekdays" xml:space="preserve">
    <value>Monday to Friday from 06:00 AM to 11:50 PM</value>
  </data>
  <data name="modal_call_schedule_weekends" xml:space="preserve">
    <value>Saturday, Sunday, and holidays 07:00 AM to 11:50 PM</value>
  </data>
  <data name="mail_contact_thank_you_message" xml:space="preserve">
    <value>Thank you! We will contact you shortly.</value>
  </data>
  <data name="mail_contact_accept_button" xml:space="preserve">
    <value>Accept</value>
  </data>
  <data name="modaltb_payment_title" xml:space="preserve">
    <value>Payment Methods</value>
  </data>
  <data name="modaltb_payment_card_title" xml:space="preserve">
    <value>Credit and Debit Cards</value>
  </data>
  <data name="modaltb_payment_until" xml:space="preserve">
    <value>Up to</value>
  </data>
  <data name="modaltb_payment_warning" xml:space="preserve">
    <value>Installment payment options depend on the selected service and the purchase amount.</value>
  </data>
  <data name="modaltb_payment_no_card_title" xml:space="preserve">
    <value>Don’t have a credit card?</value>
  </data>
  <data name="modaltb_payment_other_methods" xml:space="preserve">
    <value>We offer more payment methods:</value>
  </data>
  <data name="modaltb_payment_bancolombia" xml:space="preserve">
    <value>Pay online with your Bancolombia savings or checking account</value>
  </data>
  <data name="modaltb_payment_pse_title" xml:space="preserve">
    <value>Online payment with PSE</value>
  </data>
  <data name="modaltb_payment_pse_description" xml:space="preserve">
    <value>Pay online with your savings or checking account</value>
  </data>
  <data name="modaltb_payment_cash_title" xml:space="preserve">
    <value>Pay in cash</value>
  </data>
  <data name="modaltb_payment_cash_description" xml:space="preserve">
    <value>Pay in cash at your nearest location</value>
  </data>
  <data name="modaltb_payment_efecty" xml:space="preserve">
    <value>Pay in cash using collection code 112232 and the payment reference at any Efecty point nationwide</value>
  </data>
  <data name="modaltb_payment_bank_deposit" xml:space="preserve">
    <value>Bank deposit</value>
  </data>
  <data name="banner_download_app_text_1" xml:space="preserve">
    <value>Save up to </value>
  </data>
  <data name="banner_download_app_text_2" xml:space="preserve">
    <value>10% extra on hotels</value>
  </data>
  <data name="banner_download_app_text_3" xml:space="preserve">
    <value> with our app!</value>
  </data>
  <data name="banner_download_app_button" xml:space="preserve">
    <value>Download</value>
  </data>
  <data name="banner_download_app_close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="table_your_selection" xml:space="preserve">
    <value>Your selection</value>
  </data>
  <data name="header_tb_fact" xml:space="preserve">
    <value>Billing</value>
  </data>
  <data name="recaptcha_error" xml:space="preserve">
    <value>Please verify the reCAPTCHA</value>
  </data>
  <data name="newsletter_title" xml:space="preserve">
    <value>Don't miss any offer!</value>
  </data>
  <data name="newsletter_subtitle" xml:space="preserve">
    <value>You will receive information to have the best experience</value>
  </data>
  <data name="newsletter_email" xml:space="preserve">
    <value>Email</value>
  </data>
  <data name="newsletter_suscribe" xml:space="preserve">
    <value>Subscribe now</value>
  </data>
  <data name="newsletter_validatorMessage" xml:space="preserve">
    <value>Enter a valid email address</value>
  </data>
  <data name="subtitleHomedelta" xml:space="preserve">
    <value>Book and fly to the best destinations with Cheap Tickets</value>
  </data>
  <data name="subtitleHomeaeromexico" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomeaircanada" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomeairfrance" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomeamerican" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomeavianca" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomeboletosaereos" xml:space="preserve">
    <value>Find the best prices on CheapTickets.com</value>
  </data>
  <data name="subtitleHomeclicair" xml:space="preserve">
    <value>Enjoy the best discounts with Clic Air, cheap flights with Clic Air!</value>
  </data>
  <data name="subtitleHomecopa" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomeiberia" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomejetblue" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomejetsmart" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomelatam" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomeofertavuelos" xml:space="preserve">
    <value>Find the best prices on TiquetesBaratos.com</value>
  </data>
  <data name="subtitleHomesatena" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHometiquetesaereos" xml:space="preserve">
    <value>Find the best prices on TiquetesBaratos.com</value>
  </data>
  <data name="subtitleHometiquetesbogota" xml:space="preserve">
    <value>Find the best prices on TiquetesBaratos.com</value>
  </data>
  <data name="subtitleHometiquetesnacionales" xml:space="preserve">
    <value>Find the best prices on TiquetesBaratos.com</value>
  </data>
  <data name="subtitleHometiquetesoferta" xml:space="preserve">
    <value>Find the best prices on TiquetesBaratos.com</value>
  </data>
  <data name="subtitleHomeunited" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomevivaerobus" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomevolaris" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomevuelosenpromocion" xml:space="preserve">
    <value>Find the best prices on TiquetesBaratos.com</value>
  </data>
  <data name="subtitleHomewingo" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="tiquetesBaratosaeromexico" xml:space="preserve">
    <value>Aeromexico</value>
  </data>
  <data name="tiquetesBaratosdelta" xml:space="preserve">
    <value>Delta Airlines</value>
  </data>
  <data name="tiquetesBaratosaircanada" xml:space="preserve">
    <value>Air Canada</value>
  </data>
  <data name="tiquetesBaratosairfrance" xml:space="preserve">
    <value>Air France</value>
  </data>
  <data name="tiquetesBaratosamerican" xml:space="preserve">
    <value>American Airlines</value>
  </data>
  <data name="tiquetesBaratosavianca" xml:space="preserve">
    <value>Avianca</value>
  </data>
  <data name="tiquetesBaratosboletosaereos" xml:space="preserve">
    <value>Buy Cheap Airline Tickets Online</value>
  </data>
  <data name="tiquetesBaratosclicair" xml:space="preserve">
    <value>Clic Air</value>
  </data>
  <data name="tiquetesBaratoscontinental" xml:space="preserve">
    <value>Continental</value>
  </data>
  <data name="tiquetesBaratoscopa" xml:space="preserve">
    <value>Copa Airlines</value>
  </data>
  <data name="tiquetesBaratosiberia" xml:space="preserve">
    <value>Iberia</value>
  </data>
  <data name="tiquetesBaratosjetblue" xml:space="preserve">
    <value>JetBlue Airways</value>
  </data>
  <data name="tiquetesBaratosjetsmart" xml:space="preserve">
    <value>JetSMART</value>
  </data>
  <data name="tiquetesBaratoslatam" xml:space="preserve">
    <value>LATAM</value>
  </data>
  <data name="tiquetesBaratosofertavuelos" xml:space="preserve">
    <value>Flight Deals</value>
  </data>
  <data name="tiquetesBaratossatena" xml:space="preserve">
    <value>Satena</value>
  </data>
  <data name="tiquetesBaratostiquetesaereos" xml:space="preserve">
    <value>Airline Tickets Online</value>
  </data>
  <data name="tiquetesBaratostiquetesbogota" xml:space="preserve">
    <value>{0} to Bogotá</value>
  </data>
  <data name="meta_title_tiquetesbogota" xml:space="preserve">
    <value>Book Tickets to Bogotá</value>
  </data>
  <data name="tiquetesBaratostiquetesnacionales" xml:space="preserve">
    <value>{0} for Domestic Flights</value>
  </data>
  <data name="tiquetesBaratostiquetesoferta" xml:space="preserve">
    <value>Deals on Airline Tickets</value>
  </data>
  <data name="tiquetesBaratosunited" xml:space="preserve">
    <value>United Airlines</value>
  </data>
  <data name="tiquetesBaratosvivaaerobus" xml:space="preserve">
    <value>Viva Aerobus</value>
  </data>
  <data name="tiquetesBaratosvolaris" xml:space="preserve">
    <value>Volaris</value>
  </data>
  <data name="tiquetesBaratosvuelosenpromocion" xml:space="preserve">
    <value>Flight Promotions</value>
  </data>
  <data name="tiquetesBaratoswingo" xml:space="preserve">
    <value>Wingo</value>
  </data>
  <data name="subtitleHomelan" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomevivaaerobus" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="subtitleHomevivacolombia" xml:space="preserve">
    <value>Book and fly to the best destinations with {0}</value>
  </data>
  <data name="tiquetesBaratoslan" xml:space="preserve">
    <value>Latam Airlines</value>
  </data>
  <data name="tiquetesBaratosvivacolombia" xml:space="preserve">
    <value>Viva Colombia</value>
  </data>
  <data name="destinationsTitle" xml:space="preserve">
    <value>Flights From {0} to {1}</value>
  </data>
  <data name="destinationsSubtitle" xml:space="preserve">
    <value>{0}</value>
  </data>
  <data name="alternate_path_airlines" xml:space="preserve">
    <value>{0}/airlines/{1}</value>
  </data>
  <data name="alternate_path_destinationstb" xml:space="preserve">
    <value>{0}/tiquetes/{1}</value>
  </data>
  <data name="no_roomw_detail" xml:space="preserve">
    <value>No rooms available</value>
  </data>
  <data name="no_roomw_detail_call" xml:space="preserve">
    <value>Try checking other dates or call us for free at </value>
  </data>
  <data name="footer_tb_contrato" xml:space="preserve">
    <value>Air Transport Contract of Colombia</value>
  </data>
  <data name="footer_tb_register_enterprise" xml:space="preserve">
    <value>CheapTickets | Enterprises</value>
  </data>
  <data name="footer_tb_register_hotel" xml:space="preserve">
    <value>Register your hotel</value>
  </data>
  <data name="room_only_a" xml:space="preserve">
    <value>Adults only: This hotel is exclusively for guests of legal age.</value>
  </data>
  <data name="meta_description_delta" xml:space="preserve">
    <value>Book your flights with Delta Airlines at the best price on {0}. We offer vacation packages and travel agency services. Book now and enjoy!</value>
  </data>
  <data name="meta_description_aeromexico" xml:space="preserve">
    <value>Discover the best flight deals with Aeromexico at {0}. Book quickly and easily with our travel agency. Take advantage of our packages and save!</value>
  </data>
  <data name="meta_description_aircanada" xml:space="preserve">
    <value>Find and book cheap flights with Air Canada at {0}. Exclusive deals and vacation packages available. Plan your trip with us today!</value>
  </data>
  <data name="meta_description_airfrance" xml:space="preserve">
    <value>Book your flights with Air France at the best price on {0}. We offer vacation packages and travel agency services. Book now and enjoy!</value>
  </data>
  <data name="meta_description_american" xml:space="preserve">
    <value>Take advantage of the best deals on flights with American Airlines at {0}. Vacation packages and hotels available. Book today and save with our travel agency!</value>
  </data>
  <data name="meta_description_avianca" xml:space="preserve">
    <value>Book cheap flights with Avianca through {0}. Find the best deals and vacation packages. Book quickly and easily with our travel agency!</value>
  </data>
  <data name="meta_description_boletosaereos" xml:space="preserve">
    <value>Find and book cheap plane tickets at {0}. Exclusive deals on flights, hotels, and vacation packages. Plan your trip with our travel agency!</value>
  </data>
  <data name="meta_description_clicair" xml:space="preserve">
    <value>Discover the best flight deals with ClicAir at {0}. Book flights, hotels, and vacation packages with our travel agency. Save today!</value>
  </data>
  <data name="meta_description_continental" xml:space="preserve">
    <value>Find and book flights with Continental Airlines at {0}. Deals on vacation packages and hotels available. Book now and enjoy our travel agency services!</value>
  </data>
  <data name="meta_description_copa" xml:space="preserve">
    <value>Take advantage of the best flight deals with Copa Airlines at {0}. Vacation packages and travel agency services available. Book today and save!</value>
  </data>
  <data name="meta_description_easyfly" xml:space="preserve">
    <value>Find the best flight deals with EasyFly at {0}. Book quickly and easily with our travel agency. Take advantage of our vacation packages and save!</value>
  </data>
  <data name="meta_description_iberia" xml:space="preserve">
    <value>Book cheap flights with Iberia through {0}. Find the best deals and vacation packages. Plan your trip with our travel agency!</value>
  </data>
  <data name="meta_description_jetblue" xml:space="preserve">
    <value>Discover the best flight deals with JetBlue at {0}. Book flights, hotels, and vacation packages with our travel agency. Save today!</value>
  </data>
  <data name="meta_description_jetsmart" xml:space="preserve">
    <value>Book your flights with JetSmart at the best price on {0}. We offer vacation packages and travel agency services. Book now and enjoy!</value>
  </data>
  <data name="meta_description_lan" xml:space="preserve">
    <value>Take advantage of the best flight deals with LAN Airlines at {0}. Vacation packages and hotels available. Book today and save with our travel agency!</value>
  </data>
  <data name="meta_description_latam" xml:space="preserve">
    <value>Book cheap flights with LATAM through {0}. Find the best deals and vacation packages. Book quickly and easily with our travel agency!</value>
  </data>
  <data name="meta_description_ofertavuelos" xml:space="preserve">
    <value>Find and book cheap plane tickets at {0}. Exclusive deals on flights, hotels, and vacation packages. Plan your trip with our travel agency!</value>
  </data>
  <data name="meta_description_satena" xml:space="preserve">
    <value>Discover the best flight deals with Satena at {0}. Book flights, hotels, and vacation packages with our travel agency. Save today!</value>
  </data>
  <data name="meta_description_tiquetesaereos" xml:space="preserve">
    <value>Find and book cheap plane tickets at {0}. Exclusive deals on flights, hotels, and vacation packages. Plan your trip with our travel agency!</value>
  </data>
  <data name="meta_description_tiquetesbogota" xml:space="preserve">
    <value>Book the best tickets to Bogotá with {0}. Deals on flights, hotels, and vacation packages. Book quickly and easily with our travel agency!</value>
  </data>
  <data name="meta_description_tiquetesnacionales" xml:space="preserve">
    <value>Find and book cheap tickets on domestic flights at {0}. Exclusive deals on flights, hotels, and vacation packages. Plan your trip with our travel agency!</value>
  </data>
  <data name="meta_description_tiquetesoferta" xml:space="preserve">
    <value>Discover the best deals on plane tickets with {0}. Book flights, hotels, and vacation packages with our travel agency. Save today!</value>
  </data>
  <data name="meta_description_united" xml:space="preserve">
    <value>Book cheap flights with United Airlines through {0}. Find the best deals and vacation packages. Plan your trip with our travel agency!</value>
  </data>
  <data name="meta_description_vivaaerobus" xml:space="preserve">
    <value>Discover the best flight deals with VivaAerobus at {0}. Book flights, hotels, and vacation packages with our travel agency. Save today!</value>
  </data>
  <data name="meta_description_volaris" xml:space="preserve">
    <value>Take advantage of the best flight deals with Volaris at {0}. Vacation packages and hotels available. Book today and save with our travel agency!</value>
  </data>
  <data name="meta_description_vuelosenpromocion" xml:space="preserve">
    <value>Discover the best promotions on flights with {0}. Book flights, hotels, and vacation packages with our travel agency. Save today!</value>
  </data>
  <data name="meta_description_wingo" xml:space="preserve">
    <value>Find the best flight deals with Wingo at {0}. Book quickly and easily with our travel agency. Take advantage of our vacation packages and save!</value>
  </data>
  <data name="meta_title_delta" xml:space="preserve">
    <value>Book Flights with Delta Airlines</value>
  </data>
  <data name="meta_title_vivaaerobus" xml:space="preserve">
    <value>Book Flights with VivaAerobus</value>
  </data>
  <data name="meta_title_vivacolombia" xml:space="preserve">
    <value>Viva Air</value>
  </data>
  <data name="meta_title_volaris" xml:space="preserve">
    <value>Book Flights with Volaris</value>
  </data>
  <data name="meta_title_vuelosenpromocion" xml:space="preserve">
    <value>Flights on Sale</value>
  </data>
  <data name="meta_title_wingo" xml:space="preserve">
    <value>Book Flights with Wingo</value>
  </data>
  <data name="meta_title_aeromexico" xml:space="preserve">
    <value>Book Flights with Aeromexico</value>
  </data>
  <data name="meta_title_aircanada" xml:space="preserve">
    <value>Book Flights with Air Canada</value>
  </data>
  <data name="meta_title_airfrance" xml:space="preserve">
    <value>Air France Flights at the Best Price</value>
  </data>
  <data name="meta_title_american" xml:space="preserve">
    <value>Book Flights with American Airlines</value>
  </data>
  <data name="meta_title_avianca" xml:space="preserve">
    <value>Cheap Flights with Avianca</value>
  </data>
  <data name="meta_title_boletosaereos" xml:space="preserve">
    <value>Airline Tickets at the Best Price</value>
  </data>
  <data name="meta_title_clicair" xml:space="preserve">
    <value>Book Flights with ClicAir</value>
  </data>
  <data name="meta_title_continental" xml:space="preserve">
    <value>Flights with Continental Airlines</value>
  </data>
  <data name="meta_title_copa" xml:space="preserve">
    <value>Book Flights with Copa Airlines</value>
  </data>
  <data name="meta_title_iberia" xml:space="preserve">
    <value>Cheap Flights with Iberia</value>
  </data>
  <data name="meta_title_jetblue" xml:space="preserve">
    <value>Book Flights with JetBlue</value>
  </data>
  <data name="meta_title_jetsmart" xml:space="preserve">
    <value>Flights with JetSmart at the Best Price</value>
  </data>
  <data name="meta_title_latam" xml:space="preserve">
    <value>Cheap Flights with LATAM</value>
  </data>
  <data name="meta_title_lan" xml:space="preserve">
    <value>Book Flights with LAN Airlines</value>
  </data>
  <data name="meta_title_easyfly" xml:space="preserve">
    <value>Book Flights with EasyFly</value>
  </data>
  <data name="meta_title_ofertavuelos" xml:space="preserve">
    <value>Flight Deals</value>
  </data>
  <data name="most_popular_services" xml:space="preserve">
    <value>Most popular services</value>
  </data>
  <data name="extra_cost" xml:space="preserve">
    <value>Extra cost</value>
  </data>
  <data name="specialty_cuisine" xml:space="preserve">
    <value>Specialty / Type of cuisine</value>
  </data>
  <data name="ambient" xml:space="preserve">
    <value>Ambience</value>
  </data>
  <data name="open_to" xml:space="preserve">
    <value>Open to</value>
  </data>
  <data name="diet_options" xml:space="preserve">
    <value>Dietary options</value>
  </data>
  <data name="reservation_required" xml:space="preserve">
    <value>Reservation required</value>
  </data>
  <data name="nearby_places" xml:space="preserve">
    <value>Nearby places</value>
  </data>
  <data name="alternate_path_a_airlines" xml:space="preserve">
    <value>{0}/airlines/{1}</value>
  </data>
  <data name="alternate_path_a_flight" xml:space="preserve">
    <value>{0}/flights/airlines/{1}</value>
  </data>
  <data name="alternate_path_d_tiquetes" xml:space="preserve">
    <value>{0}/tiquetes/{1}</value>
  </data>
  <data name="alternate_path_d_flight" xml:space="preserve">
    <value>{0}/flights/tiquetes/{1}</value>
  </data>
  <data name="meta_destination_title" xml:space="preserve">
    <value>Flights from {0} to {1}</value>
  </data>
  <data name="account_benefits" xml:space="preserve">
    <value>Benefits of your account</value>
  </data>
  <data name="exclusive_dsc" xml:space="preserve">
    <value>Exclusive discounts</value>
  </data>
  <data name="phoneline_attention" xml:space="preserve">
    <value>24/7 Support through our exclusive phoneline</value>
  </data>
  <data name="hotels_packages_dsc" xml:space="preserve">
    <value>Save up to 10% on Hotels</value>
  </data>
  <data name="legals_world" xml:space="preserve">
    <value>© BTC Americas Corporation. {0}. All rights reserved.</value>
  </data>
  <data name="links_us_country" xml:space="preserve">
    <value>USA/Canada</value>
  </data>
  <data name="oneway_pricetravel" xml:space="preserve">
    <value>One-way</value>
  </data>
  <data name="oneway_tiquetesbaratos" xml:space="preserve">
    <value>One-way</value>
  </data>
  <data name="relevant_aspects" xml:space="preserve">
    <value>Relevant aspects</value>
  </data>
  <data name="meta_descriptionhotels" xml:space="preserve">
    <value>Discover the best trips and tour packages at the best price in {0}.com. Our wide selection of hotels, flights and travel packages will allow you to find the perfect option for your needs and budget. Plan your next trip with us and save money!</value>
  </data>
  <data name="pay_monthly_without_interestpricetravel" xml:space="preserve">
    <value>Pay monthly without interest</value>
  </data>
  <data name="pay_monthly_without_interesttiquetesbaratos" xml:space="preserve">
    <value>Pay in installments</value>
  </data>
  <data name="email_logo_img_url_pricetravel" xml:space="preserve">
    <value>https://img.cdnpth.com/media/assets/img/logo_pricetravel.png.webp</value>
  </data>
  <data name="email_logo_img_url_tiquetesbaratos" xml:space="preserve">
    <value>https://img.cdnpth.com/media/assets-tb/1.7.33/img/logo-tiquetesbaratos.png</value>
  </data>
  <data name="email_privacy_notice_url_pricetravel" xml:space="preserve">
    <value>https://www.pricetravel.com/en/info/privacy-policy</value>
  </data>
  <data name="email_terms_conditions_url_pricetravel" xml:space="preserve">
    <value>https://www.pricetravel.com/en/info/terms-and-conditions</value>
  </data>
  <data name="email_privacy_notice_url_tiquetesbaratos" xml:space="preserve">
    <value>https://www.tiquetesbaratos.com/en/info/privacy-policy</value>
  </data>
  <data name="email_terms_conditions_url_tiquetesbaratos" xml:space="preserve">
    <value>https://www.tiquetesbaratos.com/en/info/terms-and-conditions</value>
  </data>
  <data name="email_download_url_pricetravel" xml:space="preserve">
    <value>https://www.pricetravel.com/link/NTVFQTlFRkQ4RThCp</value>
  </data>
  <data name="email_download_url_tiquetesbaratos" xml:space="preserve">
    <value>https://www.tiquetesbaratos.com/link/REIwNjJEMEU3MjdD</value>
  </data>
  <data name="email_deals_url_pricetravel" xml:space="preserve">
    <value>https://www.pricetravel.com/en/deals/</value>
  </data>
  <data name="email_deals_url_tiquetesbaratos" xml:space="preserve">
    <value>https://www.tiquetesbaratos.com/ofertas-de-viajes/</value>
  </data>
  <data name="links_us_simple_country" xml:space="preserve">
    <value>USA/Canada</value>
  </data>
  <data name="help_pricetravel" xml:space="preserve">
    <value>https://us-help.pricetravel.com</value>
  </data>
  <data name="invoices_pricetravel" xml:space="preserve">
    <value>https://us-help.pricetravel.com/check-itinerary</value>
  </data>
  <data name="get_booking_pricetravel" xml:space="preserve">
    <value>https://www.pricetravel.com/{0}/help-center/search-itinerary</value>
  </data>
  <data name="update_booking_pricetravel" xml:space="preserve">
    <value>https://us-help.pricetravel.com/modify-reservation</value>
  </data>
  <data name="cancel_booking_pricetravel" xml:space="preserve">
    <value>https://us-help.pricetravel.com/cancel-reservation</value>
  </data>
  <data name="roundtrip_pricetravel" xml:space="preserve">
    <value>Round-trip</value>
  </data>
  <data name="meta_title_tiquetesnacionales" xml:space="preserve">
    <value>Cheap Tickets for Domestic Flights</value>
  </data>
  <data name="hotel_brand" xml:space="preserve">
    <value>Hotel Brand/Chain</value>
  </data>
  <data name="outside_service" xml:space="preserve">
    <value>Outside the accommodation</value>
  </data>
  <data name="seo_question" xml:space="preserve">
    <value>Frequently asked questions {0} {1}</value>
  </data>
  <data name="seo_question_info" xml:space="preserve">
    <value>about</value>
  </data>
  <data name="seo_info" xml:space="preserve">
    <value>About {0}</value>
  </data>
  <data name="email_welcome_user_english" xml:space="preserve">
    <value>¡Te damos la bienvenida a {0}!</value>
  </data>
  <data name="account_verify_title" xml:space="preserve">
    <value>Email successfully verified!</value>
  </data>
  <data name="account_verify_title_error" xml:space="preserve">
    <value>Verification error</value>
  </data>
  <data name="account_verify_subtitle" xml:space="preserve">
    <value>Your account has been successfully verified</value>
  </data>
  <data name="account_verify_qustion" xml:space="preserve">
    <value>What can you do now?</value>
  </data>
  <data name="account_verify_qustion_one" xml:space="preserve">
    <value>Complete your profile for better recommendations</value>
  </data>
  <data name="account_verify_qustion_two" xml:space="preserve">
    <value>Discover exclusive offers for verified members</value>
  </data>
  <data name="account_verify_qustion_three" xml:space="preserve">
    <value>Set up your travel preferences</value>
  </data>
  <data name="account_reset_password_title" xml:space="preserve">
    <value>Password successfully updated!</value>
  </data>
  <data name="account_reset_password_title_error" xml:space="preserve">
    <value>Error updating password</value>
  </data>
  <data name="account_reset_password_new_title" xml:space="preserve">
    <value>Create a new password</value>
  </data>
  <data name="account_reset_password_new_subtitle" xml:space="preserve">
    <value>Please enter and confirm your new password</value>
  </data>
  <data name="account_reset_password_new_password" xml:space="preserve">
    <value>New password</value>
  </data>
  <data name="account_reset_password_new_password_confirm" xml:space="preserve">
    <value>Confirm password</value>
  </data>
  <data name="account_reset_password_new_password_confirm_error" xml:space="preserve">
    <value>Passwords don't match</value>
  </data>
  <data name="account_reset_password_new_password_confirm_place" xml:space="preserve">
    <value>Repeat your new password</value>
  </data>
  <data name="account_reset_password_btn" xml:space="preserve">
    <value>Update password</value>
  </data>
  <data name="account_reset_password_acction" xml:space="preserve">
    <value>Action not recognized</value>
  </data>
  <data name="account_reset_password_acction_subtitle" xml:space="preserve">
    <value>The link you followed doesn't correspond to a valid action.</value>
  </data>
  <data name="meta_description_user_companions" xml:space="preserve">
    <value>Add your travel companions to make filling out forms easier</value>
  </data>
  <data name="meta_description_user_home" xml:space="preserve">
    <value>Account section</value>
  </data>
  <data name="meta_description_user_profile" xml:space="preserve">
    <value>Profile edition</value>
  </data>
  <data name="meta_description_user_reservations" xml:space="preserve">
    <value>Check your reservations</value>
  </data>
  <data name="meta_description_user_security" xml:space="preserve">
    <value>Update your account security details</value>
  </data>
  <data name="meta_title_user_companions" xml:space="preserve">
    <value>Travel companions</value>
  </data>
  <data name="meta_title_user_home" xml:space="preserve">
    <value>My account</value>
  </data>
  <data name="meta_title_user_profile" xml:space="preserve">
    <value>Profile</value>
  </data>
  <data name="meta_title_user_reservations" xml:space="preserve">
    <value>Reservations</value>
  </data>
  <data name="meta_title_user_security" xml:space="preserve">
    <value>Security</value>
  </data>
      <data name="modal_payment_and" xml:space="preserve">
    <value>and</value>
  </data>
   <data name="phonereservation_title" xml:space="preserve">
  <value>Do you prefer to book by phone?</value>
  </data>
  <data name="phonereservation_subtitle" xml:space="preserve">
  <value>Ask about our special rates</value>
  </data>
<data name="room_unique" xml:space="preserve">
    <value>Unique Room</value>
</data>
</root>
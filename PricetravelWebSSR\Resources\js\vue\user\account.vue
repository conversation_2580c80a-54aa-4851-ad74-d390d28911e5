﻿<template>
    <div id="account" class="container">
        <h1 class="mb-4">{{__("profile.account")}}</h1>

        <ul class="accountgrid">
            <li v-for="item in items" :key="item.name" v-if="!item.disabled">
                <a  class="accountcard" :href="handleURL(item)">
                    <i :class="item.icon" ></i>
                    <h2 class="font-18 mt-3">{{ item.name }}</h2>
                    <p></p>
                    <p class="font-14 card-text">{{ item.description }}</p>
                </a>
            </li>
        </ul>
    </div>
</template>

<script>
    const culture = window.__pt.cultureData; 
    
    export default {
        data() {
            return {
                site: window.__pt.settings.site,
                items: [
                    { name: this.__("account.reservations_title"), description: this.__("account.reservations_description"), component: 'reservas', icon: 'icons-suitcase', type: 'link', target: 'user/reservations', targetES: 'user/reservaciones', disabled: false },
                    { name: this.__("account.profile_title"), description: this.__("account.profile_description"), component: 'perfil', icon: 'icons-person', type: 'link', target: 'user/profile', targetES: 'user/perfil', disabled: false },
                    { name: this.__("account.paxes_title"), description: this.__("account.paxes_description"), component: 'personas', icon: 'icons-guests', type: 'link', target: 'user/companions', targetES: 'user/acompañantes', disabled: true},
                    { name: this.__("account.favorites_title"), description: this.__("account.favorites_description"), component: 'favoritos', icon: 'icons-heart', type: 'link', target: `favorites`, targetES: 'favoritos', disabled: false },
                    { name: this.__("account.security_title"), description: this.__("account.security_description"), component: 'seguridad', icon: 'icons-secure', type: 'link', target: 'user/security', targetES: 'user/seguridad', disabled: false },
                ]
            }
        },
        methods: {
            handleURL(item) {
                if (culture.cultureCode == "es") {
                    return `/${culture.cultureCode}/${item.targetES}`;
                }
                return `/${culture.cultureCode}/${item.target}`;
            }
        }
    }
</script>
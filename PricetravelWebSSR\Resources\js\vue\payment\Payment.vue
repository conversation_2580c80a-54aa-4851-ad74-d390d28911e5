<template>
    <div class="">
        <h1 class="text-strong">{{ __('payment.payOnline-title') }}</h1>
        <p>{{ __('payment.payOnline-subtitle') }}</p>
        <div class="">
            <ValidationObserver ref="payObserver"
                                v-slot="{ invalid }"
                                tag="form"
                                id="payonline"
                                @submit.prevent="onSubmit">
                <div class="mt-2">
                    <ValidationProvider rules="required"
                                        ref="codigo"
                                        v-slot="{ errors }"
                                        mode="eager">
                        <label class="px-1">{{ __('payment.payOnline-code') }}</label>
                        <input v-model="form.code"
                               type="tel"
                               id="codigo"
                               name="code"
                               class="form-control"
                               :class="{ 'is-invalid': errors.length }"
                               :placeholder="__('payment.payOnline-codePlaceHolder')" />
                        <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                    </ValidationProvider>
                </div>

                <div class="mt-2">
                    <ValidationProvider rules="required|email"
                                        ref="email"
                                        v-slot="{ errors }"
                                        mode="eager">
                        <label class="px-1">{{ __('payment.payOnline-email') }}</label>
                        <input v-model="form.email"
                               type="email"
                               id="email"
                               name="email"
                               class="form-control"
                               :class="{ 'is-invalid': errors.length }"
                               :placeholder="__('payment.payOnline-emailPlaceholder')" />
                        <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                    </ValidationProvider>
                </div>

                <input type="hidden"
                       id="recaptchaToken"
                       name="recaptchaToken"
                       v-model="recaptchaToken" />

                <div class="row mt-3 padding-recaptcha">
                    <div class="cap col-12 col-lg-8">
                        <div class="g-recaptcha-grupos">
                            <div id="recaptcha-check-reservation"></div>
                        </div>
                        <p class="invalid-feedback text-left d-block mb-0"
                           v-if="recaptchaToken === '' && submitCount > 0">
                            {{ __('payment.recaptcha_error') }}
                        </p>
                    </div>
                    <div class="pt-4 col-12 col-lg-4">
                        <button id="consultarItinerario"
                                type="submit"
                                class="btn btn-blue py-10 px-5 ml-auto"
                                :disabled="invalid || (recaptchaToken === '' && submitCount > 0)">
                            {{ __('payment.payOnline-consult') }}
                        </button>
                    </div>
                </div>
            </ValidationObserver>
            
        </div>
        <Messages :msg="msg" :title='__("payment.without_results")' :btn='__("payment.try_again")' />
    </div>
</template>

<script>
    import { ValidationProvider, ValidationObserver } from 'vee-validate'
    import { __ } from '../../vue/app'
    import { Generic } from '../../utils/date-helpers/payment/generics'
    import { getWgetIdCaptcha } from '../../utils/date-helpers/payment/recaptcha'
    import { useLoaderPageStore } from '../stores/loader-page'

    export default {
        components: { ValidationProvider, ValidationObserver },

        props: {
            pageTitle: {
                type: String,
                default: ''
            }
        },
        data() {
            return {
                submitCount: 0,
                recaptchaToken: '',
                recaptchaKey: Date.now(),
                form: { code: '', email: '' },
                msg: '',
                configRecaptcha: window.__pt.settings.site.sitekeyRecaptcha,
                config: window.__pt.settings
            }
        },

        setup() {
            const store = useLoaderPageStore()
            return {
                showLoaderPage: store.showLoaderPage,
                hideLoaderPage: store.hideLoaderPage
            }
        },
        mounted() { 
            document.title = this.pageTitle;
            window.onRecaptchaSuccess = this.onRecaptchaSuccess.bind(this);

            this.waitForRecaptcha().then(() => {
                const el = document.getElementById('recaptcha-check-reservation');
                if (el) {
                    grecaptcha.render(el, {
                        sitekey: this.configRecaptcha,
                        callback: this.onRecaptchaSuccess,
                        'expired-callback': () => {
                            this.recaptchaToken = '';
                        },
                        'error-callback': () => {
                            console.error('Error al cargar el Recaptcha');
                        }
                    });
                }
            });
        },

        methods: {
            waitForRecaptcha() {
                return new Promise(resolve => {
                    const check = () => {
                        if (typeof grecaptcha !== 'undefined' && typeof grecaptcha.render === 'function') {
                            resolve();
                        } else {
                            setTimeout(check, 100); 
                        }
                    };
                    check();
                });
            },
            onRecaptchaSuccess(token) {
                this.recaptchaToken = token
                document.activeElement.blur()
                this.showRecaptchaError = false
            },
            async onSubmit() {
                this.submitCount++

                const ok = await this.$refs.payObserver.validate()
                if (!ok) {
                    const errorField = this.$el.querySelector('.is-invalid')
                    if (errorField) errorField.focus()
                    return
                }

                if (!this.recaptchaToken) {
                    this.showRecaptchaError = true

                    const recaptchaEl = document.querySelector('#recaptcha-check-reservation')
                    if (recaptchaEl) recaptchaEl.scrollIntoView({ behavior: 'smooth', block: 'center' })

                    return
                }

                this.showLoaderPage()
                const anti = document.getElementById('AntiForgeryToken')
                const _token = anti?.querySelector('input[name="_token"]')?.value || ''

                const body = new URLSearchParams({
                    id: this.form.code,
                    email: this.form.email,
                    _token,
                    recaptchaToken: this.recaptchaToken
                })

                try {

                    const res = await fetch('/v1/api/payment-online', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': _token
                        },
                        body: JSON.stringify({
                            Id: this.form.code,
                            Email: this.form.email,
                            RecaptchaToken: this.recaptchaToken
                        })
                    })


                    if (!res.ok) throw new Error(`HTTP ${res.status}`)
                    const data = await res.json()

                    const client = data.client || { id: data.request.code }
                    Generic.paymentOnline(client.id, data.status, client.type)

                    if (data.status === 'not-found' || !data.urlRedirect) {
                        this.hideLoaderPage()
                        this.msg = `
                                    <p class="mb-2 font-18 fw-bold">${this.__('payment.not_found')}</p>
                                    <p class="body">
                                        ${this.__('payment.description_not_found')}
                                        <span class="text-link">${this.__('payment.tel_' + this.config.site.siteName)}</span>
                                    </p>`

                        const modalEl = document.getElementById('modalMessages')
                        if (modalEl) new bootstrap.Modal(modalEl).show()
                        return
                    }

                    window.location.href = data.urlRedirect

                } catch (e) {
                    console.error('Error en la solicitud:', e)
                }
            },

            validateCaptcha() {
                const id = getWgetIdCaptcha('#recaptcha-check-reservation')
                const tok = grecaptcha.getResponse(id)
                if (tok) grecaptcha.reset(id)
                return tok
            }
        },
        watch: {
            msg(newVal) {
                if (newVal) {
                    this.recaptchaToken = ''
                    const id = getWgetIdCaptcha('#recaptcha-check-reservation')
                    if (typeof grecaptcha !== 'undefined') {
                        grecaptcha.reset(id)
                    }
                }
            }
        }
    }
</script>

<style scoped>
    .row-2 {
        display: flex;
        flex-wrap: wrap;
        margin-right: -15px;
    }
    .padding-recaptcha {
        margin-left: -15px;
    }

    .text-link {
        color: var(--text-link) !important;
    }
</style>

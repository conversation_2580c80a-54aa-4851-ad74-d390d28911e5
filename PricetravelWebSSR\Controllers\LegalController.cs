﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    public class LegalController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ILogger<LegalController> _logger;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _util;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _contentDeliveryNetworkHandler;
        private readonly ILoginServices _loginServices;

        public LegalController(
            IHttpContextAccessor httpContextAccessor,
            ILogger<LegalController> logger,
            IOptions<SettingsOptions> options,
            ViewHelper util,
            IAlternateHandler alternateHandler,
            ICommonHandler commonHandler,
            IContentDeliveryNetworkHandler contentDeliveryNetworkHandler,
            ILoginServices loginServices)
        {
            _httpContextAccessor = httpContextAccessor;
            _logger = logger;
            _options = options.Value;
            _util = util;
            _alternateHandler = alternateHandler;
            _commonHandler = commonHandler;
            _contentDeliveryNetworkHandler = contentDeliveryNetworkHandler;
            _loginServices = loginServices;
        }

        [Route("/{culture}/legal")]
        public async Task<ActionResult> Index(string culture)
        {
            var route = HomeMapper.Path(Request.Path.Value ?? "");
            try
            {
                var path = HomeMapper.GetPath(route, _options);

                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path=$"legal", Type = Types.PageType.Generic }, cts.Token);
                var meta = MetaMapper.LegalMapper("legal", "/", _options, _util, userSelection.Culture);

                ViewData["MetaTag"] = meta;
                ViewData["Alternates"] = alternates;
                ViewData["PageRoot"] = path;
                ViewData["PageOrig"] = route.ToLower().Trim().Replace("/", "");
                ViewData["IsRoutMain"] = route != "/" ? route : "/legal";
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;

                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Legal Message: {e.Message}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }
        }

        [HttpGet("{culture}/legal/{name:regex(terms-and-conditions|privacy-policy|profeco-contract)}/{country?}")]
        public async Task<ActionResult> Legales(string name, string? country)
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
                var userCountry = userSelection.Context.Location.Country;
                var content = await _contentDeliveryNetworkHandler.QueryAsync(new LegalContentRequest() { Path = name, UserCountry = userCountry }, cts.Token);
                var route = Request.Path.Value ?? "";
                var meta = MetaMapper.LegalMapper(name, "/", _options, _util, userSelection.Culture);
                var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = userSelection.Culture.CultureCode, Id = 0, Path = $"legal/{name}/{country}", Route = route, Type = Types.PageType.Generic }, cts.Token);

                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = HomeMapper.GetPath(route, _options);
                ViewData["PathName"] = name;
                ViewData["LegalContent"] = content;
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["Alternates"] = alternates;

                return View();
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] Info Message: {e.Message} - Name: {name}");
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }

        }
        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }

    }
}
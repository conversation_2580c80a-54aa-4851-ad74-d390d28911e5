@using PricetravelWebSSR.Helpers
@using PricetravelWebSSR.Models.Configuration
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@{

    var resolution = ViewData["Resolution"] as SectionResolution;
    var target = resolution.Device == "mobile" ? "_self" : "_blank";
}
<div class="col-md-12 mb-4 car-selected" ng-repeat="(hotelIndex, hotel ) in group">
    <p class="suggested-header" ng-if="vm.profileId == hotel.hotelId">@viewHelper.Localizer("your_search")</p>
    <a id="hotelLink" class="HotelCard"
       ng-class="{'HotelCard--suggested': vm.profileId == hotel.hotelId , 'HotelCard--recommendedDates': hotel.dateRecommended.length && vm.profileId == hotel.hotelId && !hotel.price }"
       href="{{hotel.url}}"
       target="@(target)"
       title="{{hotel.name}}"
       ng-click="vm.onSelectHotel(hotel,$event)">
        <!-- PHOTO -->
        <div class="HotelCard__photo">
            <div ng-if="hotel.isLoadGallery === false" class="Loading d-center">
                @await Html.PartialAsync("_Loading")
            </div>
            <span class="photo__badge" ng-if="hotel.taxes.mealPlanCode != 'E' && hotel.taxes.mealPlanCode != 'AR'">
                <i class="font-16" 
                    ng-class="hotel.taxes.mealPlanCode === 'I' || hotel.taxes.mealPlanCode === 'AZ' ? 'icons-all-inclusive' : 'icons-restaurant'"
                    ng-show="vm.mealplans[hotel.taxes.mealPlanCode]"></i>
                {{vm.mealplans[hotel.taxes.mealPlanCode]}}
            </span>
            <button class="photo__favBtn" aria-label="@viewHelper.Localizer("add_to_favs")" ng-class="{ 'active': vm.isFavoriteHotel(hotel.hotelId) || hotel.isFavorite}" ng-click="vm.addFavorites($event,hotel)">
                <i class="icons-heart d-flex"></i>
            </button>
            <img width="@resolution.List.WidthB"
                 height="@resolution.List.HeightB"
                 class="hotel-card-img"
                 bn-lazy-src="{{ (hotel.picture.cloudUri || hotel.mainPhoto.cloudUri) + '?tx=g_auto,w_' + @resolution.List.WidthB + ',h_' + @resolution.List.HeightB + ',c_fill' }}"
                 alt="{{hotel.name}}"
                 data-hotel="{{hotel.hotelId}}"
                 draggable="false">
            <button class="btnIcon btnIcon--onBg btnIcon--md btnIcon--left" ng-click="vm.slideCarrousel($event, hotel, 'left', false)" aria-label="@viewHelper.Localizer("list_paginator_prev")">
                <i class="icons-angle-left d-flex"></i>
            </button>
            <button class="btnIcon btnIcon--onBg btnIcon--md btnIcon--right" ng-click="vm.slideCarrousel($event, hotel, 'right', false)" aria-label="@viewHelper.Localizer("list_paginator_next")">
                <i class="icons-angle-right d-flex"></i>
            </button>
        </div>

        <!-- CONTENT -->
        <div class="HotelCard__content">
            <div class="flex-grow-1">
                <p class="content__location d-none d-sm-block mb-0">{{hotel.locationInfo.city}}</p>
                <h2>{{hotel.name}}</h2>
                <div class="mb-1 content__direction flex-wrap">
                    <i ng-class="{'content__border': vm.areAllServicesNotHighlighted(hotel.services) && vm.showServices}" class="content__stars {{hotel.stars | getIconStars}}"></i>
                    <!-- Highlighted Icons -->
                    <div class="content__importantServices" ng-if="vm.showServices">
                        <div class="importantServices__item" ng-repeat="service in hotel.services" ng-if="!service.highlited">
                            <i class="{{service.icon}}"></i>
                            <span class="d-none d-sm-inline-block" ng-class="{'importantServices__ellipsis': service.text.length >= 20}">{{service.text}}</span>
                        </div>
                    </div>
                </div>
                <!-- Highlighted Tags -->
                <div class="d-none d-sm-flex flex-wrap my-1" style="gap: 0.25rem;" ng-repeat="service in hotel.services" ng-if="service.highlited">
                    <span class="content__highlightedTag">{{service.text}}</span>
                </div>

                <!-- Highlights -->
                <ul class="content__highlights d-none d-sm-block" ng-if="!hotel.recommendedDates">
                    <li class="highlights__item" ng-repeat="highlight in hotel.highlights | limitTo: vm.config.limitHighlightElement" title="{{highlight.info}}">
                        <i class="icons-check d-inline-flex"></i>
                        <span class="item">{{ highlight.info }}</span>
                    </li>
                    @if (settingOptions.Value.ShowPromotionsTags)
                    {
                        <li class="highlights__item" ng-show="hotel.showPromotion && !hotel.showHotSBlackFri"
                            ng-class="{ 'offer': !hotel.showHotSBlackFri, 'hotoffer': hotel.showHotSBlackFri}">
                            <i class="icons-label-right d-inline-flex"></i>
                            <span class="item">{{hotel.taxes.promotion}}</span>
                        </li>
                    }
                    <!-- Hotel Cobra tag -->
                    <li class="highlights__item align-items-start" ng-show="hotel.taxes.collectType === 2">
                        <i class="icons-check d-inline-flex"></i>
                        <span><strong class="item--promotion">@viewHelper.Localizer("no_prepayment")</strong> - @viewHelper.Localizer("pay_at_property")</span>
                    </li>
                </ul>
            </div>

            @if (settingOptions.Value.LoginActive)
            {
                <!-- Promo Login -->
                <div class="LoginPromo d-none d-md-flex" ng-if="hotel.total_saved && !hotel.notMessage && !hotel.recommendedDates">
                    <i class="icons-label-right"></i>
                    <p ng-if="!vm._isLogin">
                        <span ng-if="hotel.notAmount">@viewHelper.Localizer("room_table_save")</span>
                        <strong ng-if="!hotel.notAmount">- <currency-display amount="hotel.total_saved"></currency-display> </strong> @viewHelper.Localizer("room_table_save_sesion")
                    </p>
                    <p ng-if="vm._isLogin">
                        @viewHelper.Localizer("room_table_saved")
                        <strong>
                            <currency-display amount="hotel.total_saved"></currency-display>
                        </strong>
                    </p>
                </div>
            }


            <!-- Rating -->
            <div class="Rating mt-md-2" ng-if="hotel.surveyAverage && hotel.surveyAverage.totalSurveys >= 5 && !hotel.dateRecommended.length">
                <span class="rating__tag">{{(hotel.surveyAverage.averageValue * 2)}}</span>
                <div class="d-flex flex-wrap flex-md-column">
                    <span class="rating__category">{{hotel.surveyAverage.averageValue | getRatingDesc}}&nbsp;</span>
                    <span class="d-none d-sm-inline-block">
                        {{ (hotel.surveyAverage.totalSurveys  | number ) | commanseparatecolo}} @viewHelper.Localizer("titleReviews")
                    </span>
                </div>
            </div>

            <div class="mt-1 d-flex flex-wrap d-sm-none" style="gap: 0.25rem;" ng-repeat="service in hotel.services" ng-if="service.highlited">
                <span class="content__highlightedTag">{{service.text}}</span>
            </div>
        </div>

        <!-- FOOTER -->
        <div class="HotelCard__footer" ng-show="!vm.loadingRates && hotel.price">

            @if (settingOptions.Value.LoginActive)
            {
                <!-- Promo Login -->
                <div class="LoginPromo d-md-none" ng-if="hotel.total_saved && !hotel.notMessage && !hotel.recommendedDates">
                    <i class="icons-label-right"></i>
                    <p ng-if="!vm._isLogin">
                        <span ng-if="hotel.notAmount">@viewHelper.Localizer("room_table_save")</span>
                        <strong ng-if="!hotel.notAmount">- <currency-display amount="hotel.total_saved"></currency-display> </strong> @viewHelper.Localizer("room_table_save_sesion")
                    </p>
                    <p ng-if="vm._isLogin">@viewHelper.Localizer("room_table_saved") <strong><currency-display amount="hotel.total_saved"></currency-display></strong></p>
                </div>
                <div class="LoginPromo d-md-none" ng-hide="hotel.total_saved && !hotel.notMessage" style="visibility: hidden;">
                    _
                    <div style="height: 18px;"></div>
                    <p class="mb-0">x</p>
                </div>
            }

            <p class="mb-0 font-12">@viewHelper.Localizer("list_room_per_night")</p>
            <!-- Discount -->
            <div ng-if="hotel.taxes.discount > 0">
                <span class="DiscountTag" ng-class="{'sale-icon': hotel.showHotSBlackFri , 'disccount-icon': !hotel.showHotSBlackFri}">
                    {{ hotel.taxes.discount | number: 0 }} %
                </span>
                <s class="footer__discountedPrice">
                    <currency-display amount="hotel.taxes.totalRoomRateBeforePromoPerNight"></currency-display>
                </s>
            </div>
            <!-- Price per Night -->
            <div class="footer__pricePerNight">
                <i class="icons-flame1" ng-if="hotel.showHotSBlackFri"></i>
                <currency-display amount="( hotel.sourceTriGooHotelsIds ? hotel.taxes.totalRoomRatePerNight + hotel.taxes.totalTaxesPerRoomPerNight + hotel.taxes.feesAmountTotalForNight  : hotel.taxes.totalRoomRatePerNight)" show-currency-code="true" reduce-iso-font="true" ></currency-display>
            </div>
            <!-- Taxes -->
            <p class="mb-0 font-12" ng-if="!hotel.taxes.hasTaxes && (hotel.taxes.totalTaxesPerRoomPerNight == 0 && hotel.taxes.feesAmountTotalForNight == 0) && !hotel.sourceTriGooHotelsIds">
                @viewHelper.Localizer("list_card_tax_no_include")
            </p>
            <p class="mb-0 font-12" ng-if="hotel.taxes.hasTaxes && (hotel.taxes.totalTaxesPerRoomPerNight == 0 && hotel.taxes.feesAmountTotalForNight == 0) || hotel.sourceTriGooHotelsIds">
                @viewHelper.Localizer("list_taxes_included")
            </p>
            <p class="mb-0 font-12" ng-if="(hotel.taxes.totalTaxesPerRoomPerNight > 0 || hotel.taxes.feesAmountTotalForNight) && !hotel.sourceTriGooHotelsIds">
                + <currency-display amount="hotel.taxes.totalTaxesPerRoomPerNight + hotel.taxes.feesAmountTotalForNight "></currency-display>
             <span ng-if="hotel.taxes.feesAmountTotalForNight > 0">@viewHelper.Localizer("taxes_and_charges_fee")</span><span ng-if="hotel.taxes.feesAmountTotalForNight == 0">@viewHelper.Localizer("taxes_and_charges")</span>
            </p>

            <!-- Total Price -->
            <p class="footer__totalPrice">
                <span ng-if="hotel.taxes.rooms > 1 && hotel.taxes">
                    @viewHelper.Localizer("total_amount")
                    {{ hotel.taxes.rooms }} @viewHelper.Localizer("list_rooms_abre")
                </span>
                <span ng-if="hotel.taxes.rooms == 1 && hotel.taxes">
                    @viewHelper.Localizer("total_amount")
                </span>
                <currency-display amount="hotel.taxes.totalRoomRate + hotel.taxes.feesAmountTotal" show-currency-code="true"></currency-display>
            </p>
            <!-- Cancellation Policy -->
            <p class="mb-0 font-12"
               ng-if="hotel.taxes.cancellationPolicies.freeCancellationExpire && hotel.taxes.cancellationPolicies.freeCancellationExpire.length">
                @viewHelper.Localizer("list_free_cancel")
            </p>
            <!-- Limited Promotion Text -->
            @if (settingOptions.Value.ShowPromotionsTags)
            {
                <p class="footer__promoText mb-0" ng-show="hotel.showPromotion && !hotel.showHotSBlackFri">{{hotel.taxes.promotion}}</p>
            }
            <!-- Hotel Cobra tag -->
            <p class="mb-0 footer__highlight d-sm-none" ng-show="hotel.taxes.collectType === 2" style="text-wrap: balance;">
                <i class="icons-check-circle"></i>
                <strong class="highlight__text">@viewHelper.Localizer("no_prepayment")</strong> - @viewHelper.Localizer("pay_at_property")
            </p>
        </div>
        <!-- FOOTER No Availability -->
        <div class="HotelCard__footer  py-3" ng-if="!vm.loadingRates && !hotel.price && !hotel.isLoading && vm.shouldDisplayCard(hotel)">
            <p class="mb-0 text-right font-14">
                <strong class="d-block">@viewHelper.Localizer("list_unavailability_message_one")</strong>
                @viewHelper.Localizer("list_unavailability_message_one_detail")
            </p>
        </div>
        <!-- FOOTER Recommended Dates -->
        <div class="HotelCard__footer" ng-if="!vm.loadingRates && !hotel.price && hotel.dateRecommended.length && vm.profileId == hotel.hotelId">
            <p class="footer__suggestedDatesMessage">
                <i class="icons-error font-18 font-secondary"></i>
                <span>
                    @viewHelper.Localizer("we_have_this_dates") 
                    <span class="font-500 font-main">{{vm.getRecomendateDatePax()}}</span>
                </span>
            </p>
            <div class="footer__suggestedDatesContainer">
                <button class="suggestedDatesContainer__dateOption" ng-repeat="(index, date) in hotel.dateRecommended | limitTo: 3" ng-click="vm.onSelectDate($event,hotel, date)">
                    <div class="dateOption__header">
                        <span class="d-md-none">{{date.checkinDayAndMonth}} - {{date.checkoutDayAndMonth}}</span>
                        <strong class="font-md-14">
                         {{vm.getRecomendateDatePaxNight(date.dateDifference)}}
                        </strong>
                    </div>
                    <div class="d-none d-md-flex w-100">
                        <div class="flex-grow-1 p-1 d-flex flex-column align-items-center">
                            <span class="font-subtle font-md-12">{{date.checkinDayOfWeek}}</span>
                            <span class="font-md-14">{{date.checkinDayAndMonth}}</span>
                        </div>
                        <div class="divider"></div>
                        <div class="flex-grow-1 p-1 d-flex flex-column align-items-center">
                            <span class="font-subtle font-md-12">{{date.checkoutDayOfWeek}}</span>
                            <span class="font-md-14">{{date.checkoutDayAndMonth}}</span>
                        </div>
                    </div>
                    <div class="dateOption__footer font-md-14">
                        <span> @viewHelper.Localizer("from_txt") <strong class="font-14 font-main"> <currency-display amount="date.price * date.dateDifference"></currency-display> </strong></span>
                        <i class="icons-angle-right font-18 font-subtle d-flex d-md-none"></i>
                    </div>
                </button>
            </div>

            <!-- Rating -->
            <div class="Rating mt-md-2 d-none d-md-flex" ng-if="hotel.surveyAverage && hotel.surveyAverage.totalSurveys >= 5 && hotel.dateRecommended.length">
                <span class="rating__tag">{{(hotel.surveyAverage.averageValue * 2)}}</span>
                <div class="d-flex flex-wrap flex-md-column">
                    <span class="rating__category">{{hotel.surveyAverage.averageValue | getRatingDesc}}&nbsp;</span>
                    {{ (hotel.surveyAverage.totalSurveys  | number ) | commanseparatecolo}} @viewHelper.Localizer("titleReviews")
                </div>
            </div>
        </div>
        <!-- Skeleton -->

        <div class="HotelCard__footer" ng-if="vm.loadingRates || (!hotel.price  && hotel.isLoading)">
            <div class="skeleton-primary mb-1" ng-style="{'width': hotel.skeleton.skeleton1}"></div>
            <div class="skeleton-primary my-1" ng-style="{'width': hotel.skeleton.skeleton2}" style="height: 24px;"></div>
            <div class="skeleton-primary mb-1" ng-style="{'width': hotel.skeleton.skeleton3}"></div>
            <div class="skeleton-primary mb-2" ng-style="{'width': hotel.skeleton.skeleton4}" style="height: 22px;"></div>
            <div class="skeleton-primary mb-1" ng-style="{'width': hotel.skeleton.skeleton5}"></div>
        </div>
    </a>

    <!-- Tag externo pegado a la card -->
    <span class="phone-external-tag" ng-if="hotel.phone">
        <i class="icons-phone"></i>
        <div class="phone-tooltip-modal">
            <div class="tooltip-content">
                <h4>@viewHelper.Localizer("phonereservation_title")</h4>
                <p>@viewHelper.Localizer("phonereservation_subtitle")</p>
                <a href="tel:{{hotel.phone.phoneBase}}" class="phone-link">
                    <i class="icons-phone"></i>
                    <span>{{ hotel.phone.phoneFormatted }}</span>
                </a>
            </div>
        </div>
    </span>
</div>

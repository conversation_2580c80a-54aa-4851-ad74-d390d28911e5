﻿<template>
    <div id="info" class="container my-3 my-lg-5">
        <div v-if="getContentSelected != ''" v-html="getContentSelected" @click="handleLandmarks"></div>
        <div v-else>
            <img :width="product === 'tiquetesbaratos' ? '150' : '300'" :src="`${cdn}${img}`" class="d-block mr-auto ml-auto mb-3">
            <h3 class="d-center mb-5"> 404 </h3>
        </div>
    </div>
</template>
<script>
    import { storeToRefs } from 'pinia';
    import { useLegalStore } from '../../stores/legal';

    const site = window.__pt.settings.site;
    const culture = window.__pt.culture;
    
    export default {
        props: {
            content: null,
            product: null,
            img: null,
            cdn: null,
        },
        setup() {
            const useLegal = useLegalStore();
            const { setResponse, changeLanguage, setContentSelectedInitial } = useLegal;
            const { isStaticContent, getContentSelected } = storeToRefs(useLegal);
            return { setResponse, isStaticContent, changeLanguage, getContentSelected, setContentSelectedInitial }
        },
        mounted() {
            this.setResponse(this.content);
            this.setContentSelectedInitial();
        },
        methods: {
            handleLandmarks(event) {
                if (event.target.tagName === 'A' && event.target.getAttribute('href').startsWith('#')) {
                    event.preventDefault();
                    const targetId = event.target.getAttribute('href').substring(1);
                    this.scrollToSection(targetId);
                }
            },
            scrollToSection(sectionId) {
                const section = document.getElementById(sectionId);
                if (section) {
                    section.scrollIntoView({ behavior: 'smooth' });
                }
            },
        },
    }
</script>
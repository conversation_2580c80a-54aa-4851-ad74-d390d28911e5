﻿using PricetravelWebSSR.Models.Response;
using ProtoBuf;
using System.Text.Json.Serialization;

namespace PricetravelWebSSR.Models.User.Reservation
{


	public class PaginatorRequest
	{
		public int Limit { get; set; } = 10;
		public int Page { get; set; } = 1;
		public string? Search { get; set; } = string.Empty;
		public string? Category { get; set; } = string.Empty;
        public bool Cache { get; set; }

    }

    public class UserReservationRoot
	{
		public int Total { get; set; }
		public int CurrentPage { get; set; }
		public int From { get; set; }
		public int To { get; set; }
		public int LastPage { get; set; }
		public int PerPage { get; set; }
		public string Category { get; set; } = string.Empty;
		public List<ProductReservation> Data { get; set; } = [];

	}

	[ProtoContract]
	public class ProductReservation
	{

		[ProtoMember(1)]
		public string BookingReference { get; set; } = string.Empty;

		[ProtoMember(2)]
		public string Email { get; set; } = string.Empty;

		[ProtoMember(3)]
		public string Name { get; set; } = string.Empty;

		[ProtoMember(4)]
		public string ProductType { get; set; } = string.Empty;

		[ProtoMember(5)]
		public string BookingStatus { get; set; } = string.Empty;

		[ProtoMember(6)]
		public DateTime CheckoutStartDate { get; set; }

		[ProtoMember(7)]
		public DateTime CheckoutEndDate { get; set; }

		[ProtoMember(8)]
		public DateTime FlightStartDate { get; set; }

		[ProtoMember(9)]
		public DateTime FlightEndDate { get; set; }

		[ProtoMember(10)]
		public string Destination { get; set; } = string.Empty;

		[ProtoMember(11)]
		public string Description { get; set; } = string.Empty;

		[ProtoMember(12)]
		public string ImageUrl { get; set; } = string.Empty;

		[ProtoMember(13)]
		public ExtraInfoProduct ExtraInfo { get; set; } = new();

		[ProtoMember(14)]
		public List<string> Tags { get; set; } = [];

        [ProtoMember(15)]
        public DateTime CreatedAt { get; set; }

        [ProtoMember(16)]
        public double TotalAmount { get; set; }
        [ProtoMember(17)]
        public double PaidAmount { get; set; }

        [ProtoMember(18)]
        public double RemainingAmount { get; set; }

        [ProtoMember(19)]
        public string Currency { get; set; } = string.Empty;

        [JsonIgnore]
        public string Culture { get; set; } = string.Empty;

        [JsonIgnore]
        public bool Cache { get; set; }

		[ProtoMember(20)]
		public string ServiceCarrierCode { get; set; } = string.Empty;

        [ProtoMember(21)]
        public int Adults { get; set; } = 0;

        [ProtoMember(22)]
        public int Kids { get; set; } = 0;

        [ProtoMember(23)]
        public string DepartureCode { get; set; } = string.Empty;


        [ProtoMember(24)]
        public string DepartureName { get; set; } = string.Empty;


        [ProtoMember(25)]
        public string ArrivalCode { get; set; } = string.Empty;

        [ProtoMember(26)]
        public string ArrivalName { get; set; } = string.Empty;
        [ProtoMember(27)]
        public string HotelUri { get; set; } = string.Empty;
        [ProtoMember(28)]
        public string ServiceCarrierName { get; set; } = string.Empty;
        [ProtoMember(29)]
        public string ServiceCarrierNameReturn { get; set; } = string.Empty;
        [ProtoMember(30)]
        public List<Segments> Segments { get; set; } = [];
        [ProtoMember(31)]
        public string CustomerName { get; set; }
        [ProtoMember(32)]
        public string CustomerFirstName { get; set; }
        [ProtoMember(33)]
        public string CustomerLastName { get; set; }
    }

	[ProtoContract]
	public class ExtraInfoProduct
	{
		[ProtoMember(1)]
		public bool IsRoundtrip { get; set; }

        [ProtoMember(2)]
        public bool BookNowPayLater { get; set; }

        [ProtoMember(3)]
        public bool HasPayment { get; set; }

        [ProtoMember(4)]
        public bool HotelCollect { get; set; }

        [ProtoMember(5)]
        public bool HasPaymentHotelCollect { get; set; }

        [ProtoMember(6)]
        public int Rooms { get; set; }
        [ProtoMember(7)]
        public string Lat { get; set; } = string.Empty;

        [ProtoMember(8)]
        public string Lon { get; set; } = string.Empty;

        [ProtoMember(9)]
        public string Street { get; set; } = string.Empty;

        [ProtoMember(10)]
        public double Stars { get; set; } = 0;

        [ProtoMember(11)]
        public string ConfirmationCode { get; set; } = string.Empty;
        [ProtoMember(12)]
        public string ConfirmationCodeReturn { get; set; } = string.Empty;

    }
}

﻿import { getDate } from "./dates";
import _ from 'lodash'

export const UTILS = {
    events: {
        gtmEvent: "gtmEvent",
        trackEvent: "trackEvent",
        ads: "ads",
        event_impresion_view: "event-impression-view-pack",
        event_impression_click: "event-impression-click-pack",
        event_product_detail: "event-product-detail-pack",
        add_to_card: "addToCart",
        select_content: "select_content",
        news_letter_subscription: "newsletterSubscription",
        view_item_list: "view_item_list",
        select_item: "select_item",
        select_item_returning: "select_item_returning",
        selected_roundtrip_flight: "vuelo seleccionado",
        roundtrip_flight: "aerolinea con oferta redondo",
        matrix: "matrix"
    },
    categories: {
        list_filters: "Hotel+Flight List Filters",
        search: "Hotel+Flight Search",
        view_flight_detail: "Hotel+Flight View Flight Detail",
        list_gallery: "Hotel+Flight List Gallery",
        hotel_detail_page: "Package Hotel detail page",
        modal: "Hotel+Flight Modal",
        chage_flight: "Change Flight Hotel+Flight",
        change_flight_filter: "Hotel+Flight Change Flight Filters",
        upsell: "Hotel+Flight Upsell",
        load_more_flights: "Hotel+Flight Load More Flights",
        header: "Header",
        error: "Error",
        flights_list: "FlightsList",
        consulta_reserva: "consulta_reserva",
        recientes_autocomplete: "recientes_autocomplete",
        autocomplete_mas_buscados: "autocomplete_mas_buscados",
        recientes_tarjetas_home: "recientes_tarjetas_home",
        recotizacion_lista: "recotizacion-lista",
        filtros_sin_vuelos: "filtros-sin-vuelos"
    },
    actions: {
        hotel_list: "Hotel List",
        click_more_images: "Click More Images",
        hotel_detail: "Hotel Detail",
        show_modal: "Show Modal User Selection",
        click_modal_family: "Modal Click Family Fare",
        modal_continue_upsell: "Modal Continue With Upsell",
        flight_list: "Flight List",
        package_list: "Package List",
        hotel_error: "Hotel+Flight Error",
        modal_view_flight: "Modal View Flight Detail",
        more_groups: "More Groups",
        search_again_display: "Search again Display",
        search_again_click: "Search again Click",
        call_me: "call-me",
        payment_online: "pago-en-linea",
        phones: "phones",
        ruta_sin_vuelos: "ruta-sin-vuelos",
        flight_modal: "flight-modal",
        economic: "economic",
        near_dates: "near-dates",
        hoteles_paquetes: "hoteles_paquetes",
        round_special_rate: "tarifa_especial_redondo",
        brands_modal: "brands-modal"
    },
    pages: {
        hotel_list: "list",
        hotel_detail: "detail",
        flight_change: "flight_list"
    },
    misc: {
        separator: "|",
        source_list: "spa-package-list-hotel",
        source_flight: "spa-package-list-flight",
        variant: "Available",
        pricetravel: "Pricetravel",
        tiquetesbaratos: "TiquetesBaratos",
        domestic: "Domestic",
        no_domestic: "Non domestic",
        international: "International",
        results: "Resultados",
        layer: "paquetes"
    }
}


export const setDatalayer = (arg) => {
    if (window.dataLayer) {
        window.dataLayer.push(arg)
    }
};

export const rangeNight = (checkIn, checkOut) => {
    const diff = Math.abs(
        getDate(checkOut).getTime() - getDate(checkIn).getTime()
    );
    const nights = diff / (86400000);

    if (nights > 0 && nights <= 3) {
        return '1 to 3';
    } else if (nights > 3 && nights <= 6) {
        return '4 to 6';
    } else if (nights > 6 && nights <= 9) {
        return '6 to 9';
    } else {
        return '10+';
    }
};

export const countNight = (checkIn, checkOut) => {
    const diff = Math.abs(
        getDate(checkOut).getTime() - getDate(checkIn).getTime()
    );

    const nights = diff / (86400000);

    return nights;
}

export const isoWeekNumber = (date = new Date()) => {
    const newDate = date;
    const dayNum = date.getUTCDay() || 7;

    newDate.setUTCDate(newDate.getUTCDate() + 4 - dayNum);
    const yearStart = new Date(Date.UTC(newDate.getUTCFullYear(), 0, 1));

    return `${Math.ceil(
        ((newDate.getTime() - yearStart.getTime()) / 86400000 + 1) / 7
    )}`;
}


export const getPaxes = (paxesData) => {
    let paxesLength = paxesData.length;
    let paxes = {
        adults: 0,
        children: 0,
        format: [],
        join: ''
    };

    for (var i = 0; i < paxesLength; i++) {
        const pax = paxesData[i];

        paxes.adults += pax.adults;
        paxes.children += pax.children.length;
    }

    paxes.format.push(`a:${paxes.adults}`);
    paxes.format.push(`k:${paxes.children}`);
    paxes.format.push(`kwId:0`); //validar que valor se envia
    paxes.format.push(`kwT:0`); //validar que valor se envia

    paxes.join = paxes.format.join(UTILS.misc.separator);

    return paxes;
}

export const flightToString = flight => {
    const airline = _.get(flight, 'airlineCode', '');
    const departureDate = _.get(flight, 'departure.date', '');
    const departureTime = _.get(flight, 'departure.time', '');
    const arrivalDate = _.get(flight, 'arrival.date', '');
    const arrivalTime = _.get(flight, 'arrival.time', '');
    const duration = _.get(flight, 'flightDuration', '');
    const scales = _.get(flight, 'scales', '');

    return `${airline} | ${departureDate} ${departureTime} - ${arrivalDate} ${arrivalTime} | ${duration} | ${scales}`;
};

export const getChildren = (childrenData) => {
    let children = {
        kids: 0,
        infants: 0
    };

    for (let index in childrenData) {
        if (!isNaN(parseFloat(childrenData[index]))) {
            if (childrenData[index] < 2) {
                children.infants++;
            } else {
                children.kids++;
            }
        }
    }
    return children;
};
﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{
    [ProtoContract]
    public class Country
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public int Type { get; set; }

        [ProtoMember(3)]
        public string Name { get; set; }

        [ProtoMember(4)]
        public string Uri { get; set; }

        [ProtoMember(5)]
        public string Code { get; set; }

        [ProtoMember(6)]
        public string Latitude { get; set; }

        [ProtoMember(7)]
        public string Longitude { get; set; }

        [ProtoMember(8)]
        public int NumberOfCities { get; set; }
        [ProtoMember(9)]
        public string? NameSp { get; set; }
        public Country()
        {
            Name = string.Empty;
            Uri = string.Empty;
            Code = string.Empty;
            Latitude = string.Empty;
            Longitude = string.Empty;
        }
    }

}

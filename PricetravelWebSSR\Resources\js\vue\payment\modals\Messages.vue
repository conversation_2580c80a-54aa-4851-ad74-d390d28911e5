<template>
    <div class="modal fade modal-tb" id="modalMessages" data-backdrop="static" data-keyboard="false" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered modal-lg  modal-padding">
            <div class="modal-content">
                <div class="modal-header bg-base d-flex justify-content-between">
                    <h2 class="mb-0 font-20 font-main text-strong" v-html="title"></h2>
                    <span class="icon icons-close icon-xl ms-auto cursor-pointer" @click="closeModal" 
                          data-bs-dismiss="modal" aria-label="cerrar ventana emergente"></span>
                </div>
                <div class="modal-body">
                    <div class="d-flex gap-2">
                        <span class="icon icons-info icon-warning icon-xl" alt="" style="margin-right:10px;"></span>
                        <div v-html="msg"></div>
                    </div>
                    <div class="d-flex justify-content-end font-16">
                        <button aria-label="intenta de nuevo" class="btn btn-blue" data-bs-dismiss="modal" @click="closeModal">{{btn}}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        props: {
            msg: String,
            title: String,
            btn: String
        },
        methods: {
            closeModal() {
                const modal = document.getElementById('modalMessages');
                if (modal) {
                    modal.classList.remove('show');
                    modal.style.display = 'none';
                    modal.setAttribute('aria-hidden', 'true');
                    document.body.classList.remove('modal-open');
                    const backdrop = document.querySelector('.modal-backdrop');
                    if (backdrop) backdrop.remove();
                }
            }
        }
    
    }
</script>
<style scoped>
    .text-link {
        color: var(--text-link) !important;
    }
    .btn-blue, .btn-blue:hover {
        background: #2196f3 !important;
        color: #fff !important;
    }

    .btn-blue {
        border: none;
        border-radius: 4px;
        font-family: Roboto-Medium;
        transition: all .2s linear;
    }

    .btn {
        font-size: 15px;
    }


    .modal.show .modal-dialog {
        transform: none;
    }

    .modal.fade .modal-dialog {
        transition: transform .3s ease-out;
    }

    @media (min-width: 576px) {
        .modal-dialog {
            margin-left: auto;
            margin-right: auto;
            max-width: var(--bs-modal-width);
        }
    }

    .modal-dialog-centered {
        align-items: center !important;
        display: flex !important;
    }

    .modal-dialog {
        margin: var(--bs-modal-margin);
        pointer-events: none;
        position: relative;
        width: auto;
    }

    *, :after, :before {
        box-sizing: border-box;
    }


    .modal-padding {
        padding: 0 100px;
    }

    @media (min-width: 992px) {
        .modal-padding {
            padding: 0 450px;
        }
    }

    @media (min-width: 575px) {
        .modal-padding {
            padding: 0 100px;
        }
    }

    @media (max-width: 575px) {
        .modal-padding {
            padding: 0 0px;
        }
    }
  
</style>
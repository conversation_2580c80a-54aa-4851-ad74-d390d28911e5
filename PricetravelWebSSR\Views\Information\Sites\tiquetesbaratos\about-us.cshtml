@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Types;

@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper
@{

    var resolution = viewHelper.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = viewHelper.IsRobot();
    var cultureConf = ViewData["cultureData"] as Culture;
    var legalContent = ViewData["LegalContent"] as List<LegalContentResponse>;


    var page = (string)ViewData["PageRoot"];
    var pathName = (string)ViewData["PathName"];

    var imgPath = viewHelper.Localizer("img", @settingOptions.Value.AppName.ToLower());

    ViewData["Page"] = "info";

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;
}


<div class="container" id="app-main">
    <Info :content='@Json.Serialize(legalContent)' :product='@Json.Serialize(settingOptions.Value.SiteName)' :img='@Json.Serialize(imgPath)' :cdn='@Json.Serialize(settingOptions.Value.CloudCdn)'/>
</div>

@section Scripts {

    <script type="text/javascript">
        window.__pt = window.__pt || {};
        window.__pt.legalContent = @Html.Raw(viewHelper.ToJsonString(legalContent));
    </script>


    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_pdv.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_profile.min.js")"></script>
    <script src="@staticHelper.GetVersion($"/assets/js/vue/lang/{cultureConf.CultureCode}.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/controllers/login-controllers.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/vue/app.js")"></script>
}
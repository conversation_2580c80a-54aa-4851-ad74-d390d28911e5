﻿using PricetravelWebSSR.Models.Response;
using System.Text.Json.Serialization;

namespace PricetravelWebSSR.Models.PaymentOnline
{
    public class PaymentOnlineResponse
    {
        public string Status { get; set; } = string.Empty;
        public string Message { get; set; } = string.Empty;
        public string KeyValidation { get; set; } = string.Empty;
        public string UrlRedirect { get; set; } = string.Empty;
        public PaymentOnlineRequest Request { get; set; } = new();

        [JsonIgnore]
        public TravelItinerary TravelItinerary { get; set; } = new();
        public PaymentOnlineClient Client { get; set; } = new();

    }

    public class PaymentOnlineClient
    {
        public string Name { get; set; } = string.Empty;
        public string Lastname { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public double TotalAmount { get; set; }
        public double ServiceAmountPaid { get; set; }
        public int Id { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;



    }
}

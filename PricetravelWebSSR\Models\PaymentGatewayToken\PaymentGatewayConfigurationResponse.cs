﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.PaymentGatewayToken
{
    [ProtoContract]
    public class PaymentGatewayConfigurationResponse
    {
        [ProtoMember(1)]
        public int PaymentGatewayConfigurationId { get; set; }

        [ProtoMember(2)]
        public int ChannelId { get; set; }

        [ProtoMember(3)]
        public int ChannelGroupId { get; set; }

        [ProtoMember(4)]
        public int AffiliateId { get; set; }

        [ProtoMember(5)]
        public int AffiliateSiteId { get; set; }

        [ProtoMember(6)]
        public int AgencyId { get; set; }

        [ProtoMember(7)]
        public string Description { get; set; } = string.Empty;

        [ProtoMember(8)]
        public string ApplicationName { get; set; } = string.Empty;

        [ProtoMember(9)]
        public string HeaderColor { get; set; } = string.Empty;

        [ProtoMember(10)]
        public string ImageLogoUri { get; set; } = string.Empty;

        [ProtoMember(11)]
        public bool ShowPrivacyPolicy { get; set; }

        [ProtoMember(12)]
        public string PrivacyPolicyUrl { get; set; } = string.Empty;

        [ProtoMember(13)]
        public string CurrencyCode { get; set; } = string.Empty;

        [ProtoMember(14)]
        public string CultureCode { get; set; } = string.Empty;

        [ProtoMember(15)]
        public string RedirectUrl { get; set; } = string.Empty;

    }



    [ProtoContract]
    public class PaymentOnlineConfigurationResponse
    {
        [ProtoMember(1)]
        public string ApplicationName { get; set; } = string.Empty;

        [ProtoMember(2)]
        public int ConfigurationId { get; set; }

        /*[ProtoMember(3)]
        public string CultureCode { get; set; } = string.Empty;

        [ProtoMember(4)]
        public string CurrencyCode { get; set; } = string.Empty;

        [ProtoMember(5)]
        public string Description { get; set; } = string.Empty;

        [ProtoMember(6)]
        public string Footer { get; set; } = string.Empty;

        [ProtoMember(7)]
        public string Header { get; set; } = string.Empty;

        [ProtoMember(8)]
        public string HeaderColor { get; set; } = string.Empty;

        [ProtoMember(9)]
        public string ImageLogoUri { get; set; } = string.Empty;

        [ProtoMember(10)]
        public string PaymentApplyConfiguration { get; set; } = string.Empty;

        [ProtoMember(11)]
        public string PaymentOptionsAvailabilityView { get; set; } = string.Empty;

        [ProtoMember(12)]
        public string PaymentVersion { get; set; } = string.Empty;

        [ProtoMember(13)]
        public string PrivacyPolicyUrl { get; set; } = string.Empty;

        [ProtoMember(14)]
        public string RedirectUrl { get; set; } = string.Empty;

        [ProtoMember(15)]
        public string RenderMenuOrder { get; set; } = string.Empty;

        [ProtoMember(16)]
        public RequestOnline Request { get; set; } = new();

        [ProtoMember(17)]
        public bool ShowCountdown { get; set; }

        [ProtoMember(18)]
        public bool ShowLimitPaymentDate { get; set; }

        [ProtoMember(19)]
        public bool ShowPrivacyPolicy { get; set; }*/

        [ProtoMember(19)]
        public bool Success { get; set; }
    }

    [ProtoContract]
    public class RequestOnline
    {
        [ProtoMember(1)]
        public int AffiliateId { get; set; }

        [ProtoMember(2)]
        public int AffiliateSiteId { get; set; }

        [ProtoMember(3)]
        public int AgencyId { get; set; }

        [ProtoMember(4)]
        public int ChannelGroupId { get; set; }

        [ProtoMember(5)]
        public int ChannelId { get; set; }

        [ProtoMember(6)]
        public string PaymentGatewayApp { get; set; } = string.Empty;

    }

}

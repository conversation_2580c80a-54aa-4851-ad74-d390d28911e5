@using PricetravelWebSSR.Helpers
@inject ViewHelper viewHelper


<p class="mb-2">@viewHelper.Localizer("session_verify_help_secure")</p>
<p class="mb-0" ng-if="!vm.isResendCurrenUser">@viewHelper.Localizer("session_verify_instrucc")</p>
<p class="mb-0" ng-if="vm.isResendCurrenUser">@viewHelper.Localizer("session_verify_instrucc2")</p>
<p class="font-500">{{vm.userData.email}}</p>
<p class="font-disabled mb-2">@viewHelper.Localizer("session_verify_spam")</p>
<button type="button" name="button" class="btnPrimary w-100 my-28" ng-click="vm.goToStepOne()">@viewHelper.Localizer("header_redirect_login")</button>
<div class="w-100 text-center">
    <button ng-show="vm.settings.forgot" type="button" name="button" class="btnTertiary"
            ng-click="vm.reseendVerification()">
       @viewHelper.Localizer("resend")
    </button>
</div>
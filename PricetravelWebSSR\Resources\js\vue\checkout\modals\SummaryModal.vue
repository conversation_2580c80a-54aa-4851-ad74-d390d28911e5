<template>
    <!-- MODAL RESUMEN HC -->
    <div class="modal content-open " id="summaryModal" tabindex="-1" aria-label="Summary of payment" aria-modal="true"
        role="dialog" style="">
        <div class="modal-dialog m-0">
            <div class="modal-content bg-modal">
                <div class="modal-body">
                <button type="submit" class="btn btn-blur btn-lg mb-2 btn-block px-2 border-0 d-lg-none text-right modalclose" data-dismiss="modal" aria-label="regresar a opciones de itinerario"><i class="icons-close mr-2" aria-hidden="true" ></i></button>
                    <div class="modal-content-white" role="main">
                         <SummaryPayment @onSubmit="onSubmit" :summary="data" :customclass="'d-none d-md-block'"
                        :summaryMobile="true"></SummaryPayment>
                        <PaymentOptions
                            v-if="siteConfig.code === 'PTMX' && !rapd && isMsiAvailable && isCollectType != 2"
                            :msiIsAvailible="data.isMsiAvailableMonths" />
                    </div>
                </div>
            </div>
        </div>
    </div>

</template>
<script>

export default {
    props: {
        data: {
            type: Object,
            required: true
        },
        customclass: {
            type: String,
            default: ""
        },
        isCollectType: {
            type: Number,
            default: false
        },
        isMsiAvailable: {
            type: Boolean,
            default: false
        },
        rapd: {
            type: Boolean,
            default: false
        },
        onSubmit: {
            type: Function,
            default: false
        },
        siteConfig: {
            type: Object,
            default: false
        }
    }

}

</script>
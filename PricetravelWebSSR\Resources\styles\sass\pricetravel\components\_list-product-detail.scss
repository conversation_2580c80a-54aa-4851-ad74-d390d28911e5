.product-price-item {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font: var(--text-md);
  }
  .product-detail-item small{
    color: hsl(208, 7%, 46%);
  }
  .product-price-details {
    display: flex;
    flex-direction: column;
    gap: .25rem;
  }
  
  .product-detail {
    padding-block-end: 2rem;
  }
  
  
  .product-detail-media {
    display: flex;
    flex-direction: row;
    gap: 1rem;
  }
  
  .product-detail-specs {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
  }
  
  .product-detail-item{
    display: flex;
    width: 50%;
    gap: .5rem;
    padding-block-end: 0.5rem;
    position: relative;
    p{
      color: var(--text-subtle);
    }
    p.product-detail-item-title{
      color: var(--text-main);
    }
  
    small{
      padding: 0;
      display: block;
    }
  }

  
  .product-detail-item:last-child {
    width: 100%;
  }
  
  .product-detail-item-title {
    font: var(--title-xxs);
    padding-block-end: var(--spacing-inside-xxs);
    color: var(--text-main);
    font-size: 0.875rem;
  }
  .product-detail-item-number {
    position:absolute;
    left: 0;
  }
  .product-detail-item p{
    margin-bottom: 0;
  }
  @media(max-width: 767px){
    .product-detail-item{
      width: 100%;
    }
    .product-detail-item:nth-child(-n + 2){
      width: 50%;
    }
  }
  .product-detail-item-sucess {
    color: var(--text-success)!important;
  }
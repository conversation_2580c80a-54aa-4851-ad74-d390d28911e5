﻿import { defineStore } from "pinia";
const culture = window.__pt.culture;
const userLocation = window.__pt.userLocation;

export const useLegalStore = defineStore('legal', {
    state: () => ({
        response: [],
        languageSelected: culture.cultureCode,
        contentSelected: '',
        menuSelected: [],
        faqContent: null,
        juridiction: userLocation.country.toLowerCase() === "us" ? "BTC" : "Price Res, SAPI de CV"
    }),
    getters: {
        getResponse: (state) => state.response,
        getLanguageSelected: (state) => state.languageSelected,
        getContent: (state) => state.response,
        isStaticContent(state) {
            if (state.response.length > 0) {
                return state.response[0].static;
            }
            return true;
        },
        getContentSelected: (state) => state.contentSelected,
        getMenuSelected:(state) => state.menuSelected,
        getFaqContent: (state) => {
            if (state.faqContent) {
                return state.faqContent.content.find((element) => element.code === state.languageSelected).content
            }
            return null;
        },
        getFaq: (state) => state.faqContent,
    },
    actions: {
        setResponse(res) {
            this.response = res;
        },
        setJuridictionSelected(juridiction) {
            this.juridiction = juridiction;
        },
        changeJurisdiction() {
            const indexContent = this.getContent.findIndex((content) => content.name.toLowerCase() === this.juridiction.toLowerCase());
            const indexLanguage = this.getContent[indexContent].content.findIndex((item) => item.code === this.languageSelected.toLowerCase())
            if (indexContent >= 0) {
                const objectSelected = {
                    name: this.getContent[indexContent].name === 'Price Res, SAPI de CV' ? i18n.information.mx : i18n.information.us,
                    code: this.getContent[indexContent].name,
                }
                this.contentSelected = this.getContent[indexContent].content[indexLanguage].contentHtml;
                this.setJuridictionSelected(objectSelected);
            }

        },
        setContentSelectedInitial(country) {
            const entry = this.getContent.find(item => 
                item.country.some(c => c.toLowerCase() === country)
            ) || this.getContent.find(item => item.country.length === 0);
        
            if (!entry) return "";
        
            const content = entry.content.find(c => 
                c.code.toLowerCase() === this.languageSelected.toLowerCase()
            );
            
            Object.assign(this, {
                contentSelected: content?.contentHtml ?? null,
                menuSelected: content?.menu ?? null
            });
        },
        setFaqContent(content) {
            this.faqContent = content;
        },


    }
});
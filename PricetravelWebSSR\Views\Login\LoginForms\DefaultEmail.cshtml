﻿@using PricetravelWebSSR.Helpers
@inject ViewHelper viewHelper


<label for="email_login">@viewHelper.Localizer("enter_email2")</label>
<div >
    <input type="email" 
        id="email_login" 
        placeholder="@viewHelper.Localizer("session_email_placeholder")"  
        ng-model="vm.userData.email" 
        ng-pattern="vm.emailRegex"
        name="email" 
        ng-class="{'invalid' : vm.hasError(form_login, 'email')}"
        required>

    <p class="invalid-feedback-special" ng-show="vm.currentErrorMsg && vm.currentErrorMsg.length">{{vm.currentErrorMsg}}</p>
    <p class="invalid-feedback-special" ng-show="form_login.email.$dirty && form_login.email.$invalid">@viewHelper.Localizer("enter_email2Valid")</p>
    <p class="invalid-feedback-special" ng-show="form_login.$submitted && form_login.email.$error.required && !form_login.email.$dirty">@viewHelper.Localizer("enter_email2ValidEmpty")</p>
</div>
<button type="submit" 
    name="button" 
    class="btnPrimary mt-28 w-100" 
    ng-disabled="!vm.userData.email || form_login.email.$error.pattern">
    @viewHelper.Localizer("login_continue")
</button>
<p class="my-28 text-center font-14">@viewHelper.Localizer("use_other_login_methods") </p>
<div class="d-flex justify-content-center g-16">
    <button ng-repeat="provider in vm.loginProviders" type="button" name="button"
            class="btn-social"
            ng-click="vm.loginWithProvider(provider.code)">
        <i class="icon-{{provider.icon}}"></i>
        {{provider.name}}
    </button>
</div>
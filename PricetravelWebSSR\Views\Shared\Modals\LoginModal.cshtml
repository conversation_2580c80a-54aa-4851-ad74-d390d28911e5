@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
@using PricetravelWebSSR.Models.Meta.Metatags;
@using PricetravelWebSSR.Models.Response;
@using System.Web;
@using PricetravelWebSSR.Types;
@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions
@inject StaticHelper staticHelper
@{
        ViewData["seoContent"] = new SeoResponse();
}
<div class="modal fade" id="modal-login" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true" ng-controller="LoginController as vm" >
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content rounded-12">
               <button type="button" class="close" data-dismiss="modal" aria-label="Close" style="margin: 24.5px 24.5px;position: absolute;z-index: 999;right: 0;top: 0;" ng-click="vm.closeModal('modal-login')">
                      <i class="icons-font icons-close"></i>
                    </button>
            <div class="modal-body p-0">                            
                    @await Html.PartialAsync($"~/Views/Login/Index.cshtml", new ViewDataDictionary(ViewData) { { "IsCheckout", true } })
            </div>
        </div>
    </div>
</div>

﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{
    [ProtoContract]
    public class WeatherInfo
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public bool IsActive { get; set; }

        [ProtoMember(3)]
        public string Summary { get; set; }

        [ProtoMember(4)]
        public string ToBring { get; set; }

        [ProtoMember(5)]
        public string Degrees { get; set; }

        [ProtoMember(6)]
        public string Image { get; set; }

        [ProtoMember(7)]
        public string WeatherSeasonHtml { get; set; }

        [ProtoMember(8)]
        public string TipsHtml { get; set; }

        [ProtoMember(9)]
        public string Tips { get; set; }

        [ProtoMember(10)]
        public string ToBringHtml { get; set; }

        [ProtoMember(11)]
        public string Location { get; set; }

        [ProtoMember(12)]
        public List<WeatherForecastDay> ForecastDays { get; set; }

        public WeatherInfo()
        {
            Summary = string.Empty;
            ToBring = string.Empty;
            Degrees = string.Empty;
            Image = string.Empty;
            WeatherSeasonHtml = string.Empty;
            TipsHtml = string.Empty;
            Tips = string.Empty;
            ToBringHtml = string.Empty;
            Location = string.Empty;
            ForecastDays = new List<WeatherForecastDay>();
        }
    }
}

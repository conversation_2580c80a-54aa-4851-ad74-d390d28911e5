import moment from "moment";
import { util } from "../utils/utils";

class GrowthBookAnalytic {
    constructor() {
        this.data = window.__pt.data || {};
        this.site = window.__pt.settings.site || {};
        this._userKey = this.getUserId();
        this.experiment = window.__pt.experiment || {};
        this.fn = window.__pt.fn;
        this._isBot = window.__pt.settings.rb || false;
    }

    addShippingInfo(args) {
        let quote = args.quote || {};
        let isBookNowPayLater = quote.rate.bookNowPayLater.isBookNowPayLater;

        let listItems = [];

        if (quote.ratesWithRooms) {
            listItems = quote.ratesWithRooms.map((rwr) => {
                return {
                    item_id: rwr.room.roomID,
                    item_name: rwr.room.name,
                    item_brand: quote.name,
                    item_category: "habitacion",
                    item_category2: null,
                    item_variant: null,
                    price: rwr.rate.averageRateWithTaxes,
                    coupon: rwr.rate.discount,
                    item_list_name: null,
                    item_list_id: 0,
                };
            });
        }

        let event = {
            Currency: quote.currency,
            Value: quote.rate.totalAmount,
            FieldDestination: quote.hotelId,
            FieldDate1: quote.checkIn,
            FieldDate2: quote.checkOut,
            FieldRooms: quote.roomsTotal,
            FieldTotalNights: quote.days,
            TravelersAdults: quote.adults,
            TravelersChildren: quote.children,
            PurchaseType: isBookNowPayLater ? "pagar despues" : "pagar ahora",
            ContentType: "boton",
            PageType: "checkout_datos",
            UserId: this._userKey,
            Layer: "hoteles",
            Items: JSON.stringify(
                listItems.length
                    ? listItems
                    : [
                          {
                              item_id: quote.room.roomID,
                              item_name: quote.room.name,
                              item_brand: quote.name,
                              item_category: "habitacion",
                              item_category2: null,
                              item_variant: null,
                              price: quote.rate.averageRateWithTaxes,
                              coupon: quote.rate.discount,
                              item_list_name: null,
                              item_list_id: 0,
                          },
                      ]
            )
        };

        this.Push("add-shipping-info", event);
    }

    purchase(args) {
        let reservation = args.reservation;
        let quote = args.quote;
        let isBookNowPayLater = reservation.reserveNowPayLater;
        let info = args.info;
        let listitems = [];

        if(reservation.roomList.length) {
            listitems  = reservation.roomList.map(rm => {
                return {
                    item_id: info.roomID,
                    item_name: rm.serviceCarrierDescription,
                    item_brand: rm.serviceCarrierName,
                    item_category: "hotel",
                    item_category2: null,
                    coupon: rm.discount,
                    discount: rm.amountDiscount,
                    quantity: rm.roomsCount,
                    item_variant: null,
                    price: rm.serviceAmountBalance
                }
            })
        }

        let event = {
                Currency: reservation.currency,
                Value: reservation.hotelInformation.serviceAmountTotal,
                PaymentType: "CreditCard",
                TransactionId: reservation.bookingId,
                ArrivalId: quote.hotelId,
                MealPlan: reservation.hotelInformation.mealPlan,
                PurchaseType: isBookNowPayLater ? "pagar despues" : "pagar ahora",
                PageType: "confirmacion",
                Layer: "hoteles",
                UserId: this._userKey,
                items: JSON.stringify(listitems.length > 0 ? listitems : [
                    {
                        item_id: info.roomID,
                        item_name: reservation.hotelInformation.serviceCarrierDescription,
                        item_brand: reservation.hotelInformation.serviceCarrierName,
                        item_category: "hotel",
                        item_category2: null,
                        coupon: reservation.hotelInformation.discount,
                        discount: reservation.hotelInformation.amountDiscount,
                        quantity: reservation.hotelInformation.roomsCount,
                        item_variant: null,
                        price: info.roomPrice
                    }
                ])
            
        };

        this.Push("purchases", event);

    }
    viewed(quote,source) {
        let experimentD = {};
        if(this.experiment.isExperiment){
            experimentD = this.experiment.experiment.config.find(x=> x.isExperiment == true)
        }else{
            experimentD = this.experiment.experiment.config.find(x=> x.isExperiment == false)
        }
        this.experimentData = {
            experimentId: this.experiment.variant,
            variationId: experimentD.cookieValue,
            UserId: this._userKey,
            EventDevice: this.fn.mobileAndTabletCheck() ? "mobile" : "desktop",
            EventDestination: quote.places.displayText + " | " + quote.places.id + " | source:"+(source || "") + " | " + "bot:"+ this._isBot,
            ChannelId: quote.quote.channelId.toString(),
            Browser: window.navigator.userAgent.substring(window.navigator.userAgent.length - (window.navigator.userAgent.length > 50 ? 50: window.navigator.userAgent.length), window.navigator.userAgent.length)
        };

        this.Push('viewed-experiment', this.experimentData);
    }

    Push(evt, params) {
        fetch(`${this.site.domainAPIUrl}/api-hotel/api/experiment-tracker/` + evt, {
            method: 'POST',
            headers: {
                "Content-Type": "application/json"
            },
            body: JSON.stringify(params)
        });
    }
    getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }
    getUserId() {
        let sessionId = this.getCookie('session_id');
        return sessionId;
    }
}

export const GrowthBookAnalytics = new GrowthBookAnalytic();

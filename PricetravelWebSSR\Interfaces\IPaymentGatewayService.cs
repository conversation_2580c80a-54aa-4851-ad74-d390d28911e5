﻿using PricetravelWebSSR.Models.PaymentGatewayToken;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.Response;

namespace PricetravelWebSSR.Interfaces
{
    public interface IPaymentGatewayService : 
        IQueryHandlerAsync<PaymentGatewayTokenRequest, PaymentGatewayTokenResponse>, 
        IQueryHandlerAsync<PaymentGatewayConfigurationRequest, List<PaymentGatewayConfigurationResponse>>, 
        IQueryHandlerAsync<PaymentOnlineGatewayConfigurationRequest, PaymentOnlineConfigurationResponse>,
        IQueryHandlerAsync<PaymentGatewayOnlineRequest, PaymentGatewayTokenResponse>
    {
    }
}

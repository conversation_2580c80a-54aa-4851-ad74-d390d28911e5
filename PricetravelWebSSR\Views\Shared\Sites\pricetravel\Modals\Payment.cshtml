﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions

@{
    var page = ViewData["page"];
    var isMobile = (bool) ViewData["Mobile"];
    var cultureConf = ViewData["cultureData"] as Culture;
}

<!-- PAYMENT METHODS MODAL -->
<div class="modal fade" id="modal-payform" tabindex="-1" aria-labelledby="modal-payment" aria-hidden="true" role="dialog">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content" role="document">
            <div class="modal-header px-20">
                <h2 class="mb-0 font-20 font-main">@viewHelper.Localizer("modal_payment_title")</h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="icons-close font-24 font-subtle" aria-hidden="true"></i>
                </button>
            </div>

            <div class="modal-body px-20">
                <section class="d-flex g-12">
                    <i class="icons-credit-card-outline font-32 font-main d-flex"></i>
                    <div>
                        <div class="mb-24 d-flex align-items-center g-8 flex-wrap">
                            <h3 class="mb-0 font-18 font-main">@viewHelper.Localizer("modal_payment_card_title")</h3>
                            <div class="d-flex align-items-center g-8">
                                <div class="icon-bank icon-mastercard"></div>
                                <div class="icon-bank icon-visa"></div>
                                <div class="icon-bank icon-amex"></div>
                            </div>
                        </div>
                        <p class="mb-24"> @viewHelper.Localizer("modal_payment_TC_promotion") </p>
                        <div class="border rounded">
                            <section class="py-16 px-12"
                                ng-repeat="entry in vm.responsePayment.monthInterestFree" 
                                ng-class="{ 'border-bottom': !$last }">
                                <h4 class="mb-8 font-16 font-600"><span class="font-primary">{{ entry[1][0].paymentPlans.length === 2 ? entry[1][0].paymentPlans.join(' @viewHelper.Localizer("modal_payment_and") ') : entry[1][0].paymentPlans.slice(0, -1).join(', ') + ' @viewHelper.Localizer("modal_payment_and") ' + entry[1][0].paymentPlans.slice(-1)[0] }}</span> @viewHelper.Localizer("modal_payment_months") @viewHelper.Localizer("modal_payment_months_interest")*</h4>
                                <div class="d-flex align-items-center g-base flex-wrap">
                                    <div ng-repeat="item in entry[1]"
                                         aria-label="{{item.name}}"
                                         class="icon-bank-brand icon-brand-{{item.icon || (item.name | lowercase) | removeSpaces }}-card">
                                    </div>
                                </div>
                            </section>
                        </div>
                        <p class="mt-24 mb-0 font-12 font-subtle">*@viewHelper.Localizer("modal_payment_warning")</p> 
                    </div>
                </section>

                <hr class="my-20">

                <section class="d-flex g-12">
                    <i class="icons-wallet font-32 font-main d-flex"></i>
                    <div>
                        <h3 class="font-18 font-main">@viewHelper.Localizer("modal_payment_digital_wallet")</h3>
                        <p> @viewHelper.Localizer("modal_payment_payments_online") * </p>
                        <div class="my-3 d-flex align-items-center g-20 flex-wrap">
                            <img src="/assets/img/logos_banks/logo-paypal.svg" alt="paypal" width="100" height="35" />
                            <img src="/assets/img/logos_banks/logo-applepay.svg" alt="apple pay" width="76" height="35" />
                        </div>
                        <p class="mb-0 font-12 font-subtle"> *@viewHelper.Localizer("modal_payment_restriction") </p>
                    </div>
                </section>

                <hr class="my-20">

                <section class="d-flex g-12">
                    <i class="icons-monument font-32 font-main d-flex"></i>
                    <div>
                        <h3 class="font-18 font-main">@viewHelper.Localizer("bank_deposit_transfer")</h3>
                        <p>@viewHelper.Localizer("bank_deposit_transfer_description")</p>
                        <div class="d-flex align-items-center g-20 flex-wrap">
                            <div class="icon-bank-brand icon-brand-scotia-card" aria-label="scotiabank" style="width: 100px; height:35px;"></div>
                            <div class="icon-bank-brand icon-brand-bbva-card" aria-label="bbva" style="width: 100px; height:35px;"></div>
                            <div class="icon-bank-brand icon-brand-banorte-card" aria-label="banorte" style="width: 100px; height:35px; background-size: cover !important;"></div>
                        </div>
                    </div>
                </section>
                
                <hr class="my-20">

                <section class="d-flex g-12">
                    <i class="icons-store font-32 font-main d-flex"></i>
                    <div>
                        <h3 class="font-18 font-main">@viewHelper.Localizer("pay_at_pdv", settingOptions.Value.AppName)</h3>
                        <p>@viewHelper.Localizer("pay_at_pdv_description") <a href="/@(cultureConf.CultureCode)/puntos-de-venta" target="_blank" rel="noopenner noreferrer" class="link text-lowercase">@viewHelper.Localizer("pdv_locations")</a>.</p>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>

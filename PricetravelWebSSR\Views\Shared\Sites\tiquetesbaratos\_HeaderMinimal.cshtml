@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options
@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions

@{
    var cultureConf = ViewData["cultureData"] as Culture;
}
<!--Header-->
<header id="header" ng-cloak>
    <nav class="container">
        <div class="header__top">
            <a class="header__logo" href="@settingOptions.Value.SiteUrl" title="@settingOptions.Value.AppName">
                <img class="" @* width="@(mobile ? "160": "215" )" height="@(mobile ? "24": "37" )" *@ alt="@settingOptions.Value.AppName" src="@(settingOptions.Value.CloudCdn)@viewHelper.Localizer("img",@settingOptions.Value.SiteName.ToLower())" />
            </a>

            <menu class="header__buttons" role="menubar">
                <li role="presentation">
                    <a class="header__btn" href="@($"/{cultureConf.CultureCode}")" role="menuitem">
                        @viewHelper.Localizer("header_redirect")
                    </a>
                </li>
            </menu>
        </div>
    </nav>
</header>
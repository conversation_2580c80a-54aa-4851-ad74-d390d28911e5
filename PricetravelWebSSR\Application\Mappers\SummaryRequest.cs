﻿using PricetravelWebSSR.Models.Flight.Summary;

namespace PricetravelWebSSR.Application.Mappers
{
    public class SummaryMapper
    {

        public static SummaryRequest Request(string id, string email, int channel)
        {
            return new SummaryRequest
            {
                CustomerEmail = email,
                IdReservation = id,
                Channel = channel
            };
        }
    }
}

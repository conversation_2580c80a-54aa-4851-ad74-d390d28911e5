﻿@using PricetravelWebSSR.Helpers
@using PricetravelWebSSR.Types;
@inject ViewHelper viewHelper
<div class="modal fade modal-tb show" id="modal-confirmation" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true" class="text-white">×</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="col-12 text-center">
                    <span class="font-icons icons-check-mark-circle color-successful font-28 my-2"></span>
                </div>
                <div class="col-12 text-center">
                    <p>@viewHelper.Localizer("mail_contact_thank_you_message")</p>
                </div>
                <div class="col-12">
                    <button class="btnTertiary d-block mx-auto py-2 px-5" modal-close=".modalconfirmation" class="close" data-dismiss="modal" aria-label="Close">
                        @viewHelper.Localizer("mail_contact_accept_button")
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

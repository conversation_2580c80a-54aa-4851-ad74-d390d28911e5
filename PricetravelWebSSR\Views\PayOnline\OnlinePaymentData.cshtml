﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options
@using System.Text.Json
@using PricetravelWebSSR.Models.Collection
@using PricetravelWebSSR.Types
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.Request
@using PricetravelWebSSR.Models.PaymentOnline

@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions

@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@inject ViewHelper _
@inject StaticHelper staticHelper
@{
    var resolution = viewHelper.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = viewHelper.IsRobot();
    var mobile = resolution.Device == DeviceType.Mobile;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var cultureConf = ViewData["cultureData"] as Culture;
    var seoContent = ViewData["seoContent"] as SeoResponse;
    ViewData["IsRobot"] = isRobot;
    ViewData["IsMobile"] = isMobile;
    ViewData["Resolution"] = resolution;

}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot } })

<div id="app-main">
    <section class="container c-checkout py-4" style="min-height:530px">
        <pay-online-data></pay-online-data>
    </section>
</div>


<div>
    @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "login", isRobot } })
</div>


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
}

@section ScriptsPriority {
}


@section Scripts {
    <script>
         window.__pt.cultureData = @Json.Serialize((ViewData["CultureData"]));
        window.__pt.exchange = @Json.Serialize((ViewData["Exchange"]));
        window.__pt.alternates = @Json.Serialize((ViewData["Alternates"]));
        window.__pt.currency = @Json.Serialize((ViewData["CurrencyData"]));
        window.__pt.currencies = @Json.Serialize((currencyOptions.Value.Currencies));
    </script>

    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_pdv.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_profile.min.js")"></script>
    <script src="@staticHelper.GetVersion($"/assets/js/vue/lang/{cultureConf.CultureCode}.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/controllers/login-controllers.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/vue/app.js")"></script>
}
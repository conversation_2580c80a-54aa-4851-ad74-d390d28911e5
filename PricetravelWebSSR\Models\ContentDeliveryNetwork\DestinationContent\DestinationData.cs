﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{

    [ProtoContract]
    public class DestinationData
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public bool IsActive { get; set; }

        [ProtoMember(3)]
        public string SummarySanitize { get; set; }

        [ProtoMember(4)]
        public string Summary { get; set; }

        [ProtoMember(5)]
        public string Info { get; set; }

        [ProtoMember(6)]
        public string InfoHtml { get; set; }

        [ProtoMember(7)]
        public string PhotoUri { get; set; }

        [ProtoMember(8)]
        public string CountryA3 { get; set; }

        [ProtoMember(9)]
        public string BaseName { get; set; }

        [ProtoMember(10)]
        public string CountryName { get; set; }

        [ProtoMember(11)]
        public List<GalleryImage> Gallery { get; set; }

        [ProtoMember(12)]
        public DestinationDestination Destination { get; set; }
        [ProtoMember(13)]
        public Country Country { get; set; }
        [ProtoMember(14)]
        public FoodInfo Food { get; set; }

        [ProtoMember(15)]
        public WeatherInfo Weather { get; set; }

        public DestinationData()
        {
            SummarySanitize = string.Empty;
            Summary = string.Empty;
            Info = string.Empty;
            InfoHtml = string.Empty;
            PhotoUri = string.Empty;
            CountryA3 = string.Empty;
            BaseName = string.Empty;
            CountryName = string.Empty;
            Gallery = new List<GalleryImage>();
            Food = new FoodInfo();
            Weather = new WeatherInfo();
        }
    }

}

@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.Configuration
@inject ViewHelper viewHelper;

@inject IOptions<SettingsOptions> settingOptions

@{
    var userLocation = ViewData["UserLocation"] as UserLocation;
}
<section ng-cloak class="ContactFooterBanner">
    <div class="container pb-5">
        <div class="ContactFooterBanner__section">
            <h2 class="d-xl-none">@viewHelper.Localizer("contact_us_title")</h2>
            <h2 class="d-none d-xl-block">@viewHelper.Localizer("need_help_contact")</h2>
            <div class="ContactFooterBanner__options">
                <a class="ContactFooterBanner__item" target="_blank" rel="noopener noreferrer" href="https://wa.me/573104915803" title="Whatsapp">
                    <i class="icons-whatsapp d-flex"></i>
                    <span>@viewHelper.Localizer("by") WhatsApp</span>
                </a>
                <a class="ContactFooterBanner__item" target="_blank" rel="noopener noreferrer" href="https://m.me/128565927176678/" title="Messenger">
                    <i class="icons-messenger d-flex"></i>
                    <span>@viewHelper.Localizer("by") Messenger</span>
                </a>
         <a class="ContactFooterBanner__item" href="tel:@(settingOptions.Value.StaticPhoneNumbers.PrimaryPhone)">
                    <i class="icons-phone d-flex"></i>
                    <span>@viewHelper.Localizer("to_reservate")<strong> @(settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat)</strong></span>
                </a>
            </div>
        </div>
        <div class="ContactFooterBanner__section ContactFooterBanner__section--apps">
            <h2 class="d-xl-none">@viewHelper.Localizer("save_up_app")</h2>
            <h2 class="d-none d-xl-block">@viewHelper.Localizer("download_app",@settingOptions.Value.AppName)</h2>
            <div class="ContactFooterBanner__options">
                <a href="https://www.tiquetesbaratos.com/link/REIwNjJEMEU3MjdD" target="_blank" rel="noopenner noreferrer" >
                    <img bn-lazy-src="/assets/img/emails/app-store.png" alt="app store" fetchpriority="low" width="121" height="40">
                </a>
                <a href="https://www.tiquetesbaratos.com/link/REIwNjJEMEU3MjdD" target="_blank" rel="noopenner noreferrer" >
                    <img bn-lazy-src="/assets/img/emails/google-play.png" alt="google play" fetchpriority="low" width="121" height="40">
                </a>
            </div>
        </div>
    </div>
</section>
﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
using PricetravelWebSSR.Models.PaymentOnline;
using PricetravelWebSSR.Models.Places;
using PricetravelWebSSR.Options;
using PricetravelWebSSR.Types;

namespace PricetravelWebSSR.Controllers
{
    public class PayOnlineController : Controller
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly ICommonHandler _commonHandler;
        private readonly IContentDeliveryNetworkHandler _cdnHandler;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ILoginServices _loginServices;
        private readonly ViewHelper _util;
        private readonly IOptions<SettingsOptions> _settings;
        private readonly IHttpContextAccessor _httpContext;
        private readonly SettingsOptions _options;
        private readonly IPaymentOnlineHandler _onlinePaymentHandler;
        private readonly ILogger<PayOnlineController> _logger;

        public PayOnlineController(
            ICommonHandler commonHandler,
            IContentDeliveryNetworkHandler cdnHandler,
            IAlternateHandler alternateHandler,
            ILoginServices loginServices,
            ViewHelper util,
            IOptions<SettingsOptions> settings,
            IPaymentOnlineHandler onlinePaymentHandler,
            IOptions<SettingsOptions> options,
            IHttpContextAccessor httpContext)
        {
            _onlinePaymentHandler = onlinePaymentHandler;
            _options = options.Value;
            _commonHandler = commonHandler;
            _cdnHandler = cdnHandler;
            _alternateHandler = alternateHandler;
            _loginServices = loginServices;
            _util = util;
            _settings = settings;
            _httpContext = httpContext;
        }

        [HttpGet("{culture}/online-payment")]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public async Task<IActionResult> Index(string culture)
        {
            try
            {

                var refererUrl = Request.Headers.Referer.ToString();
                using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
                var route = Request.Path.Value ?? "";
                var path = HomeMapper.GetPath(route, _options);
                var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest(), cts.Token);
                var seoContent = await _cdnHandler.QueryAsync(new SeoRequest { Path = route }, cts.Token);
                var alternates = await _alternateHandler.QueryAsync(
                                       new Models.Places.PlaceRequest
                                       {
                                           Culture = culture,
                                           Id = 0,
                                           Path = "online-payment",
                                           Route = "online-payment",
                                           Type = Types.PageType.Generic
                                       },
                                       cts.Token);

                var meta = MetaMapper.FavoritesMapper(route, _settings.Value, _util, seoContent);
                var ua = _httpContext.HttpContext.Request.Headers["User-Agent"].ToString().ToLower();

                ViewData["RefererUrl"] = refererUrl;
                ViewData["Alternates"] = alternates;
                ViewData["MetaTag"] = meta;
                ViewData["PageRoot"] = "online-payment";
                ViewData["PageOrig"] = route.ToLower().Trim().Replace("/", "");
                ViewData["IsRoutMain"] = route != "/" ? route : "/hoteles";
                ViewData["User"] = await _loginServices.GetUser();
                ViewData["cultureData"] = userSelection.Culture;
                ViewData["siteCode"] = userSelection.Culture.SiteCode;
                ViewData["currencyData"] = userSelection.Currency;
                ViewData["exchange"] = userSelection.ExchangeClient;
                ViewData["userLocation"] = userSelection.Context.Location;
                ViewData["seoContent"] = seoContent;


                return View();
            }
            catch (Exception e)
            {
                return await ErrorPage($"{e.Message} | {e.StackTrace ?? string.Empty}", 500);
            }
        }


        public async Task<ActionResult> ErrorPage(string errorMgs, int statusCode)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;
            ViewData["ErrorMgs"] = errorMgs;

            _httpContextAccessor.HttpContext.Response.StatusCode = statusCode;
            return View("~/Views/Error/Index.cshtml");
        }
    }
}

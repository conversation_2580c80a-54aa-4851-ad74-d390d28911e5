﻿<template>
    <div class="container py-3 pr-0 pl-0">
        <breadcrumb />
        <div id="security" class="container py-2">
            <h1 class="mb-4">
                <i class="icons-secure font-28 align-middle"></i>
                {{ __('breadcrumbs.security') }}
            </h1>

            <div class="DataSections">
                <section class="DataSections__field">
                    <h2 class="field__title">{{ __('messages.email') }}</h2>
                    <p class="field__data">
                        {{getUserProfile.contactEmail || "N/A"}}
                        <span class="mt-1 d-block font-12">{{ __('security.email_subtitle') }}</span>
                    </p>
                    <span class="verified-tag">{{ __('security.verified') }}</span>
                </section>
                <section class="DataSections__field">
                    <h2 class="field__title">{{ __('security.password') }}</h2>
                    <p class="field__data">************</p>
                    <button v-if="getUserProfile.contactEmail" class="field__editBtn btnTertiary btnTertiary--md" @click="changePassword()">{{ __('security.change') }}</button>
                </section>
            </div>

            <div id="modal-reset-pasword" class="modal fade" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered" style="max-width: 460px;">
                    <div class="modal-content px-3 py-4 p-md-32 rounded-8">
                        <h2 class="mb-24 font-20">
                            {{ isSendEmailPassword ? __('security.check_inbox') : __('security.modify_password') }}
                        </h2>

                        <p class="font-14">
                            {{ isSendEmailPassword ? __('security.password_instructions_sent') : __('security.password_instructions') }}
                            <strong>{{getUserProfile.contactEmail}}</strong>
                        </p>

                        <p class="font-14" v-if="isSendEmailPassword"> {{ __('security.email_valid_time') }}</p>

                        <button type="button" :disabled="!active" class="mt-24 mx-auto d-block btnPrimary btnPrimary--md w-50" @click="recoveryPassword()">{{ isSendEmailPassword ? __('security.resend') : __('security.send') }}</button>
                        <button class="mt-24 mx-auto d-block btnTertiary btnTertiary--md " @click="closeModal()">{{ __('profile.cancel') }}</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { initializeApp } from 'firebase/app';
    import { getAuth, sendPasswordResetEmail } from 'firebase/auth';
    import { storeToRefs } from 'pinia';
    import { useUserStore } from '../stores/user';
    import GlobalUtil from '../../utils/pricetravel/shared/globalUtil';
    const _paramQuery = window.__pt.fn.search();
    const userLocation = window.__pt.userLocation;
    const siteconfig = window.__pt.settings.site;
    const cultureData = window.__pt.cultureData || {};
    const _globalUtil = new GlobalUtil(_paramQuery, siteconfig);
    const configs = siteconfig.login.firebaseSettings;
    const firebaseApp = initializeApp(configs);
    export default {
        data() {
            return {
                site: window.__pt.settings.site,
                isSendEmailPassword: false,
                culture: __pt.cultureData.cultureCode,
                active: true,
                cooldown: 120000
            }
        },
        props: {
            user: null,
        },
        beforeMount() {
            this.setUserProfile(this.user);
        },
        setup() {
            const useUser = useUserStore();
            const { setUserProfile, setBithdateParts } = useUser;
            const { getUserProfile, getReservations, getPaxes, getBirthdate } = storeToRefs(useUser);

            return { setUserProfile, setBithdateParts, getUserProfile, getReservations, getPaxes, getBirthdate, }
        },
        computed: {
        },
        methods: {
            changePassword() {
                $("#modal-reset-pasword").modal("show");
            },
            closeModal() {
                $("#modal-reset-pasword").modal("hide");
            },
            async recoveryPassword() {
                if (!this.active) return;
                const auth = getAuth(firebaseApp);
                auth.languageCode = siteconfig.language;
                const actionCodeSettings = {
                    url: siteconfig.siteUrl + "/" + cultureData.cultureCode + '/login',
                    handleCodeInApp: true
                };
                try {
                    await sendPasswordResetEmail(auth, this.user.contactEmail, actionCodeSettings);
                    this.isSendEmailPassword = true;
                    this.active = false;


                    setTimeout(() => {
                        this.active = true;
                    }, this.cooldown);
                } catch (err) {
                    console.log(err);
                    console.log("algo salió mal");
                }
            }
        }
    }
</script>

.btn {
	font-size: 16px;
	height: 50px;
    border-radius: 0.5rem;
}

.btn-light{
    color: var(--color-nav);
	background: none;

}
.btn-light:hover{
     
		color:var(--color-nav-hover);
		background: none;
}
.btn-primary {
	background-color:  var(--color-nav);
	border-color:  var(--color-nav);
}
.btn-primary:hover {
	background-color:  var(--color-nav-hover);
	border-color:  var(--color-nav-hover);
}

.btn-primary-two {
	background-color:  var(--color-nav);
	border-color:   var(--color-nav);
	color: var(--white);
}
.btn-primary-two:hover {
	background-color:  var(--color-nav-hover);
	border-color: var(--color-nav-hover);
	color:var(--white) ;
}


// .btn-secondary {
// 	background-color: var( --bg-primary)
// 	border-color: var( --bg-primary)
// }
// .btn-secondary:hover {
// 	background-color: $color-primary-hover;
// 	border-color: $color-primary-hover;
// }

.btn-secondary-light {
	color:  var(--color-nav);
}
.btn-secondary-light:hover {
	color:  var(--color-nav);
    background-color: none;
    // border-color: #dae0e5;
}
.btn-secondary-clear {
	color: var(--bg-primary);
    background-color: #F0EDF7;
}
.btn-secondary-clear:hover{
    color: #F0EDF7;
    background-color: var(--bg-primary);
    border-color: var( --bg-primary);
}
.btn-secondary-clear.active{
    color: #F0EDF7;
    background-color: var( --bg-primary);
    border-color: var(--bg-primary);
}


button.gm-ui-hover-effect {
	top: 0 !important;
	right: 20px !important;
	width: 24px !important;
	height: 24px !important;
	z-index: 3;
}

button.gm-ui-hover-effect span {
	width: 24px !important;
	height: 24px !important;
}
.btn-primary.disabled, .btn-primary:disabled {
    color: $white;
    background-color: #cccccc;
    border-color:   #cccccc;
}
.btn-link{
	color: var(--color-nav);
	&:hover{
        gap: 1rem;
		color: var(--color-nav-hover);
		text-decoration: underline;
		span{
			text-decoration: none!important;
		}
	}
	&:focus{
		box-shadow: none;
	}
}

.btn-link-primary{
	color: var(--text-color-primary);
	&:hover{
		color: var(--text-color-primary-600);
		text-decoration: underline;
	}
	&:focus{
		box-shadow: none;
	}
}


/*MOBILE*/

.mobile-button {
	padding: 10px 20px;
	position: fixed;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	// border: 1px solid $color-secondary;
	box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
	z-index: 500;
}

// Nuevos estilos
.btn-primary:focus{
    // outline: 2px solid $blue-300; 
    box-shadow: none;
    outline-offset: 1px;
}
.btn-primary:not(:disabled):not(.disabled):active:focus{
    // outline: 2px solid $blue-300; 
    box-shadow: none;
    outline-offset: 3px;
    // background-color: $color-secondary-hover;
}

.btn-primary:focus, .btn-primary.focus {
    // background-color:$color-secondary-hover;
    // border-color: $color-secondary-hover;
    box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}

.btn-primary.disabled, .btn-primary:disabled {
    color:  var(--text-color-300);
    background-color:  var(--text-color-100);
    border-color:  var(--text-color-100);
}
.btn-primary:not(:disabled):not(.disabled).active,.btn-primary:not(:disabled):not(.disabled):active,.show>.btn-primary.dropdown-toggle{color:#fff;background-color:var(--bg-primary);border-color:var(--text-color-primary)}

.btn-primary:not(:disabled):not(.disabled).active:focus,.btn-primary:not(:disabled):not(.disabled):active:focus,.show>.btn-primary.dropdown-toggle:focus{-webkit-box-shadow:red;box-shadow:red}

.btn-card{
	height: 80px;
	text-align: 80px;
	border: 1px solid var(--neutral-gray-300);
}
.btn-secondary{
	background-color: var(--color-nav-subtle);
    color: var(--color-nav);
    border: 1px solid var(--color-nav)
}
.btn-secondary:hover {
    color: #fff;
    background-color:var(--bg-base);
    border-color:var(--color-nav);
}
.btn-card:hover{
	border: 1px solid $gray-500;
}
/*MOBILE*/

.mobile-button {
	padding: 10px 20px;
	position: fixed;
	bottom: 20px;
	left: 50%;
	transform: translateX(-50%);
	// border: 1px solid $color-secondary;
	box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
	z-index: 500;
}
.btn-card-content{
	min-height: 32vh;	
	button{
		font-weight: 500;
		font-size:1rem ;
	}
}
.btn-block-table{
	background: var(--bg-brand-secondary-subtle);
	color:var(--text-color-primary);
	border:1px solid var(--bg-brand-secondary-subtle);
}
@media(min-width: 768px) and (max-width: 990px){
	.btn-card-content{
		min-height: 65vh;	
	}
 }
 @media(max-width: 767px){
	 .btn-card-content{
		 min-height: 50vh;	
	 }
	 .btn-sm-block{
		width: 100%;
	 }
  }
.btn-link{
	span{
		vertical-align: middle;
	}
}


//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES
//   NUEVOS BOTONES

.btn-primary{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    min-height: 44px;
    min-width: 80px;
    line-height: 100%;
    border: none;

    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    padding: 8px 16px;
    background-color: var(--color-nav);

    &:hover{
        background-color: var(--color-nav-hover);
    }

    &:focus{
        outline: 2px solid var(--outline);
        outline-offset: 2px;
    }
    &:disabled{
        background-color: #C1BFC7;
        color: #F5F5F7;
        filter: grayscale(1);
    }
}
.btn-primary:disabled:hover{
    background-color: #C1BFC7;    
}

.btn-secondary  {
    cursor: pointer;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    min-height: 44px;
    min-width: 80px;
    padding: 1rem;
    line-height: 100%;
    background: transparent;
    border-radius: 8px;
    border: 2px solid var(--color-nav);
    color: var(--color-nav);

    &:hover{
        color: var(---color-nav-hover);
        border-color: var(--color-nav-hover);
    }

    &:disabled{
        background-color: #C1BFC7;
        border-color: #F5F5F7;
        color: #96949C;
    }

    &:focus{
        outline: 2px solid var(--outline);
        outline-offset: 2px;
    }
}


.btn-tertiary{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    min-height: 44px;
    min-width: 80px;
    line-height: 100%;

    
    color: var(--color-nav-hover);
    background: transparent;
    border:none;
    border-radius: 8px;
    
    &:hover{
        color:#800D46;

    }
    &:focus{
        outline: 2px solid var(--outline);
        outline-offset: 2px;
    }
    &:disabled{
        color: #96949C;
    }
}


.btn-icon{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    min-height: 44px;
    min-width: 44px;
    line-height: 100%;
    border: none;

    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    padding: 8px 16px;
    background-color: var(--color-nav);
   
    &:hover{
        background-color: var(--color-nav-hover);
    }
    &:focus{
        outline: 2px solid var(--outline);
        outline-offset: 2px;
    }
    &:disabled{
        background-color: #C1BFC7;
        color: #F5F5F7;
    }
}


.btn-onBg {
    cursor: pointer;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    min-height: 44px;
    min-width: 80px;
    padding: 1rem;
    line-height: 100%;
    background: #0D0D0D75;
    border-radius: 8px;
    border: 2px solid #E4E4E6;
    color: #E4E4E6;

    &:hover{
        color: var(--color-nav-hover);
        border-color: var(---color-nav-hover);
    }

    &:disabled{
        background-color: #C1BFC7;
        border-color: #F5F5F7;
        color: #96949C;
    }

    &:focus{
        outline: 2px solid var(--outline);
        outline-offset: 2px;
    }
}
.btn-link.focus, .btn-link:focus, .btn-link:hover{
    text-decoration: none;
}
.btn-secondary:not(:disabled):not(.disabled):active, .btn-secondary:not(:disabled):not(.disabled).active, .show > .btn-secondary.dropdown-toggle{
    background-color: var(--bg-base);
    border-color: var(--bg-primary-hover);
    color: var(--bg-primary-hover);
}
.btn-secondary:focus, .btn-secondary.focus {
    color: var(--bg-primary-hover);
    background-color:var(--bg-base);
    border-color:  var(--bg-primary-hover);
    box-shadow: 0 0 0 0.2rem rgba(38, 143, 255, 0.5);
}
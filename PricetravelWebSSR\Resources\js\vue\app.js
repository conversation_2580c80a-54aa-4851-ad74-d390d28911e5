/**
 * First we will load all of this project's JavaScript dependencies which
 * includes Vue and other libraries. It is a great starting point when
 * building robust, powerful web applications using Vue.
 */

require('../bootstrap');

import _ from 'lodash';
import VeeValidate from 'vee-validate';
import pinia from './stores/store'

var _exchange = window.__pt.exchange || {};
var cultureInfo = __pt.cultureData;
var _settings = __pt.settings.site;


window.Vue = require('vue').default;

Vue.prototype.__ = (str, values = []) => {

    let formatted = _.get(window.i18n, str, str);

    if (values.length) {
        formatted = _.replace(formatted, /\{\d\}/g, (match) => {
            const index = match.replace(/\{|\}/g, '');
            return values[index] || match;
        });
    }

    return formatted;
}



window.moment.locale(__pt.settings.site.culture);


Vue.use(VeeValidate, {
    locale: __pt.settings.site.language,
    dictionary: {
        [__pt.settings.site.language]: {
            messages: window.i18n.errors, attributes: {}
        }
    }
});
//window.moment.locale(__pt.settings.site.culture);

/* Vue.filter('currency', function (value, currency = __pt.settings.site.currency) {
    if (typeof value !== "number") {
        return value;
    }
    var formatter = new Intl.NumberFormat(__pt.settings.site.culture, {
        style: 'currency',
        currency: currency,
        currencyDisplay: 'symbol',
        maximumFractionDigits: 0
    });
    return (formatter.format(value)).replace("$", __pt.settings.site.currencySymbol);
});
 */
Vue.filter('capitalising', function (word) {
    return word.charAt(0).toUpperCase() + word.slice(1);
});

Vue.filter('uppercase', function (v) {
    return v.toUpperCase();
});

Vue.filter('date', function (value, format = 'YYYY-MM-DD HH:mm') {
    if (value) {
        const raw = window.moment.parseZone(value).format(format).replace('.', '');

        // Capitalizar solo la primera letra de cada palabra usando split y join
        const formatted = raw.split(' ').map(word => {
            if (word.length > 0) {
                return word.charAt(0).toUpperCase() + word.slice(1).toLowerCase();
            }
            return word;
        }).join(' ');

        return formatted;
    }
});

Vue.filter('currency', function (value, maximumFractionDigits = _settings.decimalDigits, applyConvertion = true) {

    const formatter = new Intl.NumberFormat(__pt.settings.site.cultureSite, {
        style: 'decimal',
        minimumFractionDigits: maximumFractionDigits,
        maximumFractionDigits: maximumFractionDigits,
    });

    return formatter.format(value * (applyConvertion ? _exchange.rate : 1)); 
});

Vue.filter('currencyBase', function (value, currency = _exchange.currency, maximumFractionDigits = _settings.decimalDigits, culture = __pt.settings.site.cultureSite) {

    if (typeof value !== "number") {
        return value;
    }

    let formatter = new Intl.NumberFormat(culture, {
        style: 'currency',
        currency,
        currencyDisplay: 'code',
        maximumFractionDigits: maximumFractionDigits
    });
    return (formatter.format(value)).replace(`${ currency }`, `${currency}`);

});
Vue.filter('currencyBaseFee', function (value, currency = _exchange.currency, maximumFractionDigits = _settings.decimalDigits, culture = __pt.settings.site.cultureSite) {

    if (typeof value !== "number") {
        return value;
    }

    let formatter = new Intl.NumberFormat(culture, {
        style: 'currency',
        currency,
        currencyDisplay: 'code',
        maximumFractionDigits: maximumFractionDigits
    });
    return (formatter.format(value)).replace(new RegExp(`\\s*${currency}\\s*`, 'g'), '') + ` ${currency}`;
});

Vue.filter('removedots', function (word) {
    return word.replaceAll('.','');
});

/**
 * The following block of code may be used to automatically register your
 * Vue components. It will recursively scan this directory for the Vue
 * components and automatically register them with their "basename".
 *
 * Eg. ./components/ExampleComponent.vue -> <example-component></example-component>
 */

const files = require.context('./', true, /\.vue$/i);
files.keys().map(key => Vue.component(key.split('/').pop().split('.')[0], files(key).default))

//Vue.component('example-component', require('./components/ExampleComponent.vue').default);

/**
 * Next, we will create a fresh Vue application instance and attach it to
 * the page. Then, you may begin adding components to this application
 * or customize the JavaScript scaffolding to fit your unique needs.
 */


const app = new Vue({
    el: '#app-main',
});

Vue.use(pinia);

﻿<template>
    <div class="container py-3 pr-0 pl-0">
        <breadcrumb />
        <div id="companions" class="container py-2">
            <div class="mb-4 d-sm-flex align-items-center justify-content-between g-16">
                <h1 class="mb-0">
                    <i class="icons-guests font-28 align-middle"></i>
                    {{ __('breadcrumbs.companions') }}
                </h1>
                <button type="button" class="d-none d-sm-flex btnTertiary" @click="addNewPassenger()" v-if="(Object.keys(getPassenger).length > 0 && !allPaxesDisabled)">
                    <i class="icons-add font-20"></i>
                    {{__("profile.add_paxes")}}
                </button>
            </div>

            <!-- Empty State -->
            <div class="mx-auto text-center" v-if="!addedNewPassenger && (Object.keys(getPassenger).length === 0 || allPaxesDisabled)" style="max-width: 380px;">
                <img :src="`/assets/img/login/${site.siteName}/travelers.svg`" alt="companions" width="280">
                <p class="">{{__("profile.paxes_subtitle")}}</p>
                <button type="button" class="btnPrimary" @click="addNewPassenger()">{{__("profile.add_paxes")}}</button>
            </div>

            <!-- List of companions -->
            <section v-if="Object.keys(getPassenger).length > 0">
                <div class="mb-20 px-3 DataSections d-sm-none">
                    <p class="mb-12">{{__("profile.paxes_subtitle")}}</p>
                    <div class="text-right">
                        <button type="button" class="btnTertiary" @click="addNewPassenger()">
                            <i class="icons-add font-20"></i>
                            {{__("profile.add_paxes")}}
                        </button>
                    </div>
                </div>

                <section class="mb-20 DataSections p-4" v-for="(item, index) in filteredPassengers">
                    <div class="d-flex align-items-center justify-content-between g-16" v-show="editingIndex !== index">
                        <h2 class="mb-0 font-18" :class="{'font-disabled': addedNewPassenger}">{{ item.firstName }} {{ item.lastName }}</h2>

                        <div class="d-flex align-items-center g-32">
                            <button type="button" class="btnTertiary btnTertiary--md" @click="editPaxe(index)" :disabled="addedNewPassenger">{{ __('profile.edit') }}</button>
                            <button type="button" class="btnTertiary btnTertiary--md" @click="deletePaxe(item.originalIndex)" :aria-label="__('profile.delete_paxe')" :disabled="addedNewPassenger">
                                <i class="icons-trash-can font-28"></i>
                            </button>
                        </div>
                    </div>
                    <companionForm :data="item" :isNew="false" :index="index" v-show="editingIndex === index" @reset-form="resetForm"></companionForm>
                </section>
            </section>

            <!-- Add new person Form -->
            <section class="DataSections p-3 py-sm-4 px-sm-5" v-if="addedNewPassenger">
                <companionForm :data="newUser" :isNew="true" @reset-form="resetForm" :index="0"></companionForm>
            </section>


            <div id="modal-delete-paxe" class="modal fade" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered" style="max-width: 460px;">
                    <div class="modal-content px-3 py-4 p-md-32 rounded-8 text-center">
                        <p class="mb-24">{{ __('profile.warningDelete') }}</p>
                        <div class="d-flex justify-content-center g-32">
                            <button class="btnSecondary" @click="closeModal()">{{ __('profile.cancel') }}</button>
                            <button type="button" class="btnPrimary" @click="disablePaxe()">
                                {{ __('profile.accept') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { useUserStore } from '../stores/user';
    import GlobalUtil from '../../utils/pricetravel/shared/globalUtil';
    import { accountAnalytics } from '../analytics/AccountAnalytics';

    const _paramQuery = window.__pt.fn.search();
    const userLocation = window.__pt.userLocation;
    const siteconfig = window.__pt.settings.site;
    const cultureData = window.__pt.cultureData || {};
    const userData = window.__pt.user || {};
    const _globalUtil = new GlobalUtil(_paramQuery, siteconfig);
    export default {
        data() {
            return {
                nations: [],
                site: window.__pt.settings.site,
                culture: __pt.cultureData.cultureCode,
                day_options: [
                    { "text": "--", "value": "0" },
                    { "text": "1", "value": "01" },
                    { "text": "2", "value": "02" },
                    { "text": "3", "value": "03" },
                    { "text": "4", "value": "04" },
                    { "text": "5", "value": "05" },
                    { "text": "6", "value": "06" },
                    { "text": "7", "value": "07" },
                    { "text": "8", "value": "08" },
                    { "text": "9", "value": "09" },
                    { "text": "10", "value": "10" },
                    { "text": "11", "value": "11" },
                    { "text": "12", "value": "12" },
                    { "text": "13", "value": "13" },
                    { "text": "14", "value": "14" },
                    { "text": "15", "value": "15" },
                    { "text": "16", "value": "16" },
                    { "text": "17", "value": "17" },
                    { "text": "18", "value": "18" },
                    { "text": "19", "value": "19" },
                    { "text": "20", "value": "20" },
                    { "text": "21", "value": "21" },
                    { "text": "22", "value": "22" },
                    { "text": "23", "value": "23" },
                    { "text": "24", "value": "24" },
                    { "text": "25", "value": "25" },
                    { "text": "26", "value": "26" },
                    { "text": "27", "value": "27" },
                    { "text": "28", "value": "28" },
                    { "text": "29", "value": "29" },
                    { "text": "30", "value": "30" },
                    { "text": "31", "value": "31" },

                ],
                month_options: [
                    { "text": "--", "value": "0" },
                    { "text": this.__(`months.01`), "value": "01" },
                    { "text": this.__(`months.02`), "value": "02" },
                    { "text": this.__(`months.03`), "value": "03" },
                    { "text": this.__(`months.04`), "value": "04" },
                    { "text": this.__(`months.05`), "value": "05" },
                    { "text": this.__(`months.06`), "value": "06" },
                    { "text": this.__(`months.07`), "value": "07" },
                    { "text": this.__(`months.08`), "value": "08" },
                    { "text": this.__(`months.09`), "value": "09" },
                    { "text": this.__(`months.10`), "value": "10" },
                    { "text": this.__(`months.11`), "value": "11" },
                    { "text": this.__(`months.12`), "value": "12" },
                ],
                genrer_options: [
                    { "text": this.__(`profile.man`), "value": "M" },
                    { "text": this.__(`profile.fem`), "value": "F" },
                ],
                addedNewPassenger: false,
                editedPaxe: false,
                editingIndex: null,
                deteleIndex: null,
                nationalityTemp: this.getNewPaxe?.Nationality?.toLowerCase() || ''
            }
        },
        props: {
            user: null
        },

        computed: {
            filteredPassengers() {
                return this.getPassenger
                    .map((p, originalIndex) => ({ ...p, originalIndex }))
                    .filter(p => !p.disabled);
            },
            newUser() {
                return this.getNewPaxe;
            },
            allPaxesDisabled() {
                return this.getPassenger.every(p => p.disabled === true);
            }
        },

        beforeMount() {
            const processedUser = {
                ...this.user,
                passengers: this.user.passengers.map(p => {
                    let day_user = '', month_user = '', year_user = '';

                    if (p.birthdate) {
                        const [day, month, year] = p.birthdate.split('/');
                        day_user = day;
                        month_user = month;
                        year_user = year;
                    }

                    return {
                        ...p,
                        day_user,
                        month_user,
                        year_user
                    };
                })
            };
            this.setUserProfile(processedUser);
        },

        setup() {
            const useUser = useUserStore();
            const { setNewPassenger, setNationalityNewPaxe, setBirthdayNewPaxe, addNewPaxe, disablePassenger, setUserProfile } = useUser;
            const { getNewPaxe, getPassenger, getUserProfile, resetNewPaxe } = storeToRefs(useUser);

            return { setNewPassenger, getNewPaxe, setNationalityNewPaxe, setBirthdayNewPaxe, getPassenger, resetNewPaxe, getUserProfile, resetNewPaxe, addNewPaxe, disablePassenger, setUserProfile }
        },
        methods: {
            addNewPassenger() {
                this.resetNewPaxe();
                accountAnalytics.addNewCompanion(userData.type);
                this.addedNewPassenger = true;
                this.editingIndex = null;
                this.editedPaxe = false;
            },
            updatePassenger() {
                this.editedPaxe = true;
            },
            editPaxe(index) {
                this.editingIndex = index;
                this.editedPaxe = true;
            },
            resetForm() {
                this.editingIndex = null;
                this.editedPaxe = false;
                this.resetNewPaxe();
                this.addedNewPassenger = false;
            },
            deletePaxe(index) {
                $("#modal-delete-paxe").modal("show");
                this.deteleIndex = index;
            },
            closeModal() {
                $("#modal-delete-paxe").modal("hide");
                this.deteleIndex = null;
            },
            async disablePaxe() {
                this.disablePassenger(this.deteleIndex);
                let user = { ...this.getUserProfile };
                let options = {
                    headers: {
                        "X-CSRF-TOKEN": _globalUtil.getCSFRToken()
                    }
                }
                let response = await axios.post(siteconfig.siteUrl + "/hotel/api/update-account", user, options).catch((data) => this.onError(data, 'error'));

                if (response && response.status == 200) {
                    $("#modal-delete-paxe").modal("hide");
                    console.log(this.getPassenger);
                } else {
                    this.onError();
                }
            }
        }
    }
</script>

﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Provider.Request;
using PricetravelWebSSR.Models.Provider.Response;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Implementations
{
    public class ProviderHandler : IProviderHandler
    {
        private readonly SettingsOptions _options;

        public ProviderHandler(IOptions<SettingsOptions> options)
        {
            _options = options.Value;
        }

        public async Task<ProviderResponse> QueryAsync(ProviderRequest request, CancellationToken ct)
        {
            var providerIds = _options.HotelProviders
                .Where(h => h.HotelsId.Contains(request.HotelId))
                .SelectMany(h => h.Providers)
                .Distinct()
                .ToList();

            return new ProviderResponse
            {
                ProvidersId = providerIds
            };
        }

    }
}
﻿namespace PricetravelWebSSR.Models.Login
{
    public class Adult
    {
        public string name { get; set; }
        public string last_name { get; set; }
        public DateTime birthday { get; set; }
        public string nationality { get; set; }
        public string genre { get; set; }
        public string identity { get; set; }
    }

    public class Child
    {
        public string name { get; set; }
        public string last_name { get; set; }
        public DateTime birthday { get; set; }
        public string nationality { get; set; }
        public string genre { get; set; }
        public string identity { get; set; }
    }

    public class Paxes
    {
        public List<Adult> adults { get; set; }
        public List<Child> children { get; set; }
    }

    public class Reservations
    {
        public string destination { get; set; }
        public string startDate { get; set; }
        public string endDate { get; set; }
        public string bookingReference { get; set; }
        public string bookingStatus { get; set; }
        public List<string> serviceIcons { get; set; }
        public string imageUrl { get; set; }
    }

    public class UserDataV2
    {
        public UserProfileV2 userProfile { get; set; }
        public List<Reservations> reservations { get; set; }
        public Paxes paxes { get; set; }
    }

    public class UserProfileV2
    {
        public string name { get; set; }
        public string last_name { get; set; }
        public string telephone { get; set; }
        public DateTime birthday { get; set; }
        public string nationality { get; set; }
        public string genre { get; set; }
        public string identity { get; set; }
        public string telCountry { get; set; }
        public string email { get; set; }
    }

}

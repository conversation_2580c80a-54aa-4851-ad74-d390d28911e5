﻿@using PricetravelWebSSR.Helpers
@inject ViewHelper viewHelper
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@inject IOptions<SettingsOptions> settingOptions


<p class="label">@viewHelper.Localizer("session_account_price",@settingOptions.Value.AppName)</p>
<div class="d-flex g-16" style="flex-flow: wrap;">
    <p class="mb-0 font-500">{{ vm.userData.email }} </p>
    <button type="button" name="button" class="link"
            ng-click="vm.goToStepOne()">
        @viewHelper.Localizer("session_change_email")
    </button>
</div>

<div class="my-28">
    <label for="password_login">@viewHelper.Localizer("password_string")</label>
    <div class="input-with-icon">
        <input ng-attr-type="{{ vm.eyeUser ? 'password' : 'text' }}" required ng-model="vm.userData.password"  id="password_login" name="password" placeholder="@viewHelper.Localizer("enter_your_password")" class="login_input flex-grow-1" ng-class="{'invalid' : vm.hasError(form_login, 'password') }">
        <button type="button" ng-click="vm.eyeUser = !vm.eyeUser" aria-label="visibilidad">
            <i ng-class="{'icons-visibility-off': vm.eyeUser, 'icons-visibility': !vm.eyeUser}" class="font-24"></i>
        </button>
    </div>
    <p class="invalid-feedback-special" ng-show="vm.currentErrorMsg && vm.currentErrorMsg.length">{{vm.currentErrorMsg}}</p>
    <p class="invalid-feedback-special" ng-show="form_login.password.$dirty && form_login.password.$invalid">
        @viewHelper.Localizer("enter_your_password")
    </p>
    <p class="invalid-feedback-special" ng-show="form_login.$submitted && form_login.password.$error.required && !form_login.password.$dirty">
        @viewHelper.Localizer("session_password_verified")
    </p>
</div>

<button type="submit" name="button" class="btnPrimary w-100 mb-28">@viewHelper.Localizer("login_continue")</button>
<div class="w-100 text-center">
    <button ng-show="vm.settings.forgot" type="button" name="button" class="btnTertiary"
            ng-click="vm.forgotPasswordForm(form_login)">
        @viewHelper.Localizer("forgot_password") 
    </button>
</div>
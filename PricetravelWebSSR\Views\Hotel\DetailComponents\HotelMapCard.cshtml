@using PricetravelWebSSR.Helpers
@using PricetravelWebSSR.Models.HotelFacade
@using PricetravelWebSSR.Models.Response

@inject ViewHelper viewHelper

@{
    var hotel = ViewData["Hotel"] as ContentHotelResponse;
}

<div
    class="CardContainer"
    ng-show="vm.showCardMap"
    >
    <button class="close" type="button" ng-click="vm.showCardMap = !vm.showCardMap">
        <i class="icons-close"></i>
    </button>

    <section class="HotelCard HotelCard--map" >
        <!-- PHOTO -->
        <div class="HotelCard__photo">
            <img src="@hotel.Gallery[0].CloudUri" alt="@hotel.Name">
        </div>

        <!-- CONTENT -->
        <div class="HotelCard__content">
            <div class="flex-grow-1">
                <h2>@hotel.Name</h2>
                <i class="content__stars">@Html.Raw(viewHelper.GetStarsHtml(hotel.Stars))</i>

                <!-- Highlights -->
                <ul class="content__highlights d-none d-sm-block">
                    <li class="highlights__item" ng-repeat="highlight in vm.hotel.highlights | limitTo: vm.config.limitHighlightElement" title="{{highlight.info}}">
                        <i class="icons-check d-inline-flex"></i>
                        <span class="item">{{ highlight.info }}</span>
                    </li>
                </ul>
            </div>

            <!-- Rating -->
            <div class="Rating mt-md-2">
                <span class="rating__tag">{{vm.hotel.surveyAverage.averageValue * 2}}</span>
                <div class="d-flex flex-wrap flex-md-column">
                    <span class="rating__category">{{vm.hotel.surveyAverage.averageValue |  getRatingDesc}}&nbsp;</span>                    
                    <span class="d-none d-sm-inline-block" ng-if="vm.hotel.surveyAverage.totalSurveys">
                        {{(vm.hotel.surveyAverage.totalSurveys  | number ) | commanseparatecolo}} @viewHelper.Localizer("titleReviews")
                    </span>
                    <span class="d-none d-sm-inline-block" ng-if="!vm.hotel.surveyAverage.totalSurveys">
                        0 @viewHelper.Localizer("titleReviews")
                    </span>
                </div>
            </div>
        </div>

        <!-- FOOTER -->
        <div class="HotelCard__footer" ng-show="!vm.cheaperRoomRate.disabled && !vm.cheaperRoomRate.loading" ng-cloak>
            <p class="mb-0 font-12">@viewHelper.Localizer("hotel.per_night") @viewHelper.Localizer("from_txt")</p>
            <!-- Discount -->
            <div ng-if="vm.hotel.taxes.discount > 0">
                <span class="DiscountTag" ng-class="{'sale-icon': vm.hotel.showHotSBlackFri , 'disccount-icon': !vm.hotel.showHotSBlackFri}">
                    {{vm.hotel.taxes.discount | number: 0 }}%
                </span>
                <s class="footer__discountedPrice"> <currency-display amount="vm.hotel.taxes.totalRoomRateBeforePromoPerNight"></currency-display> </s>
            </div>
            <!-- Price per Night -->
            <div class="footer__pricePerNight">
                <i class="icons-flame1" ng-if="vm.hotel.showHotSBlackFri"></i>
                <currency-display amount="vm.sourceTriGooHotelsIds ? vm.cheaperRoomRate.taxes.totalRoomRatePerNight + vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight : vm.cheaperRoomRate.taxes.totalRoomRatePerNight" show-currency-code="true" reduce-iso-font="true"></currency-display>
            </div>
            <!-- Taxes -->
            <div ng-if="vm.country && vm.country != 'CO' && vm.country != 'MX'">
                <p class="mb-0 font-12"
                    ng-if="vm.cheaperRoomRate.taxes.hasTaxes && vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight == 0 || vm.sourceTriGooHotelsIds">
                    @viewHelper.Localizer("list_taxes_included")
                </p>
                <p class="mb-0 font-12"
                    ng-if="vm.cheaperRoomRate.taxes.hasTaxes && vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight > 0 && !vm.sourceTriGooHotelsIds">
                    + <currency-display amount="vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight"></currency-display>  @viewHelper.Localizer("taxes_and_charges")
                </p>
                <p class="mb-0 font-12"
                    ng-if="!vm.cheaperRoomRate.taxes.hasTaxes && !vm.sourceTriGooHotelsIds">
                    @viewHelper.Localizer("list_card_tax_no_include")
                </p>
            </div>
            <div ng-if="!vm.country || vm.country == 'CO' || vm.country == 'MX'">
                <p class="mb-0 font-12"
                    ng-if="vm.cheaperRoomRate.taxes.hasTaxes && vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight == 0 || vm.sourceTriGooHotelsIds">
                    @viewHelper.Localizer("list_taxes_included")
                </p>
                <p class="mb-0 font-12"
                    ng-if="vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight > 0 && !vm.sourceTriGooHotelsIds">
                    + <currency-display amount="vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight"></currency-display>
                    @viewHelper.Localizer("taxes_and_charges")
                </p>
                <p class="mb-0 font-12"
                    ng-if="!vm.cheaperRoomRate.taxes.hasTaxes && vm.cheaperRoomRate.taxes.totalTaxesPerRoomPerNight == 0 && !vm.sourceTriGooHotelsIds">
                    @viewHelper.Localizer("list_card_tax_no_include")
                </p>
            </div>
            
            <!-- Total Price -->
            <p class="footer__totalPrice">
                <span ng-if="vm.hotel.taxes.rooms  > 1 && vm.hotel.taxes">
                    @viewHelper.Localizer("total_amount")
                    {{vm.hotel.taxes.rooms}} @viewHelper.Localizer("list_rooms_abre")
                </span>
                <span ng-else>
                    @viewHelper.Localizer("total_amount")
                </span>
                <currency-display amount=" vm.hotel.taxes.totalRoomRate" show-currency-code="true"></currency-display>
            </p>
            <!-- Cancellation Policy -->
            <p class="mb-0 font-12"
                ng-if="vm.hotel.taxes.cancellationPolicies.freeCancellationExpire && vm.hotel.taxes.cancellationPolicies.freeCancellationExpire.length">
                @viewHelper.Localizer("list_free_cancel")
            </p>

            <button type="submit" class="mt-2 btnPrimary btnPrimary--xs w-100" data-dismiss="modal" aria-label="Close"
                    ng-click="vm.goSection('rooms', 100, 1000)">
                @viewHelper.Localizer("show_rooms_text")
            </button>
        </div>
        
        <!-- FOOTER No Availability -->
        <div class="HotelCard__footer" ng-if="vm.cheaperRoomRate.disabled && !vm.cheaperRoomRate.loading" ng-cloak>
            <p class="mb-0 text-right font-14">
                <strong class="d-block">@viewHelper.Localizer("list_unavailability_message_one")</strong>
            </p>
            <button type="submit" class="mt-2 btnPrimary btnPrimary--xs w-100" data-dismiss="modal" aria-label="Close"
                    ng-click="vm.goSection('rooms', 100, 1000)">
                @viewHelper.Localizer("show_rooms_text")
            </button>
        </div>

        <!-- Loader -->
        <div class="HotelCard__footer" ng-if="vm.cheaperRoomRate.loading">
            <div class="skeleton-primary mb-1" ng-style="{'width': hotel.skeleton.skeleton1}"></div>
            <div class="skeleton-primary my-1" ng-style="{'width': hotel.skeleton.skeleton2}" style="height: 24px;"></div>
            <div class="skeleton-primary mb-1" ng-style="{'width': hotel.skeleton.skeleton3}"></div>
            <div class="skeleton-primary mb-2" ng-style="{'width': hotel.skeleton.skeleton4}" style="height: 22px;"></div>
            <div class="skeleton-primary mb-1" ng-style="{'width': hotel.skeleton.skeleton5}"></div>
        </div>
    </section>
</div>
﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{
    [ProtoContract]
    public class Dish
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public string Name { get; set; }

        [ProtoMember(3)]
        public string Description { get; set; }

        [ProtoMember(4)]
        public string Image { get; set; }

        public Dish()
        {
            Name = string.Empty;
            Description = string.Empty;
            Image = string.Empty;
        }
    }
}

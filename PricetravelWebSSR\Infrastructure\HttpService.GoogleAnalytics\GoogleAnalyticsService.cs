﻿using Microsoft.AspNetCore.WebUtilities;
using PricetravelWebSSR.Infrastructure.HttpService.PTHCore.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.GoogleAnalytics;
using System.Net.Mime;
using System.Text.Json;
using System.Text;
using PricetravelWebSSR.Infrastructure.HttpService.GoogleAnalytics.Dtos;
using Microsoft.Extensions.Options;

namespace PricetravelWebSSR.Infrastructure.HttpService.GoogleAnalytics
{
    public class GoogleAnalyticsService : IGoogleAnalyticsService
    {
        private readonly HttpClient _httpClient;
        private readonly GoogleAnalyticsOptions _configuration;
        private readonly ILogger<GoogleAnalyticsService> _logger;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };

        public GoogleAnalyticsService(HttpClient httpClient, IOptions<GoogleAnalyticsOptions>  configuration, ILogger<GoogleAnalyticsService> logger)
        {
            _httpClient = httpClient;
            _configuration = configuration.Value;
            _logger = logger;

            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri("https://www.google-analytics.com/");
        }

        public async Task<bool> QueryAsync(GoogleAnalyticsEvent request, CancellationToken ct)
        {
            var result = false;

            try
            {
                var queryParams = new Dictionary<string, string>
                {
                    ["measurement_id"] = _configuration.MeasurementId,
                    ["api_secret"] = _configuration.ApiSecret
                };

                var url = QueryHelpers.AddQueryString("mp/collect", queryParams);

                var body = new
                {
                    client_id = request.ClientId,
                    events = new[]
                    {
                        new
                        {
                            name = request.EventName,
                            @params = request.Parameters 
                        }
                    }
                };

                var json = JsonSerializer.Serialize(body, _jsonSerializerOptions);
                var content = new StringContent(json, Encoding.UTF8, MediaTypeNames.Application.Json);

                var httpResponse = await _httpClient.PostAsync(url, content, ct);
                result = httpResponse.IsSuccessStatusCode;
            }
            catch (Exception ex)
            {
                _logger.LogError($"[Error] GoogleAnalyticsService {ex.Message}: Event: {JsonSerializer.Serialize(request)}");
            }

            return result;
        }
    }
}

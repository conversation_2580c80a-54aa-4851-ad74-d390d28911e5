﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@using System.Text.Json;
@using PricetravelWebSSR.Models.Collection;
@using PricetravelWebSSR.Types;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper
@{
	var resolution = viewHelper.GetImageResolution();
	var isMobile = resolution.Device == DeviceType.Mobile;
	var isRobot = viewHelper.IsRobot();
	var metaTag = ViewData["MetaTag"] as MetaTag;
	var mobile = resolution.Device == DeviceType.Mobile;
	var cultureConf = ViewData["cultureData"] as Culture;
	var seoContent = ViewData["seoContent"] as SeoResponse;
	ViewData["IsRobot"] = isRobot;
	ViewData["IsMobile"] = isMobile;
	ViewData["Resolution"] = resolution;
	ViewData["Page"] = "Auth";
	var authMode = ViewData["AuthMode"] as string;
}
@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_HeaderMinimal.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot } })
<main class="loginlayout" ng-cloak ng-controller="LoginController as vm">
	<div id="loginHeroImg" class="d-none d-lg-block">
		<img src="/assets/img/login/hero-img.webp" alt="Viaja @settingOptions.Value.SiteName">
	</div>
	<div id="loginFormContainer">
		<form id="form-user" name="form_login" novalidate="" class="formContent">
			<div class="d-center flex-column" ng-if="vm.loadingVerify">
				@await Html.PartialAsync("_Loading")
			</div>
			@if (authMode == "verifyEmail")
			{
				<div class="verification-success " ng-show="!vm.currentErrorMsg && !vm.loadingVerify">
					<div class="verified-icon font-green">
						<i class="font-24 icons-check-circle font-green"></i>
					</div>
					<h1 class="verified-title mb-2">@viewHelper.Localizer("account_verify_title")</h1>
					<div class="verified-content">
						<p class="verified-message label">@viewHelper.Localizer("account_verify_subtitle")</p>
						<article class="accountbennefits" ng-if="vm.stepRegister == 1">
							<div class="accountbennefits__container">
								<h2>@viewHelper.Localizer("account_benefits")</h2>
								<ul class="list-unstyled">
									<li>
										<img src="/assets/img/login/@(@settingOptions.Value.SiteName)/offer.svg" alt="offer">
										@viewHelper.Localizer("exclusive_dsc")
									</li>
									<li>
										<img src="/assets/img/login/@(@settingOptions.Value.SiteName)/call-center.svg" alt="call center">
										@viewHelper.Localizer("phoneline_attention")
									</li>
									<li>
										<img src="/assets/img/login/@(@settingOptions.Value.SiteName)/world-flight.svg" alt="world flight">
										@viewHelper.Localizer("hotels_packages_dsc")
									</li>
								</ul>
							</div>
						</article>

						<div class="next-steps">
							<p class="font-subtle mb-2">@viewHelper.Localizer("account_verify_qustion")</p>
							<div class="login_passwordInstructions">
								<ul>
									<li>
										<i class="font-24 icons-check-circle font-green"></i>
										<p>@viewHelper.Localizer("account_verify_qustion_one")</p>
									</li>
									<li>
										<i class="font-24 icons-check-circle font-green"></i>
										<p>@viewHelper.Localizer("account_verify_qustion_two")</p>
									</li>
									<li>
										<i class="font-24 icons-check-circle font-green"></i>
										<p>@viewHelper.Localizer("account_verify_qustion_three")</p>
									</li>
								</ul>
							</div>
						</div>
					</div>
				</div>
				<div class="verification-success" ng-show="vm.currentErrorMsg && !vm.loadingVerify">
					<div class="verified-icon font-red">
						<i class="font-24 icons-cancel-circle font-red"></i>
					</div>
					<h1 class="verified-title mb-2 font-red">@viewHelper.Localizer("account_verify_title_error")</h1>
					<p class="invalid-feedback-special font-14" ng-show="vm.currentErrorMsg && vm.currentErrorMsg.length">{{vm.currentErrorMsg}}</p>
				</div>
				<button type="button" name="button" ng-if="!vm.loadingVerify" class="btnPrimary w-100 my-28" ng-click="vm.goToLogin()">@viewHelper.Localizer("header_redirect_login")</button>
			}
			else if (authMode == "resetPassword")
			{
				<div class="verification-success " ng-show="vm.currentErrorMsg && !vm.loadingVerify">
					<div class="verified-icon font-red">
						<i class="font-24 icons-cancel-circle font-red"></i>
					</div>

					<h1 class="verified-title mb-2 font-red">@viewHelper.Localizer("account_reset_password_title_error")</h1>
					<p class="invalid-feedback-special font-14" ng-show="vm.currentErrorMsg && vm.currentErrorMsg.length">{{vm.currentErrorMsg}}</p>
				</div>
				<div class="verification-success " ng-show="!vm.currentErrorMsg && !vm.loadingVerify && vm.showMessageSucces">
					<div class="verified-icon font-green">
						<i class="font-24 icons-check-circle font-green"></i>
					</div>
					<h1 class="verified-title mb-2">@viewHelper.Localizer("account_reset_password_title")</h1>
				</div>
				<div ng-show="!vm.currentErrorMsg && !vm.loadingVerify && !vm.showMessageSucces">
					<!-- Título y descripción -->
					<h1 class="verified-title mb-2">@viewHelper.Localizer("account_reset_password_new_title")</h1>
					<p class="font-subtle mb-4">@viewHelper.Localizer("account_reset_password_new_subtitle")</p>

					<!-- Nueva Contraseña -->
					<label for="new_password">@viewHelper.Localizer("account_reset_password_new_password")</label>
					<div class="input-with-icon">
						<input ng-attr-type="{{ vm.eyeUser ? 'password' : 'text' }}"
							   required
							   ng-model="vm.userData.password"
							   id="password_create_login"
							   name="password"
							   placeholder="@viewHelper.Localizer("enter_your_password")"
							   ng-class="{'invalid': (form_login.$submitted || form_login.password.$dirty || form_login.password.$touched) && form_login.password.$error.required}">
						<button type="button" ng-click="vm.eyeUser = !vm.eyeUser" ng-class="{'checked': vm.isPasswordValid(vm.userData.password) && form_login.$dirty}">
							<i ng-class="{'icons-visibility-off': vm.eyeUser, 'icons-visibility': !vm.eyeUser}" class="font-24"></i>
						</button>
						<i class="font-24 icons-check-circle font-green"
						   ng-show="vm.isPasswordValid(vm.userData.password) && form_login.password.$dirty"></i>
						<p class="invalid-feedback-special" ng-show="(form_login.password.$dirty || form_login.password.$touched) && form_login.password.$invalid">
							@viewHelper.Localizer("session_password_error")
						</p>
					</div>

					<!-- Confirmar Contraseña -->
					<label for="new_password">@viewHelper.Localizer("account_reset_password_new_password_confirm")</label>
					<div class="input-with-icon">
						<input ng-attr-type="{{ vm.eyeUserConfirm ? 'password' : 'text' }}"
							   required
							   ng-model="vm.userData.confirmPassword"
							   id="confirmPassword_create_login"
							   name="confirmPassword"
							   compare-to="vm.userData.password"
							   placeholder="@viewHelper.Localizer("account_reset_password_new_password_confirm_place")"
							   ng-class="{'invalid': (form_login.$submitted || form_login.confirmPassword.$dirty || form_login.confirmPassword.$touched) && (form_login.confirmPassword.$error.required || form_login.confirmPassword.$error.compareTo)}">
						<button type="button" ng-click="vm.eyeUserConfirm = !vm.eyeUserConfirm"
								ng-class="{'checked': vm.userData.confirmPassword === vm.userData.password && form_login.confirmPassword.$dirty}">
							<i ng-class="{'icons-visibility-off': vm.eyeUserConfirm, 'icons-visibility': !vm.eyeUserConfirm}" class="font-24"></i>
						</button>
						<i class="font-24 icons-check-circle font-green"
						   ng-show="vm.userData.confirmPassword === vm.userData.password && vm.userData.confirmPassword.length > 0"></i>
						<p class="invalid-feedback-special" ng-show="(form_login.confirmPassword.$dirty || form_login.confirmPassword.$touched)  && vm.userData.confirmPassword != vm.userData.password ">
							@viewHelper.Localizer("account_reset_password_new_password_confirm_error")
						</p>
					</div>

					<!---- CONTRASEÑA INSTRUCCIONES ----->
					<div class="login_passwordInstructions" ng-show="vm.userData.password.length > 0">
						<p class="font-subtle mb-2">@viewHelper.Localizer("session_password")</p>
						<ul>
							<li>
								<i class="font-24 icons-check-circle" ng-class="{ 'font-disabled' : vm.userData.password.length < 8, 'font-green' : vm.userData.password.length >= 8 }"></i>
								<p class="">@viewHelper.Localizer("session_almost_8")</p>
							</li>
							<li>
								<i class="font-24 icons-check-circle" ng-class="{ 'font-disabled' : !vm.containsUppercase(vm.userData.password), 'font-green' : vm.containsUppercase(vm.userData.password) }"></i>
								<p class="">@viewHelper.Localizer("session_almost_mayus")</p>
							</li>
							<li>
								<i class="font-24 icons-check-circle" ng-class="{ 'font-disabled' : !vm.containsNumber(vm.userData.password), 'font-green' : vm.containsNumber(vm.userData.password) }"></i>
								<p class="">@viewHelper.Localizer("session_almost_number")</p>
							</li>
						</ul>
					</div>
					<!-- Botón de confirmación -->
					<button type="button"
							class="btnPrimary w-100 mt-4"
							ng-click="vm.resetPasswordAccount(vm.userData.password)"
							ng-disabled="!vm.isPasswordValid(vm.userData.password) || vm.userData.password !== vm.userData.confirmPassword || vm.loadingVerify">
						 @viewHelper.Localizer("account_reset_password_btn")
					</button>

				</div>
				<button type="button" name="button" ng-if="!vm.loadingVerify && (vm.currentErrorMsg || vm.showMessageSucces)" class="btnPrimary w-100 my-28" ng-click="vm.goToLogin()">@viewHelper.Localizer("header_redirect_login")</button>
			}
			<div class="verification-success " ng-show="vm.modeNull">
				<div class="verified-icon font-red">
					<i class="font-24 icons-cancel-circle font-red"></i>
				</div>
				<h1 class="verified-title mb-2 font-red">@viewHelper.Localizer("account_reset_password_acction")</h1>
				<p class="invalid-feedback-special font-14">@viewHelper.Localizer("account_reset_password_acction_subtitle")</p>
				<button type="button" name="button" class="btnPrimary w-100 my-28" ng-click="vm.goToLogin()">@viewHelper.Localizer("header_redirect_login")</button>
			</div>
		</form>
	</div>
	@await Html.PartialAsync($"~/Views/Shared/Components/Questions.cshtml", new ViewDataDictionary(ViewData) { { "PlacesInfoS", null }, { "seoContents", seoContent }, { "Title", "" } })
</main>

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "footerMinimal", true }, { "showLinksLegals", true }, { "login", isRobot } })


@section Preload {
	<link rel="preconnect" href="@settingOptions.Value.CloudCdn">
	<link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/account.css")" as="style">

}
@section Meta {
	@await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
	@if (metaTag.Question.mainEntity.Count > 0)
	{
		<script type="application/ld+json">
			@Json.Serialize(metaTag.Question)
		</script>
	}
}
@section Css {
	<link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/account.css")">
}
@section Scripts {
	<script src="@staticHelper.GetVersion("/assets/js/app.js")"></script>
	<script src="@staticHelper.GetVersion("/assets/js/login/controller.js")"></script>
}

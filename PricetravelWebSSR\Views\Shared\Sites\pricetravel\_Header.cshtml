@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Models.Login
@using PricetravelWebSSR.Models.Meta.Alternate
@using PricetravelWebSSR.Options
@using PricetravelWebSSR.Models.Configuration
@using PricetravelWebSSR.Types
@using PricetravelWebSSR.Models.Discount;
@using System.Text.Json;

@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CultureOptions> cultureOptions
@inject IOptions<CurrencyOptions> currencyOptions

@{
    var cultureConfiguration = ViewData["cultureData"] as Culture;
    var navs = ViewData["navs"] ?? "";
    var login = (bool)ViewData["login"];
    var tabs = cultureConfiguration.Tabs;
    var resolution = viewHelper.GetImageResolution();
    var mobile = resolution.Device == DeviceType.Mobile;
    var bot = viewHelper.IsRobot();
    var routeHome = ViewData["IsRoutMain"] ?? "";
    var pt0Head = mobile ? "pt-0" : "";
    var isRobot = viewHelper.IsRobot();
    var page = (string)ViewData["PageRoot"];
    var query = viewHelper.GetCurrentQueryStringCom(Context);
    var isHome = (bool)(ViewData["isHome"] ?? false);
    var isList = (bool)(ViewData["IsList"] ?? false);
    var IsDetail = (bool)(ViewData["IsDetail"] ?? false);
    var route = (string)(ViewData["PageOrig"] ?? "/hoteles");
    var user = ViewData["User"] as User;
    var currencyConfiguration = ViewData["currencyData"] as PricetravelWebSSR.Options.Currency;
    var alternates = ViewData["Alternates"] as AlternateMain;
    var userLocation = ViewData["UserLocation"] as UserLocation;
}

<header ng-cloak @(!login ? "ng-c" : "")ontroller="LoginController as vm" id="header">
    <!-------- BANNERS -------->
    @if (isHome)
    {
        @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/Banners/_HeaderBanner.cshtml", new ViewDataDictionary(ViewData))
    }
    @if (!isRobot)
    {
        @await Html.PartialAsync("~/Views/Shared/Components/Banner.cshtml")
    }
    <!-------- HEADER -------->
    <nav class="container">
        <div class="header__top">
            <!---------------------- LOGO ---------------------->
            <a class="header__logo" href="@settingOptions.Value.SiteUrl" title="@settingOptions.Value.AppName">
                <img alt="@settingOptions.Value.AppName" src="@(settingOptions.Value.CloudCdn)@viewHelper.Localizer("img",@settingOptions.Value.SiteName.ToLower())" width="188" height="28">
            </a>


            <!--------------- MAIN BUTTONS --------------->
            <div class="header__buttons d-none d-md-flex">
                <!--------------- LANGUAGE & CURRENCY --------------->
                <div class="position-relative">
                    <button class="header__btn" ng-click="vm.showModal('modal_langcurr')" aria-haspopup="true" type="button" aria-controls="modal_langcurr">
                        <img width="24" height="24" bn-lazy-src="/assets/img/header/@(settingOptions.Value.SiteName)/@(cultureConfiguration?.CultureCode).svg" alt="@(viewHelper.Localizer("language") + " " + cultureConfiguration?.Name)" class="rounded-circle" style="object-fit: cover;">
                        @(cultureConfiguration?.Name) - @currencyConfiguration?.CurrencyCode
                    </button>
                </div>

                <!--------------- HELP --------------->
                <a class="header__btn" href="@viewHelper.Localizer("help_" + settingOptions.Value.SiteName)" target="_blank" ng-click="vm.sendContentHeader('@viewHelper.Localizer("help")')">
                    @viewHelper.Localizer("help")
                </a>

                <!--------------- PHONES --------------->
                <div class="position-relative">
                    <button class="header__btn" aria-expanded="false" aria-haspopup="true" data-dropdown-toggle="phoneDropdown" aria-controls="phoneMenu">
                        <i class="icons-phone" aria-hidden="true" style="width: 20px;"></i>
                        <span>
                            <span class="d-none d-lg-inline">@viewHelper.Localizer("to_reservate")</span>&nbsp;
                            <strong class="skillbase_p">@(userLocation.Country == "MX" ? settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat : settingOptions.Value.StaticPhoneNumbers.UsaPhoneFormat)</strong>
                        </span>
                    </button>
                    <div class="header__dropdown header__dropdown--fullwidth" data-dropdown-menu id="phoneDropdown">
                        <menu class="dropdown__container" id="phoneMenu" role="menu">
                            <li role="presentation">
                                <a class="dropdown__link justify-content-between" href="tel:@settingOptions.Value.StaticPhoneNumbers.PrimaryPhone" role="menuitem">
                                    @viewHelper.Localizer("links_mx_country")
                                    <strong class="skillbase_p">@settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat</strong>
                                </a>
                            </li>
                            <li role="presentation">
                                <a class="dropdown__link justify-content-between" href="tel:@settingOptions.Value.StaticPhoneNumbers.SecondaryPhone" role="menuitem">
                                    @viewHelper.Localizer("links_co_country")
                                    <strong>@settingOptions.Value.StaticPhoneNumbers.SecondaryPhoneFotmat</strong>
                                </a>
                            </li>
                            <li role="presentation">
                                <a class="dropdown__link justify-content-between" href="tel:@settingOptions.Value.StaticPhoneNumbers.UsaPhone" role="menuitem">
                                    @viewHelper.Localizer("links_us_simple_country")
                                    <strong>@settingOptions.Value.StaticPhoneNumbers.UsaPhoneFormat</strong>
                                </a>
                            </li>
                            <li role="presentation">
                                <a class="dropdown__link justify-content-between" href="tel:@settingOptions.Value.StaticPhoneNumbers.RestOfWorld" role="menuitem">
                                    @viewHelper.Localizer("links_oc_country")
                                    <strong>@settingOptions.Value.StaticPhoneNumbers.RestOfWorldFormat</strong>
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation">
                                <a class="dropdown__link" target="_blank" rel="noopener noreferrer" href="https://wa.me/@(viewHelper.Localizer("phone_phone_whatsapp"))" role="menuitem">
                                    <i class="icons-whatsapp"></i>
                                    Whatsapp
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation">
                                <a class="dropdown__link" target="_blank" rel="noopener noreferrer" href="http://m.me/pricetravel" role="menuitem">
                                    <i class="icons-messenger"></i>
                                    Messenger
                                </a>
                            </li>
                        </menu>
                    </div>
                </div>

                <!----------- LOGIN ----------->
                <div class="position-relative">
                    <button class="header__btn header__btn--login" data-dropdown-toggle="no_login_content" aria-expanded="false" aria-haspopup="true" type="button" aria-controls="loginPopup">
                        <span ng-if="!vm.sessionData.isLogin">@viewHelper.Localizer("session_login_session")</span>

                        <div class="logged__avatar" aria-hidden="true" ng-if="vm.sessionData.isLogin">
                            <span>{{ vm.avatarConf.avatar }}</span>
                        </div>
                        <span class="d-none d-lg-block" ng-if="vm.sessionData.isLogin">{{vm.sessionData.displayName}}</span>

                        <i class="icons-menu"></i>
                    </button>

                    <div class="header__dropdown" data-dropdown-menu id="no_login_content">
                        <menu class="dropdown__container" role="menu" id="loginPopup">
                            <li class="dropdown__item dropdown__item--login" ng-if="!vm.sessionData.isLogin">
                                <p class="mb-12">@viewHelper.Localizer("login_and_get") <strong>@viewHelper.Localizer("10_percent_dsc")</strong> @viewHelper.Localizer("on_your_next_trip")</p>
                                <a class="link" href="@($"/{cultureConfiguration.CultureCode}/login?redirectTo={query}")">
                                    @viewHelper.Localizer("start_session_or_create")
                                    <i class="icons-angle-right"></i>
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation">
                                <a class="dropdown__link" href="/@(cultureConfiguration.CultureCode)/user" ng-click="vm.sendContentHeader('@viewHelper.Localizer("account")')" ng-if="vm.sessionData.isLogin" role="menuitem">
                                    <i class="icons-person"></i>
                                    @viewHelper.Localizer("account")
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation">
                                <a class="dropdown__link" href="@($"/{cultureConfiguration.CultureCode}/favorites")" ng-click="vm.sendContentHeader('@viewHelper.Localizer("favorites")')" role="menuitem">
                                    <i class="icons-heart"></i>
                                    @viewHelper.Localizer("favorites")
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation">
                                <a class="dropdown__link"
                                   href="@($"/{cultureConfiguration.CultureCode}/online-payment")"
                                   ng-click="vm.sendContentHeader('@viewHelper.Localizer("payOnline")')"
                                   role="menuitem">
                                   <i class="icons-credit-card1"></i>
                                    @viewHelper.Localizer("payOnline")
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation">
                                <a class="dropdown__link"
                                   href="{{ vm.sessionData.isLogin ? '/@(cultureConfiguration.CultureCode)/user/reservations' : '@viewHelper.Localizer("get_booking_" + settingOptions.Value.SiteName, cultureConfiguration.CultureCode)'}}"
                                   ng-click="vm.sendContentHeader('@viewHelper.Localizer("get_booking")')" role="menuitem">
                                    <i class="icons-suitcase"></i>
                                    @viewHelper.Localizer("my_trips")
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation" ng-if="vm.sessionData.isLogin">
                                <button class="dropdown__link" ng-click="vm.logout()" role="menuitem">
                                    <i class="icons-right-from-bracket"></i>
                                    @viewHelper.Localizer("log_out")
                                </button>
                            </li>
                        </menu>
                    </div>
                </div>
            </div>

            <div class="d-flex g-16 align-items-center d-md-none">
                <!--------------- MENU MOBILE --------------->
                <div class="header__menu">
                    <button class="menu__btn menu__btn--user" ng-click="vm.openDropdown('menu_user')" aria-label="@viewHelper.Localizer("trips_account")" aria-expanded="false" ng-class="{'active': vm.menu_user, '': !vm.menu_user, 'menu__btn--logged': vm.sessionData.isLogin}">
                        <i class="icons-person" ng-if="!vm.sessionData.isLogin"></i>

                        <div class="logged__avatar" aria-hidden="true" ng-if="vm.sessionData.isLogin">
                            <span>{{ vm.avatarConf.avatar }}</span>
                        </div>
                    </button>

                    <div class="menu__window menu__window--right" ng-class="{'show': vm.menu_user}" off-click="vm.openDropdown('menu_user', true)" off-click-activator="vm.menu_user" role="dialog" aria-label="menu">
                        <div class="menu__session" ng-class="{'menu__session--logged': vm.sessionData.isLogin}">
                            <h2 class="session__title">
                                <span ng-if="!vm.sessionData.isLogin">@viewHelper.Localizer("email_welcome_user", settingOptions.Value.AppName)</span>
                                <div class="logged__avatar" ng-if="vm.sessionData.isLogin">{{ vm.avatarConf.avatar }}</div>
                                <span ng-if="vm.sessionData.isLogin">¡@viewHelper.Localizer("hi"), {{vm.sessionData.displayName}}!</span>
                            </h2>
                            <button class="menu__closeBtn" ng-click="vm.openDropdown('menu_user')" aria-label="close">
                                <i class="icons-close"></i>
                            </button>

                            <p class="mb-12" ng-if="!vm.sessionData.isLogin">@viewHelper.Localizer("login_and_get") <strong>@viewHelper.Localizer("10_percent_dsc")</strong> @viewHelper.Localizer("on_your_next_trip")</p>
                            <a class="link" href="@($"/{cultureConfiguration.CultureCode}/login?redirectTo={query}")" ng-if="!vm.sessionData.isLogin">
                                @viewHelper.Localizer("start_session_or_create")
                                <i class="icons-angle-right"></i>
                            </a>
                        </div>

                        <menu class="menu__navigation">
                            <li>
                                <a href="/@(cultureConfiguration.CultureCode)/user" ng-click="vm.sendContentHeader('@viewHelper.Localizer("account")')" ng-if="vm.sessionData.isLogin">
                                    <i class="icons-person"></i>
                                    @viewHelper.Localizer("account")
                                </a>
                            </li>
                            <li>
                                <a href="@($"/{cultureConfiguration.CultureCode}/favorites")" ng-click="vm.sendContentHeader('@viewHelper.Localizer("favorites")')">
                                    <i class="icons-heart"></i>
                                    @viewHelper.Localizer("favorites")
                                </a>
                            </li>
                            <li class="dropdown__item" role="presentation">
                                <a class="dropdown__link"
                                   href="@($"/{cultureConfiguration.CultureCode}/PayOnline")"
                                   ng-click="vm.sendContentHeader('@viewHelper.Localizer("payOnline")')"
                                   role="menuitem">
                                    <i class="icons-credit-card1"></i>
                                    @viewHelper.Localizer("payOnline")
                                </a>
                            </li>
                            <li>
                                <a href="{{ vm.sessionData.isLogin ? '/@(cultureConfiguration.CultureCode)/user/reservations' : '@viewHelper.Localizer("get_booking_" + settingOptions.Value.SiteName, cultureConfiguration.CultureCode)'}}"
                                ng-click="vm.sendContentHeader('@viewHelper.Localizer("get_booking")')">
                                    <i class="icons-suitcase"></i>
                                    @viewHelper.Localizer("my_trips")
                                </a>
                            </li>
                            <li ng-if="vm.sessionData.isLogin">
                                <button class="session__link" ng-click="vm.logout()" role="button">
                                    <i class="icons-right-from-bracket"></i>
                                    @viewHelper.Localizer("log_out")
                                </button>
                            </li>
                        </menu>
                    </div>
                </div>

                <!--------------- MENU MOBILE --------------->
                <div class="header__menu">
                    <button class="menu__btn" ng-click="vm.openDropdown('dropdown_nav')" aria-label="menu" aria-expanded="false" ng-class="{'active': vm.dropdown_nav, '': !vm.dropdown_nav}">
                        <i class="icons-menu" style="width: 24px;"></i>
                    </button>

                    <div class="menu__window menu__window--right" ng-class="{'show': vm.dropdown_nav}" off-click="vm.openDropdown('dropdown_nav', true)" off-click-activator="vm.dropdown_nav" role="dialog" aria-label="menu">
                        <div class="d-flex justify-content-end w-100">
                            <button class="menu__closeBtn" ng-click="vm.openDropdown('dropdown_nav')" aria-label="close">
                                <i class="icons-close"></i>
                            </button>
                        </div>

                        <menu class="menu__navigation">
                            <li>
                                <button class="w-100 d-flex align-items-center g-8" ng-click="vm.showModal('modal_langcurr')" aria-haspopup="true" type="button" aria-controls="modal_langcurr">
                                    <img width="18" height="18"
                                        class="rounded-circle"
                                        style="object-fit: cover;"
                                        src="/assets/img/header/@(settingOptions.Value.SiteName)/@(cultureConfiguration?.CultureCode).svg"
                                        loading="lazy">
                                    <span class="flex-grow-1 d-flex align-items-center justify-content-between">
                                        <span class="d-flex align-items-center g-base">
                                            @viewHelper.Localizer("language") & @viewHelper.Localizer("currency")
                                            <i class="icons-angle-right font-24"></i>
                                        </span>
                                        @(cultureConfiguration?.Name) (@currencyConfiguration?.CurrencyCode)
                                    </span>
                                </button>
                            </li>

                            <div class="px-3">
                                <hr>
                            </div>

                            @foreach (var tab in tabs)
                            {
                                <li role="presentation">
                                    @if (routeHome.ToString() == tab.Url)
                                    {
                                        <a class="tab @(page == tab.Name ? "current" : "")" @if (tab.IsDisney())
                                        {
                                            @Html.Raw("target='_blank' rel='noopenner noreferrer'")
                                        } href="@tab.Url" role="menuitem">
                                            <i class="@tab.Class" style="width: 20px;"></i>
                                            @viewHelper.Localizer(tab.Title)
                                        </a>
                                    }
                                    else
                                    {
                                        <a class="tab @(page == tab.Name ? "current" : "")" @if (tab.IsDisney())
                                        {
                                            @Html.Raw("target='_blank' rel='noopenner noreferrer'")
                                        } href="@tab.Url" role="menuitem">
                                            <i class="@tab.Class" style="width: 20px;"></i>
                                            @viewHelper.Localizer(tab.Title)
                                        </a>
                                    }
                                </li>
                            }

                            <div class="px-3">
                                <hr>
                            </div>

                            <li>
                                <a href="@viewHelper.Localizer("help_" + settingOptions.Value.SiteName)" target="_blank" rel="noopenner noreferrer" ng-click="vm.sendContentHeader('@viewHelper.Localizer("help")')">
                                    <i class="icons-question-circle"></i>
                                    @viewHelper.Localizer("help")
                                </a>
                            </li>

                            <div class="px-3">
                                <hr>
                            </div>

                            <li role="presentation">
                                <a role="menuitem">
                                    <i class="icons-phone"></i>
                                    @viewHelper.Localizer("reserve_by_phone")
                                </a>
                            </li>
                            <li role="presentation">
                                <a class="header-phonebase pl-40" href="tel:@settingOptions.Value.StaticPhoneNumbers.PrimaryPhone" role="menuitem">
                                    <span>
                                        @viewHelper.Localizer("links_mx_country")
                                        <strong class="skillbase_p">@settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat</strong>
                                    </span>
                                </a>
                            </li>
                            <li role="presentation">
                                <a href="tel:@settingOptions.Value.StaticPhoneNumbers.SecondaryPhone" role="menuitem" class="pl-40">
                                    <span>
                                        @viewHelper.Localizer("links_co_country")
                                        <strong>@settingOptions.Value.StaticPhoneNumbers.SecondaryPhoneFotmat</strong>
                                    </span>
                                </a>
                            </li>
                            <li role="presentation">
                                <a href="tel:@settingOptions.Value.StaticPhoneNumbers.UsaPhone" role="menuitem" class="pl-40">
                                    <span>
                                        @viewHelper.Localizer("links_us_simple_country")
                                        <strong>@settingOptions.Value.StaticPhoneNumbers.UsaPhoneFormat</strong>
                                    </span>
                                </a>
                            </li>
                            <li role="presentation">
                                <a href="tel:@settingOptions.Value.StaticPhoneNumbers.RestOfWorld" role="menuitem" class="pl-40">
                                    <span>
                                        @viewHelper.Localizer("links_oc_country")
                                        <strong>@settingOptions.Value.StaticPhoneNumbers.RestOfWorldFormat</strong>
                                    </span>
                                </a>
                            </li>
                            <li>
                                <a target="_blank" rel="noopener noreferrer" href="https://wa.me/@(viewHelper.Localizer("phone_phone_whatsapp"))">
                                    <i class="icons-whatsapp"></i>
                                    Whatsapp
                                </a>
                            </li>
                            <li>
                                <a target="_blank" rel="noopener noreferrer" href="http://m.me/pricetravel">
                                    <i class="icons-messenger"></i>
                                    Messenger
                                </a>
                            </li>
                        </menu>
                    </div>
                </div>
            </div>
        </div>
        <div class="header__bottom @(isList || IsDetail ? "d-none d-md-flex":"")">
            <menu class="bottom__container" role="menubar" style="    scrollbar-width: none;">
                @foreach (var tab in tabs)
                {
                    <li class="bottom__tab" role="presentation">
                        @if (routeHome.ToString() == tab.Url)
                        {
                            <a class="@(page == tab.Name ? "current" : "")" @if (tab.IsDisney())
                            {
                                @Html.Raw("target='_blank' rel='noopenner noreferrer'")
                            } href="@tab.Url" role="menuitem">
                                <i class="@tab.Class" style="width: 18px;"></i>
                                @viewHelper.Localizer(tab.Title)
                            </a>
                        }
                        else
                        {
                            <a class="@(page == tab.Name ? "current" : "")" @if (tab.IsDisney())
                            {
                                @Html.Raw("target='_blank' rel='noopenner noreferrer'")
                            } href="@tab.Url" role="menuitem">
                                <i class="@tab.Class" style="width: 18px;"></i>
                                @viewHelper.Localizer(tab.Title)
                            </a>
                        }
                    </li>
                }
            </menu>
        </div>
    </nav>

    @if (!isRobot)
    {
        @await Html.PartialAsync("~/Views/Shared/Modals/LanguageCurrencyModal.cshtml")
    }
</header>
@if (!isRobot)
{
    @await Html.PartialAsync("~/Views/Shared/Modals/TerminosCupon.cshtml")
}

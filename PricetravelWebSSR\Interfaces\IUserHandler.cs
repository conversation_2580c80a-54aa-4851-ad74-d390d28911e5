﻿using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Models.User.Reservation;

namespace PricetravelWebSSR.Interfaces
{
    public interface IUserHandler : IQueryHandlerAsync<Reservation, Reservation>, IQueryHandlerAsync<ProductReservation, List<ProductReservation>>, IQueryHandlerAsync<UserProfile, UserProfile>, IQueryHandlerAsync<ItineraryRequest, Reservation>
    {
    }
}

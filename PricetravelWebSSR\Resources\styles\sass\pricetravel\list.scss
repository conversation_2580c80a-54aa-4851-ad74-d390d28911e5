@import 
'components/fonts.scss',
'./app',
 'components/breadcrumb', 
 'components/header.scss', 
 'components/layouts.scss', 
 'components/checkbox-radio.scss', 
 'components/alerts.scss', 
 'components/_tag.scss', 
 'components/bookers.scss', 
 'components/skeleton.scss', 
 'components/input', 
 'components/_modals', 
 'components/_banner-rapd', 
 'components/icons_ui', 
 'components/icons_banks', 
 'components/icon-stars', 
 'components/_icons_bootstrap.scss', 
 'components/icons_font', 
 'components/modal-fullscreen', 
 'components/card-horizontal', 
 'components/_carrusel', 
 'components/map-search', 
 'components/_loading', 
 'components/cards.scss', 
 'components/icon-stars', 
 'components/_modal-rapd.scss',
 'components/cuopon',
 'components/pagination',
 'components/footer-banners';

@import '../../../../node_modules/bootstrap-slider/dist/css/bootstrap-slider';
/*SEARCH HOTELS*/
.icon-search-hotel {
    top: 1px;
    position: relative;
}
.text-search-hotel {
    position: relative;
    top: -2px;
}

.width-text-search {
    min-width: 250px !important; 
    width: 100% !important;
}
@media (min-width: 992px) {
    .width-text-search {
        min-width: 344px !important;
        width: max-content !important;
        max-width: 349px !important;
    }
    .apply-overflow {
        max-width: 309px !important;
    }
}
.autocomplete-searh-hotel {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
@media (max-width: 555px) {
    .width-search-hotel-mobile {
        max-width: 300px !important;
    }
}
.button-sugestion {
    align-items: center;
    height: 44px;
    padding: 0.75rem 0.5rem 0.75rem 0.5rem;
    gap: 4px;
}
.margin-search-hotel {
    margin-left: 0.75rem;
    margin-right: 0.75rem;
}


.pt-breadcrumbs {
    margin-bottom: 0.5rem;

    .pt-breadcrumbs__item {
        font-size: .85rem;

        &:after {
            content: "\002F";
            display: inline-block;
            padding-left: 0.25rem;
            padding-right: 0.25rem;
        }
    }
}

.banner-telephone {
    color:  var(--text-secondary);
    background: var(--bg-level1);
    font-weight: 500;
    padding: 8px;
    text-align: center;
    margin: 8px 0px;
    font-size: 14px;
}

.card-booker {
    border: 1px solid var(--border-subtle);
    min-height: auto;
    background-color: var(--bg-base);
    justify-content: center;
}

.form-check-input:checked {
    opacity: 1;
}

i.icons-flame1 {
    color: var(--text-error);
}

.padding-icon-flame1 {
    padding-top: 1px;
    padding-left: 2px;
}


/** Formas de pago (Opcional hasta saber la maqueta)  **/
.payment-methods-banner {
    background-color: var(--bg-base);
    border: 1px solid var(--border-subtle);
    text-align: center;
    cursor: pointer;
    font-weight: 500;
    border-radius: 4px;
}

//FILTROS ACTIVOS
// .content-tags {
//     margin-bottom: 0.5rem !important;
// }

.tag {
    display: inline-block;
    margin: 4px;
    white-space: nowrap;
}

.border-dark {
    border-color: var(--border-strong) !important;
}

/*  ads */
.skyscraper {
    // position: absolute;
    // right: -160px;
    // top: 0;

    div {
        width: 160px;
        height: 600px;
        background-color: var(--bg-level3);
        margin-bottom: 20px;
    }
}

.filter-mobile-button {
    background-color: var(--bg-secondary);
    color: var(--text-oncolor);
    border-radius: 5rem;
    padding: 0.5rem 1.25rem;
    position: fixed;
    bottom: 5px;
    left: 40%;
    z-index: 1;
}

.btnFloating {
    margin: auto;
    background-color: var(--bg-base);
    border: 1px solid var(--border-strong);
    color: var(--text-primary);
    font-weight: 600;
    border-radius: 5rem;
    padding: 4px 20px;
    position: sticky;
    bottom: 20px;
    z-index: 10;
    width: 75%;
    max-width: 380px;
    text-align: center;
    box-shadow: var(--shadow-400);
    -webkit-box-shadow: var(--shadow-400);
    -moz-box-shadow: var(--shadow-400);
    display: flex;
    align-items: center;
    justify-content: space-evenly;

    &__option {
        position: relative;
        padding: 8px;
        display: inline-block;
        font-size: 0.75rem;
        min-width: 80px;
        flex-shrink: 0;

        &--active::before {
            content: '';
            display: inline-block;
            width: 10px;
            height: 10px;
            -moz-border-radius: 7.5px;
            -webkit-border-radius: 7.5px;
            border-radius: 7.5px;
            background-color: var(--bg-primary-hover);
            position: absolute;
            right: 4px;
            top: 4px;
        }
    }
    &__divider {
        flex-shrink: 0;
        width: 1px;
        height: 20px;
        background: var(--border-strong);
    }
    i {
        font-size: 18px;
        vertical-align: sub;
    }
}

//ESTILOS FONTS
h1 {
    font-size: 28px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
}
// ESTILOS CARDS
.card-title, .card-highlights {
    margin-bottom: 0rem;
}

.card-img-top {
    overflow: hidden;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -o-user-select: none;
    user-select: none;
    object-fit: cover;
    height: 100%;
    color: transparent;
}

.card-img-top:not([src]) {
    visibility: hidden;
}

.small {
    margin: 0;
}

.search-paxes {
    z-index: 3;
}

skeleton-secondary {
    &:empty {
        max-width: 270px;
        display: block;
        float: none;
        margin-bottom: 1rem;
    }
}


.vr {
    display: inline-block;
    align-self: stretch;
    width: 1px;
    min-height: 1em;
    background-color: currentColor;
    opacity: .25;
}

.l-page-main {
    display: block;
    height: 100%;
}

.card-price > .row {
    min-height: 220px;
}

.bg-dark {
    color: var(--text-oncolor);
    background-color: var(--bg-level4) !important;
    font-weight: normal;
    padding: 0.25rem 0.25rem;
}

/* MEDIA QUERIES */
@media (min-width: 991px) and (max-width: 1279px) {
    .skyscraper{
        display: none;
    }
    .skyscraper-view{
        flex: 0 0 75%;
        max-width: 75%
    }
}

@media (min-width: 768px) and (max-width: 990px) {
    h1 {
        font-size: 20px;
        font-weight: bold;
        margin-bottom: 0;
        margin-left: 15px;
    }
    .skyscraper {
        display: none;
    }
}

@media (max-width: 767px) {
    .skyscraper {
        display: none;
    }

    .card-img-top {
        overflow: hidden;
        height: 100%;
        object-fit: cover;
    }
    h1 {
        font-size: 16px;
        padding-left: 15px;
        font-weight: bold;
        margin-bottom: 0;
    }

    .card-title {
        line-height: 130%;
    }

    .bookerdesktoplist {
        display: none;
    }

    #modal-filters .modal-dialog {
        margin: 0;
    }
}

.modal-header-redi {
    justify-content: initial !important;
    padding: 0.5rem 1rem;
    position: -webkit-sticky;
    position: sticky;
    top: 0;
}
.modal-header-redi button {
    align-self: center !important;
}
.modal-header-redi p {
    width: 100%;
    text-align: center;
    margin-left: -22px;
}
.modal-header-redi .close {
    padding: 0rem 1rem;
    margin: 0;
    margin-left: -17px;
}
.modal-header-redi .d-flex a {
    display: flex !important;
}
.a-filter-class {
    display: flex !important;
    text-decoration: none !important;
}

.title-filter_results {
    font-size: 20px;
    font-weight: 600;
}

.title_range {
    font-size: 18px;
    font-weight: 500;
}

.title-filter-desk {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-main);

    span {
        color: var(--text-strong);
        font-size: 1.125rem;
    }
}

.title-filter {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-secondary);

    span {
        color: var(--text-strong);
        font-size: 1.125rem;
    }
}
// BADGE
.badge {
    font-weight: 400;
}

.collapse_filter {
    &.hidden-options {
        :nth-child(n + 5) {
            display: none;
        }
    }
}
.form-check {
    display: flex;
    justify-content: space-between;
    align-items: center;

    &__count {
        font: var(--body-sm);
        color: var(--text-subtle);
    }
    >label {
        font-size: 0.875rem;
    }
}
.form-check-label {
    .font-icons {
        margin-top: -4px;
        display: block;
    }
}

.sliderLoader {
    position: absolute;
    width: 100%;
    height: 100%;
    top: -15px;
    z-index: 1;
}

.card-content-img-alerts {
    position: absolute;
    top: 0;
}

.top-promotions {
    top: 32px !important;
}
.top-meal-plan {
    top: 40px !important;
}
.hotel-image, .dynamic {
    background-color: var(--bg-level1);
}

.card-price .slick-slider {
    height: 100%;

    .slick-track {
        height: 100%;
    }
    //CLASE PARA QUE EL BORDER TOP SOLO SE VEA EN MOBILE, NO DEBERIA VERSE EN OTRAS RESOLUCIONES//
}

.slick-slide {
    height: 215px;
}

// HOTEL TAG en MAPA
.speech-bubble {
    position: relative;
    padding: 0.25rem 0.5rem;
    min-width: 5em;
    display: flex;
    align-items: center;
    gap: 4px;
    background: var(--bg-base);
    border: 1px solid var(--border-secondary);
    border-radius: 4px;
    text-align: center;
    font-size: 14px;
    color: var(--text-secondary);
    cursor: pointer;

    i.icons-heart {
        font-size: 24px;
        color: var(--text-primary);
        -webkit-text-stroke: 2px var(--border-disabled);
    }
    
    &:after {
        content: '';
        position: absolute;
        left: calc(50% - 4px);
        top: -41px;
        width: 10px;
        height: 10px;
        margin-top: 35px;
        overflow: hidden;
        transform: rotate(45deg);
        background: var(--bg-base);
        border-left: 1px solid var(--border-secondary);
        border-top: 1px solid var(--border-secondary);
    }
    &:hover {
        background: var(--bg-secondary-level1);
        color: var(--text-secondary-strong);
        z-index: 10;
        cursor: pointer;

        &:after {
            background: var(--bg-secondary-level1);
        }
    }
    
    &.speech-bubble-active {
        background: var(--bg-secondary-hover);
        border-color: var(--border-secondary-subtle);
        color: var(--text-oncolor);
        z-index: 10;
        &:after {
            background: var(--bg-secondary-hover);
            border-color: var(--border-secondary-subtle);
        }
    }
}


@media (min-width: 768px) {
    .card-footer {
        border-top: none !important;
    }
}

.handle_area {
    z-index: 1;
    position: absolute;
    height: 100%;
    width: 50px;

    &.left {
        left: 0;
    }

    &.right {
        right: 0;
    }
}


.recommended-wrapper {
    overflow-x: scroll;
    overflow-y: hidden;
    white-space: nowrap;

    .recommended-div {
        width: 250px;
        display: inline-block;
        margin-right: 10px;
    }
}

.loading-filters {
    height: 22px;

    .loader__circle {
        width: 27px;
        height: 27px;
    }
}

/*** Sliders ***/

.slider.slider-horizontal {
    width: 100%;
}

.slider .slider-handle {
    background: var(--bg-base);
    border: 1px solid var(--border-subtle);
}

.slider-handle {
    height: 30px;
    width: 30px;
}

.slider-selection {
    background: var(--bg-primary);
}

.slider.slider-horizontal .slider-track {
    margin-top: 2px;
    height: 4px;
}



.dolar-input-abs {
    left: 5px;
    top: 5px;
    user-select: none;
}
.dolar-input-abs-mobile {
    left: 5px;
    top: 7px;
    user-select: none;
}

.label-input-abs {
    left: 10px;
    top: -10px;
    font-size: 12px;
    background: var(--bg-base);
    user-select: none;
    color: var(--text-subtle);
}

.order_by_menu_mobile_area {

    .show {
        display: flex !important;
        align-items:  flex-end !important;

    }

    .modal-dialog {
        width: 100%;
        margin-bottom: 0 !important;
        
    }

    .modal.fade .modal-dialog {
        transition: none !important;
        transform: none !important;
    }

    .fade {
        transition: none !important;
    }
}

.order_by_menu_mobile {
    max-width: 570px;
    width: 90%;
    background-color: var(--bg-base);
}

.link-text {
    color: var(--text-link);
    &:hover {
        color: var(--text-link-hover);
    }
}

.min_text-14 {
    font-size: 14px !important;
}
.min_text-12 {
    font-size: 12px !important;
}

.min_text-16 {
    font-size: 16px !important;
}

.min_text-18 {
    font-size: 18px !important;
}

.c-pointer {
    cursor: pointer;
}

.list .card-header {
    @media (min-width: 992px) {
        background-color: var(--bg-level1);
    }
}

.rounded-pill-bg {
    background-color:var(--bg-level1);
    border: 1px solid var(--border-strong);
    color: var(--text-strong);
    font-size: 14px;
    user-select: none;

    span {
        color: var(--text-strong);
    }
}

#modal-filters  {

    .modal-dialog {
        width: 100%;
        max-width: 100%;
        margin: 0 auto;
    }

    .modal-body {
        display: flex;
        justify-content: center;
    }

    .content-fil {
        width: 510px;
    }
}

.box_range_input {
    max-width: auto;
    @media screen and (min-width: 590px) {
        max-width: 120px
    }
}
.btn-close {
    color: var(--text-strong);
    font-size: 1.5rem;
    background: transparent;
    border: none;
}
#order_by {
    border: 1px solid var(--border-subtle);
    display: flex;
    align-items: center;
    gap: 0.75rem;

    &.active {
        border-radius: 0.25rem 0.25rem 0 0;
        box-shadow: var(--shadow-200);
    }
}
.dropdown--menu {
    position: absolute;
    z-index: 2;
    top: 100%;
    background-color: var(--bg-base);
    border: 1px solid var(--border-subtle);
    border-top: none;
    margin: 0;
    padding: 0;
    border-radius: 0 0 6px 6px;
    box-shadow: var(--shadow-200);
    animation: growDown 300ms ease-in-out forwards;
    transform-origin: top center;

    .dropdown__button {
        padding: 0.5rem 1rem;
        border: none;
        background: transparent;
        width: 100%;
        color: var(--text-strong);
        font: var(--body-sm);
        text-align: left;

        &:hover {
            background-color: var(--bg-level2);
            cursor: pointer;
        }
    }
}

.RAPDBanner {
    width: 100%;
    padding: 1.25rem;
    display: flex;
    align-items: flex-start;
    gap: 8px;
    border: 1px solid var(--border-subtle);
    border-left: 5px solid var(--border-primary);
    border-radius: 0 16px 16px 0;
}

.list {
    .card {
        border-radius: 12px;

        .card-header {
            border-radius: 12px 12px 0 0;
        }
    }
}

:root {
    --marker-cluster-bg: #E5007D;
    --marker-cluster-text: #ffffff;
}

.CardContainer .close {
    padding: 0.5rem;
    font-size: 1.5rem;
    /*position: absolute;*/
    top: -7px !important;
    right: -5px !important;
    z-index: 10;
    background-color: var(--bg-base);
    border-radius: 50%;
    box-shadow: var(--shadow-300);
    transition: opacity 150ms ease-out;
}

/*--------------------------------------- TAG EXTERNO DE TELÉFONO ---------------------------------------*/
// Tag pegado externamente a la card con tooltip
.phone-external-tag {
    position: absolute;
    top: -8px;
    right: 15px;
    width: 40px;
    height: 40px;
    background: var(--bg-primary);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    box-shadow: var(--shadow-300);
    transition: all 0.3s ease;
    z-index: 10;
    border: 3px solid var(--bg-base);

    i {
        color: var(--text-oncolor);
        font-size: 1.1rem;
        transition: transform 0.3s ease;
    }

    &:hover {
        transform: scale(1.15);
        background: var(--bg-primary-hover);
        box-shadow: var(--shadow-400);

        i {
            transform: rotate(15deg) scale(1.1);
        }

        .phone-tooltip-modal {
            opacity: 1;
            visibility: visible;
            transform: translateY(0) scale(1);
        }
    }
}

.phone-tooltip-modal {
    position: absolute;
    top: 100%;
    right: -10px;
    background: var(--bg-base);
    border-radius: 16px;
    box-shadow: var(--shadow-500);
    min-width: 300px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-15px) scale(0.9);
    transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    margin-top: 12px;
    border: 1px solid var(--border-subtle);
    overflow: hidden;
    z-index: 20;

    // Flecha apuntando hacia arriba
    &::before {
        content: '';
        position: absolute;
        top: -10px;
        right: 25px;
        width: 0;
        height: 0;
        border-left: 10px solid transparent;
        border-right: 10px solid transparent;
        border-bottom: 10px solid var(--bg-base);
        filter: drop-shadow(0 -3px 3px var(--shadow-200));
    }
}

.tooltip-content {
    padding: 24px;
    text-align: center;

    h4 {
        margin: 0 0 12px 0;
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-strong);
        line-height: 1.3;
    }

    p {
        margin: 0 0 20px 0;
        font-size: 0.95rem;
        color: var(--text-secondary);
        line-height: 1.4;
    }
}

.call-button {
    display: inline-flex;
    align-items: center;
    gap: 10px;
    color: var(--text-oncolor);
    background: var(--bg-secondary);
    text-decoration: none;
    font-weight: 600;
    font-size: 1rem;
    padding: 14px 24px;
    border-radius: 12px;
    transition: all 0.3s ease;
    box-shadow: var(--shadow-300);
    width: 100%;
    justify-content: center;

    i {
        font-size: 1.1rem;
        color: var(--text-oncolor);
    }

    &:hover {
        transform: translateY(-2px);
        box-shadow: var(--shadow-400);
        text-decoration: none;
        color: var(--text-oncolor);
        background: var(--bg-secondary-hover);
    }
}

// Responsive adjustments
@media (max-width: 768px) {
    .phone-external-tag {
        width: 36px;
        height: 36px;
        top: -6px;
        right: 12px;
        border: 2px solid white;

        i {
            font-size: 1rem;
        }
    }

    .phone-tooltip-modal {
        min-width: 280px;
        right: -20px;

        &::before {
            right: 35px;
        }
    }

    .tooltip-content {
        padding: 20px;

        h4 {
            font-size: 1rem;
        }

        p {
            font-size: 0.9rem;
            margin-bottom: 16px;
        }
    }

    .call-button {
        font-size: 0.95rem;
        padding: 12px 20px;
    }
}
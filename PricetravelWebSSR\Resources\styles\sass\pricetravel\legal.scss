@import 'components/fonts.scss',
'./app',
'components/header.scss',
'components/icons_font',
'components/layouts.scss';

/* --------------- BASE --------------- */
html {
    scrollbar-width: thin;
    scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
}

body {
    display: flex;
    flex-direction: column;
    min-height: 100vh;

    main {
        flex-grow: 1;
        background-color: var(--bg-level1);
    }
}

h1 {
    margin: 28px 0;
    font: var(--title-xl);
    color: var(--text-main);
}

i.icons-chevron-down {
    transition: transform 300ms ease-in-out;
    &.rotate {
        transform: rotate(180deg);
    }
}
/* --------------- BASE --------------- */

/* --------------- LAYOUT --------------- */
.legalgrid {
    margin: 28px 0;
    list-style: none;
    padding-left: 0;
    display: grid;
    grid-template-columns: 1fr;
    gap: 28px;

    @media (992px <= width) {
        grid-template-columns: repeat(3, 1fr);
    }
}

.legalcontent {
    margin-top: 32px;
    section, article {
        scroll-margin-top: 68px;
    }
    #sign {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }

    #sign-client {
        width: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }

    h2,
    h3,
    h4,
    h5,
    h6 {
        color: var(--text-main);
    }

    h2 {
        margin: 36px 0 16px;
        font: var(--title-sm);
    }

    h3 {
        margin: 16px 0 8px;
        font: var(--title-xs);

        i {
            margin-right: 16px;
        }
    }

    h4,
    h5,
    h6 {
        margin: 16px 0 8px;
        font: var(--title-xxs);
        
        i {
            margin-right: 16px;
        }
    }

    label {
        margin-bottom: 4px;
        font: var(--tight-bold);
        color: var(--text-subtle);
    }

    input[type="text"] {
        width: 100%;
        height: 52px;
        padding: 16px;
        border: 1px solid var(--border-strong);
        border-radius: 4px;
        color: var(--text-strong);
        
        &::placeholder {
            color: var(--text-subtle)
        }
    }

    a {
        @extend .link;
        font-size: inherit;
    }

    dl {
        padding-left: 20px;

        dt {
            position: relative;
            
            &::before {
                content: "";
                position: absolute;
                left: -16px;
                top: 50%;
                transform: translateY(-50%);
                width: 6px;
                height: 6px;
                background-color: currentColor;
                border-radius: 50%;
            }
        }
    }
    ul {
        padding-left: 20px;
        li {
            list-style: disc;
            li {
                list-style: circle;
            }
        }
    }
    ol {
        padding-left: 20px;
        li:has(> h3)::marker {
            font: var(--title-sm);
            color: currentColor;
        }
        li:has(> h4,> h5,> h6)::marker {
            font: var(--title-xs);
            color: currentColor;
        }
    }
}

.legallayout--noMenu {
    overflow-x: hidden;

    .legalcontent {
        margin: 0 auto;
        max-width: 900px;
    }
}

// MEDIA QUERIES
@media (992px <=width) {
    .legallayout {
        display: grid;
        grid-template-columns: 1fr 300px;
        align-items: start;
        column-gap: 60px;

        .legalcontent {
            margin-top: 0;
            grid-column: 1 / 2;
            grid-row: 1 / 2;

            section, article {
                scroll-margin-top: 0;
            }

            h1 {
                margin: 36px 0;
                font: var(--display-xxs);
            }
    
            h2 {
                font: var(--title-md);
            }
    
            h3 {
                font: var(--title-sm);
            }
    
            h4,
            h5,
            h6 {
                font: var(--title-xs);
            }

            ol {
                li:has(> h3)::marker {
                    font: var(--title-xs);
                }
                li:has(> h4,> h5,> h6)::marker {
                    font: var(--title-xxs);
                }
            }
        }
        .legalindice {
            grid-column: 2 / 3;
            grid-row: 1 / 2;
        }
        
        &--noMenu {
            grid-template-columns: 1fr;
        }
    }
}
/* --------------- LAYOUT --------------- */

/* --------------- COMPONENTS --------------- */
.breadcrumbs {
    list-style: none;
    padding: 0;
    display: flex;
    align-items: baseline;
    flex-wrap: wrap;
    column-gap: 8px;
    font-size: 0.875rem;
    color: var(--text-subtle);

    li {
        display: none;
        align-items: center;
        gap: 4px;

        &::after {
            font-family: 'PTH-icon';
            content: "\e904";
            font-size: 1rem;
        }

        &:last-of-type {
            &::after {
                content: "";
            }
        }

        &:nth-last-child(-n+2) {
            display: flex;
        }
    }

    @media (992px <=width) {
        li {
            display: flex;
        }
    }
}

.tab {
    flex-shrink: 0;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 8px;
    font: var(--body-sm-bold);
    color: var(--text-strong);
    border-bottom: 3px solid transparent;
    transition: color 300ms ease-in-out;
    position: relative;


    &:before {
        position: absolute;
        bottom: -3px;
        left: 0;
        display: block;
        height: 3px;
        width: 100%;
        transform: scaleX(0);
        transform-origin: left;
        content: "";
        background-color:var(--border-strong);
        transition: transform 350ms ease-in;
    }

    &:hover {
        &:before {
            transform: scaleX(1);
        }
    }

    &.active {
        color: var(--text-main);
        &:before {
            background-color:var(--border-primary);
            transform: scaleX(1);
        }
    }
}

.legalcard {
    padding: 28px;
    width: 100%;
    height: 100%;
    display: block;
    background-color: var(--bg-base);
    border: 1px solid var(--border-subtle);
    border-radius: 16px;
    transition: box-shadow 300ms ease-out;

    i, img {
        display: flex;
        font-size: 2.25rem;
        color: var(--text-primary);
        transition: transform 300ms ease-out;
    }

    h2 {
        margin: 16px 0;
        font-size: 1.125rem;
        transition: transform 300ms ease-out;
    }

    p {
        margin: 0;
    }

    &:hover {
        box-shadow: var(--shadow-300);
        h2, i, img {
            transform: translateX(8px);
        }
    }
}

.info-notification {
    width: 100%;
    margin-bottom: 0;
    padding: 16px;
    display: flex;
    align-items: flex-start;
    gap: 16px;
    background-color: var(--bg-base);
    border: 1px solid var(--border-subtle);
    border-left: 5px solid var(--border-info);
    border-radius: 4px;
    font-size: 0.875rem;

    i {
        font-size: 24px;
        color: var(--text-info);
    }

    a {
        @extend .link;
        font-size: inherit;
        white-space: normal;
        display: inline;
        word-break: break-word;
    }
}

.legalindice {
    position: sticky;
    top: 4px;
    z-index: 10;
    background: var(--bg-base);
    border-radius: 8px;
    border: 1px solid var(--border-subtle);
    font-size: 0.875rem;
    box-shadow: var(--shadow-100);
    transition: box-shadow 300ms ease-out;
    width: 100%;

    &--open {
        max-height: calc(100vh - 8px);
        box-shadow: var(--shadow-300);
    }

    &__btn {
        width: 100%;
        padding: 16px;
        display: flex;
        align-items: stretch;
        justify-content: space-between;
        gap: 4px;
        font-weight: 500;

        h2 {
            margin: 0;
            font: var(--body-sm-bold);
        }

        i {
            position: absolute;
            top: 50%;
            right: 0;
            transform: translateY(-50%);
            transition: all 600ms ease-out;

            &.disappear {
                opacity: 0;
                transform: translateY(-50%) scale(0);
            }
        }
    }

    &__menu {
        @extend .list-unstyled;
        margin: 0;
        max-height: 0px;
        opacity: 0;
        transition: max-height 300ms ease-out, opacity 300ms ease-out;
        overflow-y: auto;

        &--open {
            max-height: 80vh;
            max-height: calc(100vh - 8px - 53px);
            border-top: 1px solid var(--border-subtle);
            opacity: 1;
        }

        li {
            a {
                width: 100%;
                padding: 16px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                border-left: 3px solid transparent;
                transition: all 300ms ease-out;

                &.active {
                    border-color: var(--border-strong-hover);
                    font-weight: 500;
                    color: var(--text-main);
                }
            }
        }
    }

    &__submenu {
        @extend .list-unstyled;
        margin: 0;

        li {
            a {
                padding-left: 28px;
            }
        }
    }

    @media (992px <=width) {
        position: sticky; 
        top: 16px;
        background: transparent;
        border: none;
        box-shadow: none;

        h2 {
            margin: 0;
            font: var(--body-xs-bold);
            color: var(--text-subtle);
        }

        &__menu {
            max-height: calc(100vh - 32px - 18px);
            border-left: 1px solid var(--border-subtle);
            opacity: 1;
        }
    }
}
/* --------------- COMPONENTS --------------- */
﻿<template>
    <div id="legal" class="container py-32">
        <!-- Breadcrumb --->
        <breadcrumb />

        <!-- Main Title --->
        <h1>{{ getPageTitle }} </h1>
        
        <hr class="my-28 my-lg-36">

        <div class="legalcontent" v-if="getContentSelected != ''" v-html="getContentSelected"></div>
        <div v-else>
            <img :width="product === 'tiquetesbaratos' ? '150' : '300'" :src="`${cdn}${img}`" class="d-block mr-auto ml-auto mb-3">
            <h3 class="d-center mb-5"> 404 </h3>
        </div>
    </div>
</template>
<script>
    import { storeToRefs } from 'pinia';
    import { useLegalStore } from '../../stores/legal';
    
    export default {
        props: {
            content: null,
            product: null,
            img: null,
            cdn: null,
        },
        data() {
            const legalType = window.__pt.legalType.replace(/-/g, "_");
            const appName = window.__pt.settings.site.appName;
            
            return {
                legalType,
                appName
            }
        },
        computed: {
            getPageTitle() {
                return `${this.__(`information.${this.legalType}_title`, [ this.appName ])}`;
            },
        },
        setup() {
            const useLegal = useLegalStore();
            const { setResponse, changeLanguage, setContentSelectedInitial } = useLegal;
            const { isStaticContent, getContentSelected } = storeToRefs(useLegal);
            return { setResponse, isStaticContent, changeLanguage, getContentSelected, setContentSelectedInitial }
        },
        mounted() {
            this.setResponse(this.content);
            this.setContentSelectedInitial();
        },
    }
</script>
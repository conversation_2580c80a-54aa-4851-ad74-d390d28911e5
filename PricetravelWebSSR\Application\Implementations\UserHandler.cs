﻿using Amazon.DynamoDBv2.DataModel;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Infrastructure.DatabaseService.DynamoDB;
using PricetravelWebSSR.Models.User.Reservation;
using PricetravelWebSSR.Models.Response;
using PricetravelWebSSR.Models.Request;
using PricetravelWebSSR.Application.Mappers;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.HotelFacade;
using PricetravelWebSSR.Models.Places;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Application.Implementations
{
    public class UserHandler(IOptions<SettingsOptions> settingsOptions, DynamoService dynamoDBContext, ILoginServices accountService, IBookingServices bookingServices, IHotelFacadeHandler hotelFacadeHandler, ICommonHandler commonHandler, ICacheService cache, IPlaceHandler placeHandler) : IUserHandler
    {
        private readonly SettingsOptions _settingsOptions = settingsOptions.Value;
        private readonly DynamoDBContext _dynamoDBContext = dynamoDBContext.DynamoDBContext();
        private readonly ILoginServices _accountServices = accountService;
        private readonly IBookingServices _bookingServices = bookingServices;
        private readonly IHotelFacadeHandler _hotelFacadeHandler = hotelFacadeHandler;
        private readonly ICommonHandler _commonHandler = commonHandler;
        private readonly ICacheService _cacheService = cache;
        private readonly IPlaceHandler _placeHandler = placeHandler;


        public async Task<Reservation> QueryAsync(Reservation reservation, CancellationToken ct)
        {
            var user = await _accountServices.GetUser();

            if (user is not null)
            {
                user.UserProfile.Reservations ??= [];

                if (!user.UserProfile.Reservations.Any(r => r.ReservationId == reservation.ReservationId))
                {
                    var email = await _accountServices.EncryptValueAsync(reservation.Email);
                    user.UserProfile.Reservations.Add(new Reservation
                    {
                        ReservationId = reservation.ReservationId,
                        Email = email
                    });

                    await _accountServices.UpdateProfile(user.UserProfile);
                }

            }

            return reservation;
        }


        //metodo para el mapeo y consulta de las reservaciones
        public async Task<List<ProductReservation>> QueryAsync(ProductReservation request, CancellationToken ct)
        {
            var user = await _accountServices.GetUser();
            var reservations = new List<ProductReservation>();
            var itineraryTasks = new List<Task<ItineraryResponse>>();
            var hotelContentTasks = new List<Task<ContentHotelResponse>>();
            var placeContentTasks = new List<Task<List<PlaceResponse>>>();


            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, ct);


            if (user is null)
            {
                return reservations;
            }

            var userKey = $"{_settingsOptions.SiteName}_{user.Id}";


            reservations = await _cacheService.GetCache<List<ProductReservation>>(userKey, ct);


            if (reservations is not null && !request.Cache)
            {
                return reservations;
            }

            /**
             * Llamar itinerarios
             */
            foreach (var item in user.UserProfile.Reservations)
            {
                if (!string.IsNullOrEmpty(item.Email))
                {
                    var email = await _accountServices.DecryptValueAsync(item.Email);
                    if (!string.IsNullOrEmpty(email))
                    {
                        var task = _bookingServices.QueryAsync(new ItineraryRequest { Email = email, Id = item.ReservationId }, ct);
                        itineraryTasks.Add(task);
                    }
                    
                }
            }

            await Task.WhenAll(itineraryTasks);

            var aggregatedItineraryResponse = itineraryTasks.Select(t => t.Result.Data).Where(i => i is not null).ToList();


            /**
             * Llamar contenido de hotel (si aplica)
             */
            var hotelsId = UserMapper.GetHotels(aggregatedItineraryResponse);
            var airportsCode = UserMapper.GetAirports(aggregatedItineraryResponse);


            foreach (var item in hotelsId)
            {
                var task = _hotelFacadeHandler.QueryAsync(new ContentHotelRequest { HotelId = item, Culture = string.IsNullOrEmpty(request.Culture) ? userSelection.Culture.InternalCultureCode : request.Culture }, ct);
                hotelContentTasks.Add(task);
            }

            foreach (var item in airportsCode)
            {
                var task = _placeHandler.QueryAsync(new PlaceRequest { OriginCode = item, Culture = string.IsNullOrEmpty(request.Culture) ? userSelection.Culture.InternalCultureCode : request.Culture }, ct);
                placeContentTasks.Add(task);
            }


            await Task.WhenAll(hotelContentTasks);
            await Task.WhenAll(placeContentTasks);


            var aggregatedHotelResponse = hotelContentTasks.Select(t => t.Result).ToList();
            var aggregatedPlaceResponse = placeContentTasks.Select(t => t.Result.FirstOrDefault()).Where( r => r != null).ToList();



            reservations = UserMapper.Map(aggregatedItineraryResponse, aggregatedHotelResponse, aggregatedPlaceResponse);


            if (reservations.Count > 0)
            {
                _cacheService.SetCache(userKey, reservations);
            }

            return reservations;
        }

        public async Task<UserProfile> QueryAsync(UserProfile request, CancellationToken ct)
        {
            var user = await _accountServices.GetUser();

            if (user == null)
            {
                return new UserProfile();

            }
            return user.UserProfile;
        }

        public async Task<Reservation> QueryAsync(ItineraryRequest request, CancellationToken ct)
        {
            var itinerary = await _bookingServices.QueryAsync(new ItineraryRequest { Email = request.Email, Id = request.Id }, ct);
            var response = new Reservation();

            if (itinerary.Data != null)
            {
                response.Email = request.Email;
                response.ReservationId = request.Id;
                response.Status = true;

                response = await this.QueryAsync(response, ct);
            }

            return response;
        }

        public Task<ApiResponse<bool>> UpdateProfile(UserProfile userProfile)
        {
            throw new NotImplementedException();
        }

    }
}

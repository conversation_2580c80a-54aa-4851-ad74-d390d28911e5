﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options
@using System.Text.Json
@using PricetravelWebSSR.Models.Collection
@using PricetravelWebSSR.Types
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.Request

@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CurrencyOptions> currencyOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper
@{
    var resolution = viewHelper.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = viewHelper.IsRobot();
    var mobile = resolution.Device == DeviceType.Mobile;
    var metaTag = ViewData["MetaTag"] as MetaTag;
    var cultureConf = ViewData["cultureData"] as Culture;
    var seoContent = ViewData["seoContent"] as SeoResponse;
    var target = ViewData["Target"] as string;
    var page = ViewData["PageRoot"] as string;
    ViewData["PageRoot"] = page;
    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;
    ViewData["cultureData"] = cultureConf;
    ViewData["IsList"] = true;

    }
@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot } })


<div class="container c-pago py-3 pt-md-5" ng-controller="controller as vm" ng-cloak>
    <div class="row">
        <div class="col-12 col-md-6 d-none d-md-block">
            <img class="w-100 h-100 rounded img-cover" src="/assets/img/tiquetesbaratos/img-pago-en-linea.jpg" />
        </div>
        <div class="col-12 col-md-6">
            <div id="app-main">
                <payment :page-title="'@Html.Raw(viewHelper.Localizer("payment-title_" + settingOptions.Value.SiteName))'"></payment>
            </div>
            <p class="mt-4 mb-2"></p>
            <div class="col-12 border px-md-3 px-4 px-lg-3 py-3 cf-pay">

                <div class="col-12 mb-4">
                    <div class="text-center font-18">
                        <span class="fw-bold">@viewHelper.Localizer("secure-payments")</span>
                    </div>
                </div>
                @if (settingOptions.Value.SiteName == "pricetravel")
                {
                    <div class="PaymentBanner__logos d-flex justify-content-center align-items-center flex-wrap">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-visa.svg" alt="visa card" width="50" height="16" src="https://3.cdnpt.com/images/bancos/logo-visa.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-amex.svg" alt="american express" width="26" height="26" src="https://3.cdnpt.com/images/bancos/logo-amex.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg" alt="mastercard" width="34" height="26" src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg">
                        <img bn-lazy-src="/assets/img/logos_banks/logo-bbva.svg" alt="Diners Club" width="50" height="15" src="/assets/img/logos_banks/logo-bbva.svg">
                        <img bn-lazy-src="/assets/img/logos_banks/logo-nubank.svg" alt="Pagos Seguros en Línea" width="47" height="26" src="/assets/img/logos_banks/logo-nubank.svg">
                        <img bn-lazy-src="/assets/img/logos_banks/logo-citibanamex.svg" alt="Efecty" width="78" height="12" src="/assets/img/logos_banks/logo-citibanamex.svg">
                        <img bn-lazy-src="/assets/img/logos_banks/logo-scotiabank.svg" alt="Bancolombia" width="78" height="14" src="/assets/img/logos_banks/logo-scotiabank.svg">
                        <img bn-lazy-src="/assets/img/logos_banks/logo-paypal.svg" alt="Daviplata" width="78" height="26" src="/assets/img/logos_banks/logo-paypal.svg">
                    </div>
                }
                else if (settingOptions.Value.SiteName == "tiquetesbaratos")
                {
                    <div class="PaymentBanner__logos d-flex justify-content-center align-items-center flex-wrap">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-visa.svg" alt="visa card" width="50" height="16" src="https://3.cdnpt.com/images/bancos/logo-visa.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-amex.svg" alt="american express" width="26" height="26" src="https://3.cdnpt.com/images/bancos/logo-amex.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg" alt="mastercard" width="34" height="26" src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-dinersclub.svg" alt="Diners Club" width="78" height="20" src="https://3.cdnpt.com/images/bancos/logo-dinersclub.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-pse.svg" alt="Pagos Seguros en Línea" width="26" height="26" src="https://3.cdnpt.com/images/bancos/logo-pse.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-bancolombia.png" alt="Bancolombia" width="83" height="26" src="https://3.cdnpt.com/images/bancos/logo-bancolombia.png">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-efecty.svg" alt="Efecty" width="26" height="26" src="https://3.cdnpt.com/images/bancos/logo-efecty.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-daviplata.svg" alt="Daviplata" width="70" height="26" src="https://3.cdnpt.com/images/bancos/logo-daviplata.svg">
                        <img bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-nequi.svg" alt="Nequi" width="70" height="26" src="https://3.cdnpt.com/images/bancos/logo-nequi.svg">
                    </div>
                }
                <div class="text-center font-18 mt-3">
                    <div class="text-center font-18 mt-3">
                        <button type="button"
                                class="btn-Tertiary mt-3"
                                data-bs-toggle="modal"
                                data-bs-target="#modal-payform"
                                ng-click="vm.onLoadPayment('modal-payform', '@viewHelper.Localizer("payment_title")', '@viewHelper.Localizer("payment_button")')">
                            @viewHelper.Localizer("check-methods-pay")
                            <i class="icons-angle-right font-20 d-flex"></i>
                        </button>
                    </div>
                </div>
            </div>
            <small class="lh-sm d-block mt-2">@viewHelper.Localizer("payment-option-depend_" + settingOptions.Value.SiteName)</small>
            <form id="AntiForgeryToken">
                @Html.AntiForgeryToken()
            </form>
        </div>
        <div class="PaymentBanner__head">
            @if (settingOptions.Value.PaymentMethodActive)
            {
                @await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/Modals/Payment.cshtml", new ViewDataDictionary(ViewData){  { "Mobile", isMobile },{ "Target", "modal-payform" },{ "page", "home" } })
            }
        </div>
    </div>

</div>
@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "login", isRobot } })

@section Preload {
    <link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/home.css")" as="style" />
}
@section Css {
    <link type="text/css" rel="stylesheet"
          href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/home.css")">
    <link rel="stylesheet" href="https://stackpath.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css" />
}
@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
}
@section Scripts {
    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_destination.min.js")"></script>
    <script src="https://www.google.com/recaptcha/api.js?render=explicit&hl=@cultureConf.CultureCode" async defer></script>
    <script src="@staticHelper.GetVersion($"/assets/js/vue/lang/{cultureConf.CultureCode}.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/controllers/hotel-controllers.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/vue/app.js")"></script>
    <script src="https://code.jquery.com/jquery-3.5.1.slim.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.5.2/dist/js/bootstrap.bundle.min.js"></script>
}
<style>
    .payonline-container {
        margin-top: 2rem;
        margin-bottom: 2rem;
    }

    .payonline__title {
        font-size: 1.75rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .payonline__subtitle {
        font-size: 1rem;
        margin-bottom: 1.5rem;
        color: #555;
    }

    .payonline__btn-consultar {
        width: 100%;
    }

    @@media (min-width: 992px) {
        .payonline__btn-consultar {
            width: auto;
            margin-left: auto;
            display: inline-block;
        }
    }

    .g-recaptcha-grupos {
        margin-bottom: 0.5rem;
    }

    .g-recaptcha {
        border: 1px solid transparent;
        padding: 0.25rem;
        border-radius: 4px;
    }

    .g-recaptcha.border-error {
        border-color: #dc3545;
    }

    .invalid-feedback {
        display: block;
    }

    .loading-page {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(255, 255, 255, 0.4);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .loader__logo {
        width: 64px;
        height: 64px;
    }

    .loader__logo::before {
        content: "";
        display: inline-block;
        width: 64px;
        height: 64px;
        background-image: url('/assets-tb/img/tiquetesbaratos/loader-logo.svg');
        background-repeat: no-repeat;
        background-size: 100%;
        animation: rotatepulse 2s linear infinite;
    }

    @@keyframes rotatepulse {
        0% {
            transform: rotate(0) scale(1);
        }

        50% {
            transform: rotate(400deg) scale(0.6);
        }

        100% {
            transform: rotate(1080deg) scale(1);
        }
    }

    .mobile-button {
        padding: 10px 20px;
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        border: 1px solid #2196f3;
        box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.5);
        z-index: 500;
    }

    .btn-blue {
        background: #2196f3 !important;
        border: none;
        border-radius: 4px;
        color: #fff !important;
        font-family: Roboto-Medium;
        transition: all 0.2s linear;
    }

    .btn-blue:hover {
        background: #2196f3 !important;
        border: none !important;
        border-radius: 4px !important;
        color: #fff !important;
        font-family: Roboto-Medium !important;
        transition: all 0.2s linear !important;
    }

    .lh-sm {
        line-height: 1.5 !important;
    }

    .a-link, .color-link {
        color: #186bdf;
    }

    .font-16 {
        font-size: 16px !important;
    }

    .font-18 {
        font-size: 18px !important;
    }

    .text-center {
        text-align: center !important;
    }

    .fw-bold, b, strong {
        font-weight: 700 !important;
    }

    .PaymentBanner__logos {
        align-items: center;
        display: flex;
        flex-wrap: wrap;
        gap: 2rem;
        justify-content: center;
        row-gap: 1rem;
    }

    .border {
        border: 1px solid #dee2e6 !important;
    }

    .cf-pay {
        border-radius: 16px;
    }

    .PaymentBanner__logos img {
        flex: 0 0 auto;
    }

    @@media (min-width: 768px) {
        .img-cover {
            object-fit: cover;
            object-position: right;
        }
    }

    @@media (min-width: 768px) {
        .px-md-3 {
            padding-left: 1rem !important;
            padding-right: 1rem !important;
        }
    }

    .btn-Tertiary {
        padding: 1rem;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        border-radius: 8px;
        line-height: 1.2em;
        letter-spacing: 0.01em;
        transition: all 150ms ease-out;
        cursor: pointer;
        padding: 0 !important;
        background: transparent;
        color: black;
        font-size: 16px;
        font-style: normal !important;
    }

    .modal-dialog {
        max-width: 1000px;
        margin-left: auto;
        margin-right: auto;
    }

    @@media (max-width: 768px) {
        .modal-dialog {
            margin: 20px auto 0 auto;
            padding: 0 20px;
            max-width: 100%;
        }
    }
</style>
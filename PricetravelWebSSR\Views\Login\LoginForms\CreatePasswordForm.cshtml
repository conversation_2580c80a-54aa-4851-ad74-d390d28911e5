﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper

@{
    var culture = ViewData["cultureData"] as Culture;
}


<p class="label">@viewHelper.Localizer("enter_email2")</p>
<div class="d-flex g-16 mb-2">
    <p class="mb-0 font-500">{{ vm.userData.email }} </p>
    <button type="button" name="button" class="link"
            ng-click="vm.goToStepOne()">
        @viewHelper.Localizer("session_change_email")
    </button>
</div>
<!---- NOMBRE Y APELLIDO ----->
<div class="d-flex g-16 mb-16">
    <div class="w-100">
        <label for="name_login">@viewHelper.Localizer("session_name")</label>
        <div class="input-with-icon">
            <input type="text"
                    ng-attr-type="text"
                   required
                   minlength="2"
                   id="name_login"
                   ng-model="vm.userData.name"
                   name="name"
                   placeholder="@viewHelper.Localizer("session_name")"
                   ng-class="{'invalid': (form_login.$submitted || form_login.name.$dirty || form_login.name.$touched ) && (form_login.name.$error.minlength || form_login.name.$error.required)}" >
            <i class="font-24 icons-check-circle font-green" ng-show="form_login.name.$valid && !form_login.name.$pristine"></i>
            <p class="invalid-feedback-special" ng-show="(form_login.name.$dirty || form_login.name.$touched) && form_login.name.$invalid">
                @viewHelper.Localizer("session_name_error",@viewHelper.Localizer("session_name"))
            </p>
            <p class="invalid-feedback-special" ng-show="form_login.$submitted && form_login.name.$invalid && !form_login.name.$dirty">
                @viewHelper.Localizer("session_error_gener",@viewHelper.Localizer("session_name"))
            </p>
        </div>
    </div>
    <div class="w-100">
        <label for="lastName_login">@viewHelper.Localizer("session_last_name")</label>
        <div class="input-with-icon">
            <input type="text" ng-attr-type="text" required id="lastName_login" minlength="2" ng-model="vm.userData.lastname" name="lastName_login" placeholder="@viewHelper.Localizer("session_last_name")" class="login_input" ng-class="{'invalid': (form_login.$submitted || form_login.lastName_login.$dirty || form_login.lastName_login.$touched ) && (form_login.lastName_login.$error.minlength || form_login.lastName_login.$error.required)}">
            <i class="font-24 icons-check-circle font-green" ng-show="form_login.lastName_login.$valid && !form_login.lastName_login.$pristine"></i>
            <p class="invalid-feedback-special" ng-show="(form_login.lastName_login.$dirty || form_login.lastName_login.$touched) && form_login.lastName_login.$invalid">
                @viewHelper.Localizer("session_name_error",@viewHelper.Localizer("session_last_name"))
            </p>
            <p class="invalid-feedback-special" ng-show="form_login.$submitted && form_login.lastName_login.$invalid  && !form_login.lastName_login.$dirty">
                @viewHelper.Localizer("session_error_gener",@viewHelper.Localizer("session_last_name"))
            </p>
        </div>
    </div>
</div>

<!---- CONTRASEÑA ----->
<label for="password_create_login">@viewHelper.Localizer("enter_your_password")</label>
<div class="input-with-icon">
    <input ng-attr-type="{{ vm.eyeUser ? 'password' : 'text' }}" required ng-model="vm.userData.password" id="password_create_login" name="password" placeholder="@viewHelper.Localizer("enter_your_password")" ng-class="{'invalid': (form_login.$submitted || form_login.password.$dirty || form_login.password.$touched ) && form_login.password.$error.required}">
    <button type="button" ng-click="vm.eyeUser = !vm.eyeUser" ng-class="{'checked': vm.isPasswordValid(vm.userData.password) && form_login.$dirty}">
        <i ng-class="{'icons-visibility-off': vm.eyeUser, 'icons-visibility': !vm.eyeUser}" class="font-24"></i>
    </button>
    <i class="font-24 icons-check-circle font-green" ng-show="vm.isPasswordValid(vm.userData.password) && form_login.$dirty"></i>
    <p class="invalid-feedback-special" ng-show="(form_login.password.$dirty || form_login.password.$touched) && form_login.password.$invalid">
        @viewHelper.Localizer("session_password_error")
    </p>
    <p class="invalid-feedback-special" ng-show="form_login.$submitted && form_login.password.$invalid  && !form_login.name.$dirty">
        @viewHelper.Localizer("session_error_gener",@viewHelper.Localizer("session_error_password"))
    </p>
</div>
<!---- CONTRASEÑA INSTRUCCIONES ----->
<div class="login_passwordInstructions" ng-show="vm.userData.password.length > 0">
    <p class="font-subtle mb-2">@viewHelper.Localizer("session_password")</p>
    <ul>
        <li>
            <i class="font-24 icons-check-circle" ng-class="{ 'font-disabled' : vm.userData.password.length < 8, 'font-green' : vm.userData.password.length >= 8 }"></i>
            <p class="">@viewHelper.Localizer("session_almost_8")</p>
        </li>
        <li>
            <i class="font-24 icons-check-circle" ng-class="{ 'font-disabled' : !vm.containsUppercase(vm.userData.password), 'font-green' : vm.containsUppercase(vm.userData.password) }"></i>
            <p class="">@viewHelper.Localizer("session_almost_mayus")</p>
        </li>
        <li>
            <i class="font-24 icons-check-circle" ng-class="{ 'font-disabled' : !vm.containsNumber(vm.userData.password), 'font-green' : vm.containsNumber(vm.userData.password) }"></i>
            <p class="">@viewHelper.Localizer("session_almost_number")</p>
        </li>
    </ul>
</div>

<!---- CAPTCHA ----->
<fieldset class="captcha">
    <div class="g-recaptcha-login">
        <div id="g-recaptcha-login"></div>
        <input type="text" required style="z-index: -1;display: contents;" data-invalid="g_recaptcha_invalid" name="recatcha" id="recatchacall" ng-model="vm.userData.recatcha" class="form-control" required />
    </div>
</fieldset>

<!---- BOTON ----->
<button type="submit" name="button"
        class="btnPrimary w-100"
        ng-disabled="!form_login.$valid || (vm.userData.password.length < 8 || !vm.containsNumber(vm.userData.password) || !vm.containsUppercase(vm.userData.password))"
        >
    @viewHelper.Localizer("session_create_account")
</button>

<!---- CAPTCHA SCRIPT ----->
<script src="https://www.google.com/recaptcha/api.js?render=explicit&hl=@culture.CultureCode"></script>

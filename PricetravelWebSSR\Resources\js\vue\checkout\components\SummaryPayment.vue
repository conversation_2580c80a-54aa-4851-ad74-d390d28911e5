<template>

    <div>
        <SumaryMinify :summary="summary" :listrooms="listRooms" :source="source"></SumaryMinify>
        <PaymentOptions
            v-if="!rapd && isMsiAvailable && isCollectType != 2"
            :msiIsAvailible="msiIsAvailible" :mobile="false"/>
        <h2 class="p-4">{{ __('messages.summary_reservation') }}</h2>
        <summary-hotel :summary="summary" :summaryMobile="summaryMobile"></summary-hotel>

    </div>
</template>

<script>
import ResidentsColo from './ResidentsColo';
import { CookieService } from '../../services/CookieService';
import SumaryMinify from './SumaryMinify.vue';
import PaymentOptions from './PaymentOptions.vue';
const culture = window.__pt.cultureData;
export default {
    props: {
        summary: {
            type: Object,
            required: true
        },
        customclass: {
            type: String,
            default: ""
        },
        isMobile: {
            type: Boolean,
            default: false
        },
        summaryMobile: {
            type: Boolean,
            default: false
        },
        rapd: {
            type: Boolean,
            default: false
        },
        isMsiAvailable:
        {
            type: Boolean,
            default: false
        },
        isCollectType: {
            type: Number,
        },
        msiIsAvailible: {
            type: Array,
            default: () => []
        }
    },
    data() {
        return {
              culture,
            open: false,
            siteConfig: window.__pt.settings.site,
            source: "",
            totalExluded: 0,
            isMetaTotal: window.__pt.settings.site.isMetaTotal,
            currencySite: window.__pt.settings.site.currency,
            listRooms: []
        };
    },
    computed: {
        summaryClass() {
            return {
                'collapse show': this.summaryMobile && this.isMobile,
                'collapse': this.summaryMobile && !this.isMobile
            };
        },
        getSummaryClass() {
            return this.summaryMobile ? "collapseOne" : "";
        },

    },
    mounted() {
        this.getCookieSource();
        this.mergeRooms();
    },
    methods: {
        getCookieSource() {
            this.source = CookieService.getCookie("source_origin");
            this.totalExluded = this.summary.rate.breakdownExcluded.reduce((acc, item) => acc + item.amount, 0);
        },
        toggleSummary() {
            this.open = !this.open;
            this.isMobile = false;
        },
        mergeRooms() {
            const map = new Map();
            this.summary.ratesWithRooms.forEach((item) => {
                const key = `${item.room.name}${item.room.mealPlan}${item.rate.cancellation.isCancellationFree}`;
                if (map.has(key)) {
                    const existingItem = map.get(key);
                    existingItem.room.quantity += item.room.quantity;
                } else {
                    map.set(key, { ...item, room: { ...item.room, quantity: item.room.quantity } });
                }
            });
            this.listRooms = Array.from(map.values());
        }

    },
    components: {
        ResidentsColo,
        PaymentOptions
    }
};
</script>

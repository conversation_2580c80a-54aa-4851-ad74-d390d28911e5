

// BOTONES
// BOTONES
// BOTONES
// BOTONES
.btn{
	font-weight: 500;
}
.btn-middle-tooltip{
  color: var(--text-primary);
}
.btn-middle-tooltip:hover{
  color: var(--text-primary-strong);
}

.btn-primary {
	background-color: var(--bg-primary);
	border-color: var(--border-primary);
}
.btn-primary:hover {
	background-color: var(--bg-primary-hover);
	border-color: var(--border-primary-hover);
}
.btn-primary-two {
	background-color:var(--purple-500);
	border-color: var(--purple-500);
	color: var(--white);
}
.btn-primary-two:hover {
	background-color:var(--purple-600);
	border-color:var(--purple-600);
	color:var(--white) ;
}
.btn-secondary{
	border: 2px solid var(--bg-primary);
    color: var(--text-primary);
}
.btn-secondary:hover {
  background-color:var(--bg-base);
  border-color:var(--bg-primary-hover);
  color:var(--bg-primary-hover);
}
.copy-element a{
  color:var(--text-primary);
}
a.card .btn-link{
  color: var(--text-primary);
}
.btn-secondary-light {
	color: var(--text-primary);
}
.btn-secondary-light:hover {
	color: var(--text-primary);
}
.btn-banner .btn-link{
  color: var(--text-primary);
}


.btn-tertiary{
    color: var(--text-primary);

}


.btn-icon{
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    min-height: 44px;
    min-width: 44px;
    line-height: 100%;
    border: none;

    border-radius: 8px;
    color: #fff;
    font-size: 16px;
    padding: 8px 16px;
    background-color: var(--color-nav);
   
    &:hover{
        background-color: var(--color-nav-hover);
    }
    &:focus{
        outline: 2px solid var(--outline);
        outline-offset: 2px;
    }
    &:disabled{
        background-color: #C1BFC7;
        color: #F5F5F7;
    }
}


.btn-onBg {

    &:hover{
        color: var(--color-nav-hover);
        border-color: var(---color-nav-hover);
    }

    &:disabled{
        background-color: #C1BFC7;
        border-color: #F5F5F7;
        color: #96949C;
    }

    &:focus{
        outline: 2px solid var(--outline);
        outline-offset: 2px;
    }
}
.btn-link-primary{ 
  color: var(--text-primary); 
}  
.copy-element-box{
  .btn-link-primary{ 
    color: var(--text-primary); 
  }  
} 

// NAV SECTION
// NAV SECTION
// NAV SECTION
// NAV SECTION
// NAV SECTION
.nav-section .active{      
  color: var(--text-primary);
  border-left: 4px solid var(--border-primary);
}

.highlighted-text{
	color: var(--text-primary);
  }

  .luggage{
    a{
      color: var(--text-primary);
    }
  }
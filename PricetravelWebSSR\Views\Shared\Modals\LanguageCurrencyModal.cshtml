﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Meta.Alternate
@using PricetravelWebSSR.Models.Meta.Metatags;

@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions
@inject IOptions<CultureOptions> cultureOptions
@inject IOptions<CurrencyOptions> currencyOptions

@{
    var cultureConfiguration = ViewData["cultureData"] as Culture;
    var currencyConfiguration = ViewData["currencyData"] as PricetravelWebSSR.Options.Currency;
    var alternates = ViewData["Alternates"] as AlternateMain;
    var sortedAlternates = new List<AlternatePage>(); ;
    if (alternates is not null)
    {
        var selectedAlternatePage = alternates?.Alternates?.FirstOrDefault(a => a.Name == cultureConfiguration?.Name);
        sortedAlternates = alternates.Alternates
            .OrderByDescending(a => a == selectedAlternatePage)
            .ToList();
    }
    
    var selectedCurrency = currencyOptions.Value.Currencies.FirstOrDefault(c => c.CurrencyCode == currencyConfiguration?.CurrencyCode);
    var sortedCurrencies = currencyOptions.Value.Currencies
        .OrderByDescending(c => c == selectedCurrency)
        .ToList();
}

<!-- Modal Language & Currency -->
<dialog class="modal fade" id="modal_langcurr" tabindex="-1" aria-labelledby="modal-langcurr-tittle">
    <div class="modal-dialog modal-dialog-scrollable">
        <div class="modal-content" ng-init="">
            <div class="modal-header">
                <h2 class="modal-title" id="modal-langcurr-tittle">@viewHelper.Localizer("select_lang_currency")</h2>
                <button type="button" class="close" accesskey="" data-bs-dismiss="modal" data-dismiss="modal" aria-label="Close">
                    <i class="icons-close font-24"></i>
                </button>
            </div>
            <form name="vm.languageCurrencyForm" id="settingsForm" novalidate class="modal-body" method="GET" ng-submit="vm.onSubmitSettingsForm(vm.languageCurrencyForm)">

                <fieldset>
                    <legend>@viewHelper.Localizer("language")</legend>
                    <div class="modal__container">
                        @if (alternates is not null && alternates.Alternates.Count > 0)
                        {
                            @foreach (var alternate in sortedAlternates)
                            {
                                <input type="radio" ng-click="vm.onChangeSettings('language', '@(settingOptions.Value.SiteUrl)/@alternate.UrlPath' )"
                                        name="choose-language"
                                        class="d-none"
                                        value="@(settingOptions.Value.SiteUrl)/@alternate.UrlPath"
                                        id="@(settingOptions.Value.SiteUrl)/@alternate.UrlPath"  @(alternate.Name == cultureConfiguration?.Name ? "checked" : "")>
                                <label for="@(settingOptions.Value.SiteUrl)/@alternate.UrlPath"
                                       class="option">
                                    <img class="option__flag" width="28" height="28" src="/assets/img/header/@(settingOptions.Value.SiteName)/@(alternate.Culture).svg">
                                    <i class="icons-check-circle"></i>
                                    <span class="option__value">
                                        <span>@alternate.Name</span>
                                        @if (alternate.Name == cultureConfiguration?.Name)
                                        {
                                            <i class="option__mark">@viewHelper.Localizer("active")</i>
                                        }
                                    </span>
                                </label>
                            }
                        }
                    </div>
                </fieldset>

                <fieldset class="mt-32">
                    <legend>@viewHelper.Localizer("currency")</legend>
                    <p class="d-flex g-8 font-14">
                        <i class="icons-info font-24 font-blue d-none d-md-inline"></i>
                        @viewHelper.Localizer("price_warning")
                    </p>
                    <div class="modal__container">
                        @foreach (var currency in sortedCurrencies)
                        {
                            <input type="radio" ng-click="vm.onChangeSettings('currency', '@(settingOptions.Value.InternalApi.CurrencyChangePath)/@(currency.CurrencyCode.ToLower())' )"
                                    name="choose-currency"
                                    value="@(settingOptions.Value.InternalApi.CurrencyChangePath)/@(currency.CurrencyCode.ToLower())"
                                    id="@(settingOptions.Value.InternalApi.CurrencyChangePath)/@(currency.CurrencyCode.ToLower())"
                                    class="d-none"  @(currency.CurrencyCode == currencyConfiguration?.CurrencyCode ? "checked" : "")>
                            <label for="@(settingOptions.Value.InternalApi.CurrencyChangePath)/@(currency.CurrencyCode.ToLower())"
                                   class="option">
                                <span class="option__code">@currency.CurrencyCode</span>
                                <i class="icons-check-circle"></i>
                                <span class="option__value">
                                    <span>@currency.Name</span>
                                    @if (currency.CurrencyCode == currencyConfiguration?.CurrencyCode)
                                    {
                                        <i class="option__mark">@viewHelper.Localizer("active")</i>
                                    }
                                </span>
                            </label>
                        }
                    </div>
                </fieldset>
            </form>

            <div class="modal-footer">
                <button type="submit" form="settingsForm" class="btnPrimary w-100">@viewHelper.Localizer("apply")</button>
            </div>
        </div>
    </div>
</dialog>
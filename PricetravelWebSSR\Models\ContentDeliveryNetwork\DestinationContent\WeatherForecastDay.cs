﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{
    [ProtoContract]
    public class WeatherForecastDay
    {
        [ProtoMember(1)]
        public string Degrees { get; set; }

        [ProtoMember(2)]
        public bool Current { get; set; }

        [ProtoMember(3)]
        public string DegreesMax { get; set; }

        [ProtoMember(4)]
        public string DegreesMin { get; set; }

        [ProtoMember(5)]
        public int DescriptionCode { get; set; }

        [ProtoMember(6)]
        public string Description { get; set; }

        public WeatherForecastDay()
        {
            Degrees = string.Empty;
            DegreesMax = string.Empty;
            DegreesMin = string.Empty;
            Description = string.Empty;
        }
    }
}

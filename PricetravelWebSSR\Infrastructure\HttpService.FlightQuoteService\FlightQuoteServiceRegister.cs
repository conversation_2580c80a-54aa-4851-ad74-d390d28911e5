﻿using PricetravelWebSSR.Infrastructure.HttpService.FlightQuoteService.Dtos;
using PricetravelWebSSR.Interfaces;

namespace PricetravelWebSSR.Infrastructure.HttpService.FlightQuoteService
{
    public static class FlightQuoteServiceRegister
    {
        public static void AddFlightQuoteDependencies(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient<FlightQuoteService>("");

            services.AddSingleton(s => configuration.GetSection("HttpFlightQuoteServiceConfigurations").Get<FlightQuoteConfiguration>());

            services.AddSingleton<IFlightService, FlightQuoteService>();

        }
    }
}

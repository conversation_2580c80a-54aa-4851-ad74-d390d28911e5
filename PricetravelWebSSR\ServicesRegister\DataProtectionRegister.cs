﻿using PricetravelWebSSR.Infrastructure.DatabaseService.Redis.Dtos;
using StackExchange.Redis;
using Microsoft.AspNetCore.DataProtection;

namespace PricetravelWebSSR.ServicesRegister
{
    public static class DataProtectionRegister
    {
        public static void AddDataProtectionRegister(this IServiceCollection services, IConfiguration configuration)
        {
            var config = configuration.GetSection("RedisCache:Configuration").Get<RedisCacheConfiguration>();

            services.AddAntiforgery(options =>
            {
                options.HeaderName = "X-CSRF-TOKEN";
                options.FormFieldName = "_token";
            });

            if (config is not null && config.Active)
            {
                var redis = ConnectionMultiplexer.Connect($"{config.Host}:{config.Port},abortConnect=false");
                services.AddDataProtection().PersistKeysToStackExchangeRedis(redis, "DataProtection-Keys");
            }

        }
    }
}


.form-control{
	height: 52px;
	border-color:var(--border-strong);
}

.form-control::placeholder {
  color:var(--text-disabled);
  opacity: 1; /* Firefox */
}

.form-control:hover{
	color: #1a1a1a;
	border-color: #808080;
}
.form-control:focus {
    outline: 2px solid #4E67F1; 
	border-color: #BABABA;
    box-shadow: none;
}
.form-control:focus::placeholder {
    color: transparent;
}

.form-control:disabled, .form-control[readonly] {
    background-color: #fff;
    opacity: .50;
}


.form-control-txt{
    content: "MXN$";
}
.form-control-icon{
	border-color: #BABABA;
    padding-right: calc(1.5em + 0.75rem)!important;
    background-image: url("data:image/svg+xml,%3csvg width='14' height='12' viewBox='0 0 14 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath fill-rule='evenodd' clip-rule='evenodd' d='M7.29289 0.292893C7.68342 -0.0976311 8.31658 -0.0976311 8.70711 0.292893L13.7071 5.29289C14.0976 5.68342 14.0976 6.31658 13.7071 6.70711L8.70711 11.7071C8.31658 12.0976 7.68342 12.0976 7.29289 11.7071C6.90237 11.3166 6.90237 10.6834 7.29289 10.2929L10.5858 7H1C0.447715 7 0 6.55228 0 6C0 5.44772 0.447715 5 1 5H10.5858L7.29289 1.70711C6.90237 1.31658 6.90237 0.683417 7.29289 0.292893Z' fill='%23333333'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.is-password{
	border-color: #BABABA;
    padding-right: calc(1.5em + 0.75rem)!important;
    background-image: url("data:image/svg+xml,%3csvg width='12' height='14' viewBox='0 0 12 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M6 6.75C7.875 6.75 9.42857 5.24707 9.42857 3.375C9.42857 1.5293 7.875 0 6 0C4.09821 0 2.57143 1.5293 2.57143 3.375C2.57143 5.24707 4.09821 6.75 6 6.75ZM7.33929 8.01563H4.63393C2.0625 8.01563 0 10.0723 0 12.6035C0 13.1045 0.401786 13.5 0.910714 13.5H11.0625C11.5714 13.5 12 13.1045 12 12.6035C12 10.0723 9.91071 8.01563 7.33929 8.01563Z' fill='%23333333'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}


.form-control.is-valid, .was-validated .form-control:valid {
    border-color: #BABABA;
    background-image: url("data:image/svg+xml,%3csvg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M7 14C10.8553 14 14 10.8553 14 7C14 3.14469 10.8553 0 7 0C3.14469 0 0 3.14469 0 7C0 10.8553 3.14469 14 7 14ZM11.2381 5.19497C11.2381 5.3591 11.2106 5.46821 11.1015 5.55028L7.30076 9.37855L6.56262 10.1167C6.45303 10.1988 6.31641 10.2533 6.20731 10.2533C6.07069 10.2533 5.93407 10.1988 5.852 10.1167L3.22676 7.49145C3.11717 7.38186 3.06262 7.24524 3.06262 7.10862C3.06262 6.972 3.11717 6.83538 3.22676 6.75331L3.91035 6.04221C4.04697 5.93262 4.15655 5.87807 4.29317 5.87807C4.42979 5.87807 4.53938 5.93262 4.64848 6.04221L6.20731 7.60103L9.67979 4.10103C9.78938 4.01897 9.89848 3.96441 10.0351 3.96441C10.1447 3.96441 10.2813 4.01897 10.3904 4.10103L11.1015 4.83917C11.2111 4.92124 11.2381 5.05834 11.2381 5.19497Z' fill='%231C8207'/%3e%3c/svg%3e");
}
.is-warning {
    border-color: #B37B09;
    padding-right: calc(1.5em + 0.75rem)!important;
    background-image: url("data:image/svg+xml,%3csvg width='20' height='18' viewBox='0 0 20 18' fill='none' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='M10.8227 10.603L10.9986 6.19014C10.9986 6.12277 10.9632 6.05541 10.8929 5.98804C10.8227 5.92067 10.7524 5.88672 10.6468 5.88672H8.46943C8.36379 5.88672 8.29355 5.92067 8.22331 5.98804C8.15308 6.05541 8.11768 6.12277 8.11768 6.19014L8.29355 10.603C8.29355 10.6703 8.32895 10.7043 8.39919 10.7717C8.43459 10.8056 8.53967 10.839 8.6099 10.839H10.4715C10.576 10.839 10.6459 10.8058 10.7154 10.7727L10.7176 10.7717C10.7878 10.7043 10.8227 10.6703 10.8227 10.603Z' fill='%23B37B09'/%3e%3cpath d='M10.8581 14.2074V12.3885C10.8581 12.2871 10.8227 12.2198 10.7524 12.1524C10.6822 12.085 10.612 12.0511 10.5063 12.0511H8.6099C8.50427 12.0511 8.43403 12.085 8.36379 12.1524C8.29355 12.2198 8.25815 12.2871 8.25815 12.3885V14.2074C8.25815 14.3087 8.29355 14.3761 8.36379 14.4434C8.43403 14.4774 8.50427 14.5108 8.6099 14.5108H10.5063C10.6109 14.5108 10.6807 14.4776 10.7502 14.4445L10.7524 14.4434C10.8227 14.3761 10.8581 14.3087 10.8581 14.2074Z' fill='%23B37B09'/%3e%3cpath fill-rule='evenodd' clip-rule='evenodd' d='M12.1628 1.51158C11.0111 -0.50386 8.10503 -0.503862 6.95334 1.51158L0.399704 12.9805C-0.743136 14.9804 0.700963 17.4689 3.00443 17.4689H16.1117C18.4152 17.4689 19.8593 14.9804 18.7164 12.9805L12.1628 1.51158ZM8.68983 2.50386C9.07372 1.83205 10.0424 1.83205 10.4263 2.50386L16.98 13.9727C17.3609 14.6394 16.8795 15.4689 16.1117 15.4689H3.00443C2.23661 15.4689 1.75524 14.6394 2.13619 13.9727L8.68983 2.50386Z' fill='%23B37B09'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}
.warning-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: #B37B09;
}
.valid-feedback {
    display: none;
    width: 100%;
    margin-top: 0.25rem;
    font-size: 80%;
    color: $green;
}


.form-control.is-valid:focus, .was-validated .form-control:valid:focus {
    border-color: #ced4da;
    box-shadow: none;
}

label{
    margin-bottom:0rem;
    color: var(--text-subtle);
    font-weight: 500;
}

textarea.form-control {
    min-height: 112px;
    max-width: 100%;
}
 textarea.form-control.is-warning {
    padding-right: calc(1.5em + 0.75rem);
    background-position: top calc(0.375em + 0.1875rem) right calc(0.375em + 0.1875rem);
}
.input-mastercard, .input-visa, .input-amex{
    position: relative;
}
.input-mastercard span, .input-visa span, .input-amex span{
    background-repeat: no-repeat;
    background-size: 100%;
    position: absolute;
    height: 24px;
    width: 35px;
    top: 36px;
    right: 40px;
}       
.input-mastercard span {
    background-image: url(https://static.cdnpth.com/assets-core/img/logos_banks/mastercard-card.svg);
}     
.input-visa span {
    background-image: url(https://static.cdnpth.com/assets-core/img/logos_banks/visa-card.svg);
}   
.input-amex span {
    background-image: url(https://static.cdnpth.com/assets-core/img/logos_banks/amex-card.svg);
}

.number-input::-webkit-outer-spin-button,
.number-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.number-input {
  -moz-appearance: textfield;
  appearance: textfield;
}


// INPUTS STYLES
@media(max-width: 767px){

.form__box {
    position: relative;
    padding: 0.5rem;
    margin-bottom: 0;
}

.form__input:focus {
    outline: solid 1px $gray-400;
}

.form__input:focus+.form__label {
    transform: translateY(-25px);
    font-size: 13px;
    color: $gray-600;
    left: 15px;
    background-color: #fff;
}

.form__input:not(:placeholder-shown)+.form__label {
    transform: translateY(-0.6rem);
    font-size: 13px;
    left: 15px;
}

.phone__label {
    left: 3rem;
}

.form__input:focus+.phone__label {
    left: 20px;
}

.form__input:focus+.phone__label {
    transform: translateY(-1.6rem);
    font-size: 13px;
    color: $gray-600;
    left: 15px;
}

.form__input:not(:placeholder-shown)+.phone__label {
    transform: translateY(-1.6rem);
    font-size: 13px;
    left: 15px;
}

// .form-control {
//     height: auto;
// }

.is-invalid {
    outline: solid 1px $red;
}

.is-valid {
    outline: var(--border-strong);
}
// label{
//     font-weight: 400;
// }
}

input[type=number]::-webkit-inner-spin-button, 
input[type=number]::-webkit-outer-spin-button { 
  -webkit-appearance: none; 
  margin: 0; 
}

input[type=number] { -moz-appearance:textfield; }
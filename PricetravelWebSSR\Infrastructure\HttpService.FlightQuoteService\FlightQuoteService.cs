﻿using Microsoft.Extensions.Options;
using PricetravelWebSSR.Infrastructure.HttpService.FlightQuoteService.Dtos;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Models.Flight.Summary;
using PricetravelWebSSR.Options;
using System.Net.Http.Headers;
using System.Net.Mime;
using System.Text;
using System.Text.Json;

namespace PricetravelWebSSR.Infrastructure.HttpService.FlightQuoteService
{
    public class FlightQuoteService : IFlightService
    {
        private readonly HttpClient _httpClient;
        private readonly FlightQuoteConfiguration _configuration;
        private readonly static JsonSerializerOptions _jsonSerializerOptions = new() { PropertyNameCaseInsensitive = true };
        private readonly SettingsOptions _options;
        private readonly ILogger<FlightQuoteService> _logger;

        public FlightQuoteService(HttpClient httpClient, FlightQuoteConfiguration configuration, IOptions<SettingsOptions> options, ILogger<FlightQuoteService> logger)
        {
            _configuration = configuration;
            _httpClient = httpClient;
            _httpClient.DefaultRequestHeaders.Add("Accept", MediaTypeNames.Application.Json);
            _httpClient.BaseAddress = new Uri(_configuration.UriApiPTH);
            _options = options.Value;
            _logger = logger;
        }

        public async Task<SummaryResponse> QueryAsync(SummaryRequest request, CancellationToken ct)
        {
            var summaryResponse = new SummaryResponse();
            try
            {
                var queryParameters = $"IdReservation={request.IdReservation}&CustomerEmail={request.CustomerEmail}&Channel={request.Channel}";
                var uriService = $"{_configuration.UriSummary}?{queryParameters}";

                _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", GetAuthBasic(_configuration.UserSummary, _configuration.PasswordSummary));

                var httpResponseMessage = await _httpClient.GetAsync(uriService, ct);
                using var contentStream = await httpResponseMessage.Content.ReadAsStreamAsync(ct);
                summaryResponse = await JsonSerializer.DeserializeAsync<SummaryResponse>(contentStream, _jsonSerializerOptions, ct);

                if (!string.IsNullOrEmpty(summaryResponse?.Id))
                {
                    summaryResponse.Status = true;
                }
            }
            catch (Exception e)
            {
                _logger.LogError($"[Error] SummaryResponse {e.Message}: Summary Request: {JsonSerializer.Serialize(request)} - Summary Response: {JsonSerializer.Serialize(summaryResponse)} ");
            }

            return summaryResponse ?? new SummaryResponse();
        }



        private static string GetAuthBasic(string username, string password)
        {
            var svcCredentials = Convert.ToBase64String(ASCIIEncoding.ASCII.GetBytes(username + ":" + password));
            return $"{svcCredentials}";
        }
    }
}

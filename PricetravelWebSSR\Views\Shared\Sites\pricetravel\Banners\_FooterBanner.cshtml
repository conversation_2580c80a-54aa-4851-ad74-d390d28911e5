@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Models.Configuration
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Meta.Metatags
@inject ViewHelper viewHelper;
@inject IOptions<SettingsOptions> settingOptions
@{
    var userLocation = ViewData["UserLocation"] as UserLocation;
}

<section ng-cloak class="ContactFooterBanner" >
    <div class="container">
        <div class="ContactFooterBanner__section">
            <h2 class="d-xl-none">@viewHelper.Localizer("contact_us_title")</h2>
            <h2 class="d-none d-xl-block">@viewHelper.Localizer("need_help_contact")</h2>
            <div class="ContactFooterBanner__options">
                <a class="ContactFooterBanner__item" target="_blank" rel="noopener noreferrer" href="https://wa.me/@(viewHelper.Localizer("phone_phone_whatsapp"))">
                    <i class="icons-whatsapp d-flex"></i>
                    <span>@viewHelper.Localizer("by") WhatsApp</span>
                </a>
                <a class="ContactFooterBanner__item" target="_blank" rel="noopener noreferrer" href="http://m.me/pricetravel">
                    <i class="icons-messenger d-flex"></i>
                    <span>@viewHelper.Localizer("by") Messenger</span>
                </a>
         <a class="ContactFooterBanner__item" href="tel:@(userLocation.Country == "MX" ? settingOptions.Value.StaticPhoneNumbers.PrimaryPhone : settingOptions.Value.StaticPhoneNumbers.UsaPhone)">
                    <i class="icons-phone d-flex"></i>
                    <span>@viewHelper.Localizer("to_reservate")<strong>@(userLocation.Country == "MX" ? settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat : settingOptions.Value.StaticPhoneNumbers.UsaPhoneFormat)</strong></span>
                </a>
            </div>
        </div>
        <div class="ContactFooterBanner__section ContactFooterBanner__section--apps">
            <h2 class="d-xl-none"> @viewHelper.Localizer("save_up_app")</h2>
            <h2 class="d-none d-xl-block">@viewHelper.Localizer("download_app",@settingOptions.Value.AppName)</h2>
            <div class="ContactFooterBanner__options">
                <a href="@(userLocation.Country == "MX" ? "https://www.pricetravel.com/link/NTVFQTlFRkQ4RThC": "https://www.pricetravel.com/link/NkFFRDQwMEY4RTA3")" target="_blank" rel="noopenner noreferrer" >
                    <img bn-lazy-src="/assets/img/emails/app-store.png" alt="app store" fetchpriority="low" width="121" height="40">
                </a>
                <a href="@(userLocation.Country == "MX" ? "https://www.pricetravel.com/link/NTVFQTlFRkQ4RThC": "https://www.pricetravel.com/link/NkFFRDQwMEY4RTA3")" target="_blank" rel="noopenner noreferrer" >
                    <img bn-lazy-src="/assets/img/emails/google-play.png" alt="google play" fetchpriority="low" width="121" height="40">
                </a>
            </div>
        </div>
    </div>
</section>
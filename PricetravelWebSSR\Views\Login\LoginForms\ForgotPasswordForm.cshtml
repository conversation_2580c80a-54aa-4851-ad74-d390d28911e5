﻿@using PricetravelWebSSR.Helpers
@inject ViewHelper viewHelper



<p class="mb-0">@viewHelper.Localizer("forgot_review_email_instrucc")</p>
<p class="font-500">{{ vm.userData.email }}</p>
<p class="mb-2">@viewHelper.Localizer("forgot_review_valid")</p>

<button type="button" name="button" class="btnPrimary w-100 my-28" ng-click="vm.goToStepOne()">@viewHelper.Localizer("header_redirect_login")</button>
<div class="w-100 text-center mt-1">
    <button ng-show="vm.settings.forgot" type="button" name="button" class="btnTertiary"
            ng-click="vm.recoveryPassword()">
        @viewHelper.Localizer("resend")
    </button>
</div>
.input--radio {
    appearance: none; 
    -webkit-appearance: none;
    -moz-appearance: none; 
    position: relative;
    flex-shrink: 0;
    width: 20px;
    height: 20px;
    margin: 0;
    display: inline-block;
    border: 2px solid var(--border-strong);
    border-radius: 50%;
    vertical-align: middle;
    transition: all 150ms ease-out;

    &:checked {
        border-color: var(--border-primary);

        &::before {
            content: "";
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: var(--bg-primary);
            border-radius: 50%;
            transform: scale(0.75);
        }
    }
}
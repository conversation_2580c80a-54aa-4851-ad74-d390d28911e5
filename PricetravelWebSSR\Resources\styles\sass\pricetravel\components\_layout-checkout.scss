body {
    margin: 0;
    color: var(--text-main);
    background-color: var(--bg-level2);
}

p, li{
    color: var(--text-main);
}
h1, h2, h3, h4, h5, h6 {
    font-family: var(--basefontFamily-brand);  
    font-weight: 600;
}
h1{
    font: var(--title-md);
}
h2{
    font-size: 1.125rem;
    margin:0;
    padding-bottom:var(--spacing-inside-sm);
}
.text-success {
    color: var(--text-success);
    font-weight: 500;
} 
.text-success-strong {
    color: var(--text-success-strong);
    font-weight: 500;
} 
.text-inf {
    color: var(--text-info-strong);
    font-weight: 500;
}  
.sidebar-desktop{
    display: block;
}
.sidebar-mobile{
    display: none;
    button{
        text-align: right;
        height: auto;
    }
    .product-detail-item:nth-child(-n+2){
        width: 100%;
    }
    
}
.container{
    margin-top: 0.5rem;
}
@media(max-width: 767px){
    .container{
        margin-top: 0rem;
    }
    h1{
        font-size: 1.125rem;
    }
    h2{
        font-size: 1rem;
    }


}
@media(max-width: 991px){
    .sidebar-desktop{
        display: none;
    }
    .sidebar-mobile{
        display: block;
      
    }
}


  .title-success {
    font: var(--title-xxs);
    color: $green;
    font-weight: 500;
}  
.btn-light:focus{
    box-shadow: none;
}
.disabled{
    pointer-events: none;
    opacity: 0.5;
}



.font-weight-400{
    font-weight: 400;
}
.font-weight-500{
    font-weight: 500;
}
a{
    color: var(--bg-primary);
    &:hover{
        color: var(--bg-primary-hover);
    }
}
i{
    font-size: 1.2em;
    vertical-align: middle;
    font-style: normal;
}

li{
	margin-bottom: 1.5rem;
  }
.d-center{
    display: flex;
    justify-content: center;
    align-items: center;
}
.gap-bottom{
    padding-bottom: 0.5rem;
}
.cursor-pointer{
    cursor: pointer;
}

.title{
    font-weight: 500;
    color: var(--text-color-600);
    width: auto;
    display: inline-block;
    i{
        float: left;
        margin:0 5px;
    }
}



.header-page {
    padding: 25px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.brand-logo {
    width: 120px;
}



header + svg {
    width: 100%;
    height: 6px;
    display: block;
}

.alert-telephone svg {
    width: 24px;
    vertical-align: middle;
}

.svg_line {
    background-image: url('https://static.cdnpth.com/img/hr-brand.svg');
    height: 6px;
    background-size: cover;
    background-position: center;
    width: 100%;
}

.svg_line.svg_line_footer {
    height: 3px;
}

.hr-brand {
    display: block;
    background-image: url('https://static.cdnpth.com/img/hr-brand.svg');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: bottom;
    width: 100%;
    height: 6px;

    &.hr-brand--footer {
        height: 2px;
    }
}

.header-page {
	padding: 25px 0;
	display: flex;
	justify-content: space-between;
	align-items: center;
}



header + svg {
	width: 100%;
	height: 6px;
	display: block;
}

.navbar{
    padding-top: 16px;
    padding-bottom: 16px;

}

.avatar-user{
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #5b459b;
    color: white;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 1.375rem;
    margin: 0 5px;
    background-position: center center;
    background-size: cover;
    background-repeat: no-repeat;
    background-color: rgb(234, 0, 116);
}

.navbar-nav{
    .nav-link{
        padding: 16px;
        font-size: 0.875rem;
        line-height: 1.5;
        text-transform: capitalize;
        color: var(--bg-primary) !important;
        height: 100%;
        
    }
    .nav-item{
        &:hover{
            background-color: #eeebf6 !important;
            border-radius: 4px;
        }
    }
}
.navbar-light .navbar-nav .nav-link.active{
    background-color:var(--bg-primary-hover) ;
    border-radius: 5px;
    color: $white !important;
}

.nav-login{
    .nav-link{
        font-weight: bold;
    }

    .dropdown-item{
        transition: 1s;
        &:hover{
            transition: 1s;
            color: var(--bg-primary-hover);
            background-color: #eeebf6;
        }
    }
}

// .color-pink, .text-pink{
//     color: $color-secondary;
// }

.text-shadow{
    text-shadow: 1px 1px 2px black;
}

.navbar-tb {
    height: 60px;
    line-height: 30px;
    padding: 0.5rem;
    background: linear-gradient(180deg, #037bba, #035aaf 79%);
}

.navbar-tb .list-group-item {
    background-color: unset;
}

.navbar-tb .list-group-item a{
    color: white;
}

@media(max-width: 767px){
    .footer-group-heading{
        border-bottom: 1px solid #c2c2c2;
        padding: 10px 5px 10px 5px;
        cursor: pointer;
        &:hover{
            background-color: #eeebf6;
        }
    }
    .navbar-tb{
        display: none;
    }
    .nav-item{
        border-bottom:#c2c2c2 1px solid ;
    }
        
        footer .icons-expand-more{
            position: absolute;
            right: 18px;
        }

    .phonenumber {
        font-size: 0.938rem;
        padding: 25px 9px;
    }
    .b-sm-none{
        border:none;
    }

}

@media (min-width: 768px) and (max-width: 1199px){

    .navbar-tb__menu-contact{
        display: none;
    }

}

@media(min-width: 1280px){

    .navbar-tb__menu-contact-m{
        display: none;
    }

}

@media(min-width: 1280px){
    .navbar-expand-lg .navbar-nav .nav-link {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    footer .icons-expand-more {
        display: none;
    }

}

.card-header {
    background-color: transparent !important;
    h2{
        padding:0;
    }
}
.card-header-bg {
    background-color: #f2f2f2;
    padding: 8px 12px;
    margin: 0;
    font-size: 1.063rem;
}

.card-header-bg2 {
    background-color: #f9f9f9;
}
.form-group, .form-row, .form-content-2{
    max-width: 330px;
}
.form-group-1 {
    margin-bottom: 1rem;
  width: 100% !important;
}
.form-group-small{
    max-width: 330px;
}
.form-content-2{    
    display: flex;
    flex-direction: row;
    gap: 1.5rem;
}
small{
    font-size: 0.875rem;
}
.text-dark{
    color: $gray-900;
}
.text-lightgray{
    color: $gray-300;
}
.text-primary{
    color: var(--bg-primary) !important;
}
.text-green{
    color: $green;
}
// .text-green-light{
//     color: $green-light;
// }
.font-12{
    font-size: 0.75rem;
}
.font-14{
    font-size: 0.875rem;
}
.font-16{
    font-size: 1rem !important;
}
.font-18{
    font-size: 1.125rem;
}
.font-20{
    font-size: 1.25rem;
}
.font-24{
    font-size:  1.5rem;
}
.font-26{
    font-size:  1.6rem;
}
.rounded {
    border-radius: 0.5rem !important;
}
.bg-gray{
    background-color: #F2F2F2;
}


.form-row-full{  
    width: 100%;     
    display: inline-block;
    margin-bottom: 1rem;
}
.form-group-full{  
    width: 100%;     
    display: inline-block;
    margin-bottom: 1rem;
}
.form-group-inline div{
    width: 49%;
    display: inline-grid;
    padding-right: 0.5rem;
}
.form-group-inline div:last-child{
    padding-left: 0.5rem;
    width: 50%;
    padding-right: 0rem;
}
@media(max-width: 767px){
    .form-group-inline div, .form-group-inline div:last-child{
        width: 100%;
        display: inline-block;
        padding-right: 0rem;
        padding-left: 0rem;
        margin-bottom: 1rem;
    }   
}
.form-details {
    max-width: 600px;
    width: 100%;
}
.space-rooms {
    gap: 10px;
    display: flex;
}
.card-flex {
    display: flex;
    gap: 1rem;
    padding-bottom: 1rem;
}
@media(max-width: 767px){ 

    .card-flex {
        flex-direction: column;
        img {
            width: 100% !important;
            max-width: 348px !important;
            height: 102px !important;
            object-fit: cover;
        }
    
    }
    .two-collumns {
        .button-special {
            float: right;
            margin-top: 8px !important;
            height: 32px !important;
        }
    }
    .margin-mobile {
        margin-bottom: 0 !important;
    }
    .content-border {
        margin-bottom: 0 !important;
    }
}
.button-special {
    height: 32px !important;
}
.button-deposit {
    margin-bottom: 10px !important;
    max-width: fit-content !important;
}
﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{
    [ProtoContract]
    public class DestinationResponse
    {
        [ProtoMember(1)]
        public DestinationData Destination { get; set; }

        [ProtoMember(2)]
        public List<SimilarDestination> SimilarDestination { get; set; }

        [ProtoMember(3)]
        public List<Country> Countries { get; set; }

        [ProtoMember(4)]
        public DispatchInfo Dispatch { get; set; }

        [ProtoMember(5)]
        public string Message { get; set; }

        [ProtoMember(6)]
        public int Status { get; set; }

        public DestinationResponse()
        {
            Destination = new DestinationData();
            SimilarDestination = new List<SimilarDestination>();
            Countries = new List<Country>();
            Dispatch = new DispatchInfo();
            Message = string.Empty;
        }
    }
}

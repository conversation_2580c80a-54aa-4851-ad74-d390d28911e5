@using PricetravelWebSSR.Helpers
@using PricetravelWebSSR.Options
@using Microsoft.Extensions.Options

@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions

@{
    var mobile = false;
}
<!--Header-->
<header class="checkout">
    <!--Banner-->
    @await Html.PartialAsync($"~/Views/Shared/Components/BannerCheckout.cshtml")

    <div class="container-fluid" style="max-width: 1280px;">
        <div class="row">
            <div class="col-6">
                <a class="" href="@settingOptions.Value.SiteUrl" title="@settingOptions.Value.AppName" style="margin-top: -9px;width: 103px;">
                    <img src="https://img.cdnpth.com/media/assets-tb/1.7.33/img/logo-tiquetesbaratos.png" width="56" height="56" data-srcset="https://img.cdnpth.com/media/assets-tb/1.7.33/img/logo-tiquetesbaratos.png 1x" alt="TiquetesBaratos" />

                </a>
            </div>
            <div class="col-6 header-phone-navigation" role="navigation">
                <a href="tel:@settingOptions.Value.StaticPhoneNumbers.PrimaryPhone" class="header-phone-link">
                    <i class="icons-phone header-phone-icon" aria-hidden="true"></i>
                    <span>@viewHelper.Localizer("to_reservate")</span>
                    <span class="header-phone-number">@settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat</span>
                </a>
            </div>
        </div>
    </div>
</header>

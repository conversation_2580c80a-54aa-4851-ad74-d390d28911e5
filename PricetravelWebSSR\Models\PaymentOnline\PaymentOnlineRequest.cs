﻿using System.ComponentModel.DataAnnotations;

namespace PricetravelWebSSR.Models.PaymentOnline
{
    public class PaymentOnlineRequest
    {
        [Required]
        public string Id { get; set; } = string.Empty;

        [Required]
        public string Email { get; set; } = string.Empty;
        public string? SessionId { get; set; }
        public string RecaptchaToken { get; set; }

        public bool NoValid()
        {
            return string.IsNullOrEmpty(Id) && string.IsNullOrEmpty(Email);
        }
    }
}

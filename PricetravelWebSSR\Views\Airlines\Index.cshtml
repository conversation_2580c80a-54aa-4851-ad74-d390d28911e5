﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent
@using PricetravelWebSSR.Options;
@using System.Text.Json;
@using PricetravelWebSSR.Models.Collection;
@using PricetravelWebSSR.Models.Request;
@using PricetravelWebSSR.Types;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.Configuration
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@{
	var resolution = viewHelper.GetImageResolution();
	var isMobile = resolution.Device == DeviceType.Mobile;
	var request = ViewData["request"] as FlightRequest;
	var isRobot = viewHelper.IsRobot();
	var tabsLanding = ViewData["tabs"] as TabContent;
	var page = ViewData["PageRoot"] as string;
	var pageOrigin = ViewData["PageOrig"] as string;
	var metaTag = ViewData["MetaTag"] as MetaTag;
	var airline = ViewData["airline"] as String;
	var cultureConf = ViewData["cultureData"] as Culture;
	var destinations = (bool)ViewData["isDestinations"];
	var tabs = cultureConf.Tabs;
	var seoContent = ViewData["seoContent"] as SeoResponse;
	var tabsContent = tabsLanding.Content;
	var userLocation = ViewData["UserLocation"] as UserLocation;
	ViewData["Page"] = "home";
	ViewData["IsRobot"] = isRobot;
	ViewData["isMobile"] = isMobile;
	ViewData["Resolution"] = resolution;
	ViewData["cultureData"] = cultureConf;
}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot }, { "isHome", true } })

<div ng-controller="controller as vm" ng-cloak>
	<div class="c-box-home @airline pb-4">
		<div class="container ">
			<div class="row">
				<div class="col-12 col-md-4">
					<div id="promo" class="bg-img"></div>
				</div>

				@{
					var title = "";
					var subtitle = "";
				}

				@if (!destinations)
				{
					title = @viewHelper.Localizer($"tiquetesBaratos{airline}", settingOptions.Value.AppName);
					subtitle = @viewHelper.Localizer($"subtitleHome{airline}",settingOptions.Value.AppName);
				}
				else
				{
					title = @viewHelper.Localizer("destinationsTitle", request.StartingAirportPlace.CityName, request.ReturningAirportPlace.CityName);
					subtitle = @viewHelper.Localizer("destinationsSubtitle", settingOptions.Value.AppName);
				}
				<div class="col-12 col-md-8 text-right text-white mt-3 c-title-home mb-3 mb-md-0 px-3 px-md-0 z-1">
					<h1 class="pt-3 pt-md-2 pt-lg-3 text-white position-relative">@title</h1>
					<h2 class="f-r-medium">@subtitle</h2>
				</div>
			</div>
			<div class="row mt-lg-5 bg-booker mt-md-3">
				@await Html.PartialAsync("~/Views/Shared/Booker/Flight/Booker.cshtml", new
									ViewDataDictionary(ViewData) { { "Mobile", isMobile } })

			</div>
		</div>
	</div>

	<div class="container">
		<div class="row">
			<div class="col-md-12 my-3" ng-if="vm.history.length" ng-cloak>
				<p class="h3">@viewHelper.Localizer("continue_with_search" + @settingOptions.Value.SiteName)</p>
				<p>@viewHelper.Localizer("find_that_search")</p>
				<div class="row">
					<div class="col-md-6 col-lg-6 col-xl-3 mb-2" ng-repeat="registry in vm.history track by $index" title="{{registry.place_name}}">

						<button type="button" class="card ho-shadow p-0 w-100"
								ng-click="vm.selectDestination(registry)">

							<div class="d-flex align-items-center w-100">
								<div class="col-4 p-0 image-container" style="height:105px;">
									<img bn-lazy-src="@(settingOptions.Value.CloudCdn)/assets/recent-search-imgs/iatas/{{registry.IdG}}.jpg?tx=w_100,h_100,g_auto,c_fill" class="w-100"
										 data-img="{{registry.image}}?tx=w_100,h_100,g_auto,c_fill"
										 onerror="this.src = this.getAttribute('data-img');"
										 height="105" alt="{{registry.name}}" loading="lazy"
										 style="border-radius: .25rem 0rem 0rem .25rem; object-fit: cover"
										 onload="this.classList.add('loaded')" />
									<div class="skeleton-placeholder" style="height:105px;"></div>
								</div>
								<div class="col-8 pl-3 text-left" style="place-self: baseline;    line-height: 23px;">
									<div class="image-container" style="height:23px;">
										<img class="" bn-lazy-src="{{registry.icon}}" alt="" loading="lazy" width="15" height="15"
											 onload="this.classList.add('loaded')" />
										<div class="skeleton-placeholder" style="height:15px;"></div>
										<span style="font-size: 12px;" class="text-muted">{{registry.title}}</span>
									</div>
									<p class="card-title mb-0 h6  wrap-ellipsis">
										{{registry.data.place_name}} -  {{registry.data.place_name_to}}
									</p>
									<p class="card-text mb-1 mt-1 small">
										{{ registry.checkIn | date:'yyyy-MM-dd' }} - {{ registry.checkOut | date:'yyyy-MM-dd' }}
									</p>
									<p class="card-text mb-0 small text-muted">
										{{registry.adults}} {{ '@viewHelper.Localizer("adults")' | singularize:registry.adults }}
										<span ng-show="registry.kids > 0"> / {{registry.kids}} {{'@viewHelper.Localizer("children")' | singularize:registry.kids:@(cultureConf.CultureCode == "es" ? "2" : "3") }}</span>
									</p>
								</div>

							</div>

						</button>

					</div>


				</div>

			</div>
			@if (ViewData["collectionPromotion"] != null && ViewData["collectionPromotion"] is Section)
			{
				var collection = ViewData["collectionPromotion"] as Section;
				if (collection?.Cards != null && collection.Cards.Any())
				{
					<div class="col-12 my-3" in-viewport="vm.onScreenCards(0)">
						@await Html.PartialAsync("~/Views/Home/Components/Promotion.cshtml", new ViewDataDictionary(ViewData) { { "Mobile", isMobile }, { "collection", ViewData["collectionPromotion"] } })
					</div>
				}
			}

			@await Html.PartialAsync("~/Views/Home/Components/PaymentBanner.cshtml", new ViewDataDictionary(ViewData) { { "Mode", "Desktop" }, { "Target", "modal-payform" } })


			@if (settingOptions.Value.SeoHomeActive)
			{
				<div class="col-12 mt-5">
					@if (ProductType.FlightsPage == page)
					{
						@await Html.PartialAsync("~/Views/Home/Components/Landing.cshtml", new
											ViewDataDictionary(ViewData) { { "collection", ViewData["CollectionTabsLanding"] = tabsContent.Flights } })
					}
				</div>
			}
			@if (ViewData["collectionHotel"] != null && ViewData["collectionHotel"] is Section)
			{
				var collection = ViewData["collectionHotel"] as Section;
				if (collection?.Cards != null && collection.Cards.Any())
				{
					<div class="col-12 my-3">
						@await Html.PartialAsync("~/Views/Home/Components/Hotels.cshtml", new ViewDataDictionary(ViewData) { { "Mobile", isMobile }, { "collection", ViewData["collectionHotel"] } })
					</div>
				}
			}

			@if (ViewData["collectionVacation"] != null && ViewData["collectionVacation"] is Section)
			{
				var collection = ViewData["collectionVacation"] as Section;
				if (collection?.Cards != null && collection.Cards.Any())
				{
					<div class="col-12 my-3">
						@await Html.PartialAsync("~/Views/Home/Components/Vacation.cshtml", new ViewDataDictionary(ViewData) { { "Mobile", isMobile }, { "collection", ViewData["collectionVacation"] } })
					</div>
				}
			}
			@await Html.PartialAsync($"~/Views/Shared/Components/Questions.cshtml", new ViewDataDictionary(ViewData) { { "PlacesInfoS", null }, { "seoContents", seoContent }, { "Title", "" } })
		</div>
	</div>
</div>



@if (!isRobot)
{
	@if (settingOptions.Value.PaymentMethodActive)
	{
		@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/Modals/Payment.cshtml", new ViewDataDictionary(ViewData) { { "Mobile", isMobile } })
	}
	@await Html.PartialAsync("~/Views/Shared/Modals/CustomerSupport.cshtml")
	@await Html.PartialAsync("~/Views/Shared/Modals/RAPD.cshtml")
	@await Html.PartialAsync("_LoadingFull")
}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/Banners/_FooterBanner.cshtml", new ViewDataDictionary(ViewData))
@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "login", isRobot }, { "NewsletterForm", true } })

@section Meta {
	@await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
	@if (metaTag.Question.mainEntity.Count > 0)
	{
		<script type="application/ld+json">
			@Json.Serialize(metaTag.Question)
		</script>
	}
}

@section Preload {
	<link rel="preconnect" href="@settingOptions.Value.CloudCdn">
	<link rel="preconnect" href="https://img.cdnpth.com">
	<link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/home.css")" as="style" />

}

@section Css {
	<link type="text/css" rel="stylesheet"
		  href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/home.css")">
}



@section ScriptsPriority {
}

@section Scripts {
	<script>
		window.__pt = window.__pt || {};
		window.__pt.dataD = @Json.Serialize(request);
		window.__pt.collectionPromotion = @Html.Raw(viewHelper.ToJsonString(ViewData["collectionPromotion"]));
		window.__pt.collectionHotel = @Html.Raw(viewHelper.ToJsonString(ViewData["collectionHotel"]));
		window.__pt.collectionVacation = @Html.Raw(viewHelper.ToJsonString(ViewData["collectionVacation"]));
		window.__pt.cultureData = @Html.Raw(viewHelper.ToJsonString(ViewData["cultureData"]));

		/**  window.growthbook_config = window.growthbook_config || {};
		 *
		 window.growthbook_config.backgroundSync = false;

		 const value = window._growthbook?.getFeatureValue(
			 "test-feature",
			 "fallback"
		 ); **/
	</script>

	<script src="@staticHelper.GetVersion("/assets/js/controllers/flight-controllers.min.js")"></script>
}
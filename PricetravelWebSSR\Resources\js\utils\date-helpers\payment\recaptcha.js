﻿/**
 * Retrieves the widget ID of a reCAPTCHA element based on the provided query selector.
 *
 * @param {string} querySelectorItem - The query selector used to identify the reCAPTCHA element. Will be id element, class element, tag element etc-
 * @return {string|null} - The widget ID if found, otherwise null.
 */
export function getWgetIdCaptcha(querySelectorItem) {
    let wgetId = null
    let recaptcha = document.querySelector(querySelectorItem)
    let clients = window.___grecaptcha_cfg.clients ?? []
    for (const clientKey of Object.keys(clients)) {
        const client = clients[clientKey]

        for (const elem of Object.values(client)) {
            if (elem?.id === recaptcha.id) {
                wgetId = clientKey
                break;
            }
        }
    }
    return wgetId
}

/**
 * Creates a dynamic form, copies attributes from the original form, adds hidden inputs with values from the original form, and submits the dynamic form.
 *
 * @param {HTMLFormElement} form - The original form to be submitted
 *
 * @return {void}
 */
export function submitFormRc(form) {
    // Crear un nuevo formulario dinámico
    const dynamicForm = document.createElement('form');

    // Copiar todos los atributos del formulario original al nuevo formulario dinámico
    for (let i = 0; i < form.attributes.length; i++) {
        let attr = form.attributes[i];
        dynamicForm.setAttribute(attr.name, attr.value);
    }



    // Crear inputs ocultos en el nuevo formulario con los valores del formulario original
    const formData = new FormData(form);
    // Crear inputs ocultos en el nuevo formulario con los valores del formulario original para el método POST
    formData.forEach((value, key) => {
        if (key.toLowerCase() !== 'g-recaptcha-response') {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = key;
            input.value = value;
            dynamicForm.appendChild(input);
        }
    });

    let formAnti = document.getElementById('AntiForgeryToken');
    if (formAnti) {
        let csrfTokenInputAnti = formAnti.querySelector('input[name="_token"]');
        if (csrfTokenInputAnti && csrfTokenInputAnti.value) {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = "_token";
            input.value = csrfTokenInputAnti.value;
            dynamicForm.appendChild(input);
        }
    }

    // Agregar el formulario dinámico al body y enviarlo
    document.body.appendChild(dynamicForm);
    dynamicForm.submit();
}
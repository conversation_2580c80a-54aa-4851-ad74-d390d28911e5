﻿@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Helpers
@using PricetravelWebSSR.Models.Configuration
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@{
	var culture = ViewData["CultureData"] as Culture;
}
<div id="modal-call" class="modal fade" ng-click="vm.clickOutsideCall($event, 'modal-call', formllamadatos)">
    <div class="modal-dialog modal-dialog-centered modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-body p-0">
                <div class="row">
                    <div class="col-12 col-lg-6 bg-modal-call d-none d-lg-inline-block">
                        <div class="c-animated-call cac-1">
                            <span class="font-icons icons-phone d-block text-center font-30 pt-2"></span>
                            <small class="d-block px-3 text-center mt-1">@viewHelper.Localizer("modal_call_title")</small>
                            <img alt="@viewHelper.Localizer("modal_call_title")" width="60" src="@(settingOptions.Value.CloudCdn)/assets/img/tiquetesbaratos/animated-call.svg">
                        </div>
                    </div>
                    <div class="col-12 col-lg-6">
                        <div class="modal-header border-0 pb-1 pb-md-3">
                            <button modal-close=".modalCall" ng-click="vm.hideModalCall('modal-call', formllamadatos)" type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">×</span>
                            </button>
                        </div>
                        <div class="modal-body pt-0 pl-4 pr-5">
                            <form novalidate="true" method="post" name="formllamadatos" ng-submit="vm.submitFormtWrite(formllamadatos)" id="formllamadatos">
                                <h5 class="mb-2">@viewHelper.Localizer("modal_call_title")</h5>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group mt-1">
                                            <input name="name" ng-model="vm.userDataWriten.name" id="nombrecall" type="text" class="form-control" data-invalid="nombrecall_invalid" placeholder="@viewHelper.Localizer("modal_call_name_placeholder")" required>
                                            <div ng-class="{  'block-home' : vm.hasError(formllamadatos, 'name') }" id="nombrecall_invalid" class="invalid-feedback">
                                                @viewHelper.Localizer("modal_call_name_invalid")
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-12">
                                        <div class="form-group mt-1">
                                            <input type="text" name="phone" ng-model="vm.userDataWriten.phone" id="telefonocall" data-invalid="telefonocall_invalid" size="10" class="form-control" placeholder="@viewHelper.Localizer("modal_call_phone_placeholder")" required>
                                            <div ng-class="{  'block-home' : vm.hasError(formllamadatos, 'phone') }" id="telefonocall_invalid" class="invalid-feedback">
                                                @viewHelper.Localizer("modal_call_phone_invalid")
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="g-recaptcha">
                                    <div id="g-recaptcha"></div>
                                    <input type="text" style="z-index: -1; display: contents;" data-invalid="g_recaptcha_invalid" name="recaptchaToken" ng-model="vm.userDataWriten.recaptchaToken" id="recatchacall" class="form-control" required />
                                    <div id="g_recaptcha_invalid" ng-class="{  'block-home' : vm.hasError(formllamadatos, 'recaptchaToken') }" class="invalid-feedback">
                                        @viewHelper.Localizer("modal_call_recaptcha_invalid")
                                    </div>
                                </div>
                                <div class="mt-3">
                                    <div class="col-12 px-0">
                                        <button class="btnPrimary w-100" type="submit">@viewHelper.Localizer("modal_call_submit_button")</button>
                                    </div>
                                </div>
                                <div class="c-recapcha mt-3">
                                    <div class="col-12 bg-light py-3 px-4">
                                        <div class="row">
                                            <div class="col-12">
                                                <p class="mb-1"><b>@viewHelper.Localizer("modal_call_schedule_title")</b></p>
                                                <p class="mb-1 gray-200">@viewHelper.Localizer("modal_call_schedule_weekdays")</p>
                                                <p class="mb-1 gray-200">@viewHelper.Localizer("modal_call_schedule_weekends")</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


<script src="https://www.google.com/recaptcha/enterprise.js?render=explicit&hl=@culture.CultureCode"></script>

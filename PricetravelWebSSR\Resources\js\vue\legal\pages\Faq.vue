﻿<template>
    <div id="xplan" class="col-12 my-5">
        <h1 class="font-28 my-3">{{ __('information.faq') }}</h1>
        <div class="accordion" id="accordionExample"  v-if="getFaq">
            <div class="card" v-for="(item, index) in getFaqContent">
                <div class="card-header" id="`heading${index}`">
                    <h2 class="mb-0">
                        <button class="btn btn-link btn-block text-left" type="button" data-toggle="collapse" :data-target="`#collapse${index}`" aria-expanded="true" :aria-controls="`#collapse${index}`">
                            {{ item.question }}
                        </button>
                    </h2>
                </div>

                <div :id="`collapse${index}`" class="collapse" :class="index == 0 && 'show'" aria-labelledby="headingOne" data-parent="#accordionExample">
                    <div class="card-body">
                        {{ item.answer }}
                    </div>
                </div>
            </div>
        </div>
        <div v-else>{{ __('information.no_results') }}</div>
    </div>

</template>
<script>
    import { storeToRefs } from 'pinia';
    import { useLegalStore } from '../../stores/legal';

    export default {
        props: {
            content: null,
        },
        setup() {
            const useLegal = useLegalStore();
            const { setFaqContent } = useLegal;
            const { getFaqContent, getFaq } = storeToRefs(useLegal);

            return { setFaqContent, getFaqContent, getFaq }
        },
        beforeMount() {
            this.setFaqContent(this.content);
        },
    }
</script>

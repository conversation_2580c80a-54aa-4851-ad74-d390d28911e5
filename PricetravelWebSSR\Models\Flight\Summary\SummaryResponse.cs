﻿namespace PricetravelWebSSR.Models.Flight.Summary
{
    public class SummaryResponse
    {
        public string Id { get; set; } = string.Empty;
        public string ExternalId { get; set; } = string.Empty;
        public CustomerInformation? CustomerInformation { get; set; }
        public SummaryItem? Items { get; set; }
        public double TotalAmount { get; set; }
        public double TotalTaxes { get; set; }
        public int BookingStatus { get; set; }
        public DateTime? ExpirationDate { get; set; }
        public bool Status { get; set; }
        public string KeyValidation { get; set; } = string.Empty;

    }


    public class SummaryFamilyFareContent
    {
        public int CategoryType { get; set; }
        public string Description { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public int ContentType { get; set; }
        public int FlightIndex { get; set; }
        public int SegmentIndex { get; set; }
        public int Quantity { get; set; }
        public int Include { get; set; }
        public double Amount { get; set; }
    }

    public class SummaryFlight
    {
        public string Id { get; set; } = string.Empty;
        public int TripMode { get; set; }
        public List<SummaryFlightSegment> FlightDepartureSegments { get; set; } = [];
        public List<SummaryFlightSegment> FlightReturningSegments { get; set; } = [];
        public List<SummaryFlightPassenger> FlightPassengers { get; set; } = [];
        public int PaxCount { get; set; }
        public double TotalAmount { get; set; }
        public List<TotalTaxes> Taxes { get; set; } = [];
        public string ConfirmationCode { get; set; } = string.Empty;
    }
    public class TotalTaxes
    {
        public double Amount { get; set; }
        public string Currency { get; set; } = string.Empty;
        public string Code { get; set; } = string.Empty;
        public string CurrencyCode { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;

    }
    public class SummaryFlightSegment
    {
        public string FlightNumber { get; set; } = string.Empty;
        public string Airline { get; set; } = string.Empty;
        public string AirlineCode { get; set; } = string.Empty;
        public string OperatingAirline { get; set; } = string.Empty;
        public string OperatingAirlineCode { get; set; } = string.Empty;
        public string DepartureAirport { get; set; } = string.Empty;
        public string DepartureAirportCode { get; set; } = string.Empty;
        public string ArrivalAirport { get; set; } = string.Empty;
        public string ArrivalAirportCode { get; set; } = string.Empty;
        public DateTime DepartureDate { get; set; }
        public DateTime ArrivalDate { get; set; }
        public string DepartureAirportTerminal { get; set; } = string.Empty;
        public string ArrivalAirportTerminal { get; set; } = string.Empty;
        public DateTime ArrivalTime { get; set; }
        public DateTime DepartureTime { get; set; }
        public int FlightSegmentIndex { get; set; }
        public string FamilyFareName { get; set; } = string.Empty;
        public List<SummaryFamilyFareContent> FamilyFareContent { get; set; } = [];
    }

    public class SummaryFlightPassenger
    {
        public string Title { get; set; } = string.Empty;
        public int Type { get; set; }
        public string Names { get; set; } = string.Empty;
        public string LastNames { get; set; } = string.Empty;
        public int PassengerIndex { get; set; }
        public int PassengerNumber { get; set; }
        public bool RequireAdditionalUsaInformation { get; set; }
        public int Sex { get; set; }
        public int BirthDateDay { get; set; }
        public int BirthDateMonth { get; set; }
        public int BirthDateYear { get; set; }
        public EmergencyContact EmergencyContact { get; set; } = new();
        public string Nationality { get; set; } = string.Empty;
        public int CustomerIdentityDocumentType { get; set; }
        public string CustomerDocumentNumber { get; set; } = string.Empty;
        public string CustomerId { get; set; } = string.Empty;
    }

    public class SummaryItem
    {
        public List<SummaryFlight> Flights { get; set; } = [];
    }

    public class EmergencyContact
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string PhoneNumber { get; set; } = string.Empty;
        public string Title { get; set; } = string.Empty;
        public bool WasProvided { get; set; }
    }

    public class CustomerInformation
    {
        public string FirstName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string Phone { get; set; } = string.Empty;
        public string MobilePhone { get; set; } = string.Empty;
    }

}

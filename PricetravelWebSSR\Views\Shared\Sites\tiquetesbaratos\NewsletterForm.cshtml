@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using System.Text.Json;
@using PricetravelWebSSR.Types;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Options;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper
<div class="row">
    <div class="col-12 col-lg-10 offset-lg-1 p-0">
        <div class="row c-newsletter px-2 py-4 bg-white mx-0 shadow border">
            <div class="col-12 col-lg-12 pl-lg-4">
                <h3 class="font-poppins-medium mt-lg-3 mb-lg-2 font-22 mb-3">@viewHelper.Localizer("newsletter_title")</h3>
                <p class="mb-0 color-black-light">@viewHelper.Localizer("newsletter_subtitle")</p>
            </div>
            <div class="col-12 col-lg-12 pr-lg-4">
                <div class=" pt-4 content-form-newsletter">
                    <div class="col pr-md-0 pr-lg-2 col pt-md-0 mb-md-3 mb-lg-2 pt-lg-0">
                        <div class="form-email">
                            <input type="text" ng-model="vm.newsletter.emailUser" class="p-3 w-100 border rounded " placeholder="@viewHelper.Localizer("newsletter_email")" />
                            <small ng-if="vm.newsletter.showError" class="text-danger">@viewHelper.Localizer("newsletter_validatorMessage")</small>
                        </div>
                        
                    </div>
                    <div class="col pr-md-0 pr-lg-2 pb-md-2">
                        <div>
                            <div class="cap">
                                <div class="g-recaptcha-grupos">
                                    <div class="g-recaptcha-newslette"  id="g-recaptcha-newsletter"></div>
                                <input type="text" style="z-index: -1;display: contents;" name="g-recaptcha-newsletter" ng-model="vm.newsletter.recatcha" class="form-control" />
                                </div>
                                <p class="invalid-feedback text-left d-block mb-0" ng-if="vm.newsletter.recatcha === '' && vm.newsletter.submitCount">
                                  @viewHelper.Localizer("recaptcha_error")
                                </p>
                            </div>
                        </div>
                    </div>
                    <div class="col mt-md-0 ps-md-1 h-100">
                        <div class="d-flex align-items-center h-100">
                            <button ng-click="vm.sendEmailNewsletter()" class="btnSecondary w-100 p-4">@viewHelper.Localizer("newsletter_suscribe")</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

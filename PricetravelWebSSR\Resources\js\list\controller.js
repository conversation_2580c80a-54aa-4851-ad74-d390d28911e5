'use strict';

import ListTracker from '../MetricsTracker/list/metrics';
import FooterTracker from '../MetricsTracker/footer/metrics';
import GoogleMapList from '../utils/maps/GoogleMapList';
import ListUtil from '../utils/pricetravel/list/utils';
import GlobalUtil from '../utils/pricetravel/shared/globalUtil';
import { matchFamilyRule, paxRateFamily, familyRequote } from '../utils/pricetravel/shared/utilsFamily';
import AlgoliaTracker from '../MetricsTracker/algolia/metrics';
import { AMENITIES_DESCRIPTIONS } from '../utils/pricetravel/shared/cardService'
import { getHeaders } from '../utils/pricetravel/shared/tokenUtil';
import LowPriceNotifierService from '../utils/pricetravel/shared/lowPriceNotifierService';
import { formatDate, calculateDateDifference } from '../utils/constants/date';
import FavsTracker from '../MetricsTracker/favorite/metrics';
import { StorageService } from '../vue/services/StorageService'
window.app.controller('ListController', controller);
controller.$inject = ["$scope", "$http", "$q", "$timeout", "localStorageService", "$location", "$rootScope", "$filter","$compile"];

function controller($scope, $http, $q, $timeout, localStorageService, $location, $rootScope, $filter,$compile) {
    let config = window.__pt.settings.site;
    let exchange = window.__pt.exchange;
    let vm = this;
    let fn = window.__pt.fn;
    let userLocation = window.__pt.userLocation;
    let settings = window.__pt.settings;
    let place = window.__pt.place || {};
    let box = window.__pt.box || {};
    let cultureData = window.__pt.cultureData || {};
    let _exchange = window.__pt.exchange || {};
    let hotelResponse = window.__pt.hotelResponse || {};
    let ln = window.__pt.ln || {};
    let rates = {};
    let currentHotelList = {};
    let skillBase = {};
    let _paxes = {};
    let _size = 3;
    let _separator = "...";
    let _successSkillBase = "200 - OK";
    let _campaignToken = null;
    let __modal = "";
    let _pref = "";
    let _googleMap = null;
    let _devMode = window.__pt.devMode;
    let _user = window.__pt.user || null;
    let _isLogin = !!_user;
    let _paramQuery = fn.search();
    let _listUtil = new ListUtil(_paramQuery, config, _separator);
    let _globalUtil = new GlobalUtil(_paramQuery, config);
    let _userKey = _globalUtil.getUserKey();
    let _userKeyFac = _globalUtil.getUserKeyFavorites();
    let tracker = null;
    let trackerFooter = new FooterTracker();
    let algoliaTraker = new AlgoliaTracker();

    let allowRequest = true;
    let isMobile = fn.mobileAndTabletCheck();
    let headerPhoneId = '.header-phonebase';
    let isOpenMap = false;
    let dataRatesFetch = {};
    let disabledCardTargets = ['slick-next', 'slick-prev'];
    let filtersLoaded = false;
    let filtersInitReq = false;
    var slide_price = null;
    var slide_price_map = null;
    let retryData = null;
    let responseWithLogin = {
        loaded: false,
        data: {}
    };


    let newPax = matchFamilyRule(box.pax);
    let paramsRates = {
        from: "",
        site: config.domain,
        ids: "",
        countryCode: config.country.toUpperCase(),
        format: false,
        cache: settings.rb,
        CampaignToken: _campaignToken,
        profileId: null
    }

    let params = {
        culture: cultureData.internalCultureCode,
        pageSize: config.listItemsCount,
        organizationId: config.organization,
        propertyId: config.property,
        getFilters: true,
        filters: null,
        placeId: null,
        currentPage: +box.page,
        page: +box.page,
        site: config.site,
        countryCode: config.country.toUpperCase(),
        format: false,
        cache: settings.rb,
        placeName: place.displayText,
        placeId: place.id,
        checkIn: box.checkIn,
        checkOut: box.checkOut,
        profileId: null,
        filters: null,
        pax: "",
        userType: settings.userType

    }
    let trackerFavs = new FavsTracker();
    let paramsSkillBase = {
        checkInDate: params.checkIn,
        checkOutDate: params.checkOut,
        placeId: place.id,
        adultQuantity: 0,
        kidQuantity: 0,
        hotelId: 0,
        stepId: 2
    };

    //FUNCIONALIDAD HOTELSEARCH
    vm.keyPosition = 0;
    let _list = "";
    vm.suggestionSearchHotel = {
        from: {
            results: [],
            show: false
        }
    };
    vm.placeInputHasFocus = false;

    let hotelIdWithOutRates = null;
    vm.showServices = config.showServices;
    vm.code = config.code;
    vm.hotels = [];
    vm.isActive = undefined
    vm.mealplans = ln.mealplans;
    vm.loading = true;
    vm.loadingRefresh = false;
    vm.showMapDetail = false;
    vm.onRendeCenter = false;
    vm.loadingRates = true;
    vm.showAd = false;
    vm.hasError = false;
    vm.mapContent = true;
    vm.notQuote = false;
    vm.filters = [];
    vm.filtersSelectedArray = [];
    vm.filterCustomList = [];
    vm.filtersSelectedArrayString = box.filters || [];
    vm.currency = __pt.settings.site.currencyCodeName;
    vm.currencyList = __pt.settings.site.currencyCodeName;
    vm.currencyFormat = __pt.settings.site.currencyCodeName;
    vm.phoneBase = config.phoneDefault || "";
    vm.profileId = null;
    vm.input_hotel_name = box.hotel_name || "";
    vm.label_hotel_name = box.hotel_name || "";
    vm.title = "";
    vm.monthInterestData = {};
    vm.mealplans = ln.mealplans;
    vm.dataTotalCapacity = 0;
    vm.paxData = {};
    vm.showMapDetailCard = false;
    vm.filterUpDown = false;
    vm.nightsList = 0;
    vm.pricesSelected = [];
    vm.user = _user;
    vm.toggleRange = true;
    vm.rangeApplyState = false;
    vm.toggleOrderBy = false;
    vm.togglePopularFilter = true;
    vm.textOrderBy = "";
    vm.limitFilterGroup = 3;
    vm._isLogin = _isLogin;
    vm.popularFilters = null;

    vm.detailCardMap = {
        id: '',
        name: '',
        stars: 0,
        discount: 0,
        cloudUri: '',
        surveyAverage: '',
        highlightedAttributes: '',
        price: '',
        tax: '',
        showPromotion: false,
        showHotSBlackFri: false,
        highlights: [],
        rooms: 0,
        isFavorite: false,
        services: []
    };

    vm.paramsFilter = {
        cache: false,
        countryCode: config.country.toUpperCase(),
        culture: cultureData.internalCultureCode,
        filters: null,
        format: false,
        getFilters: true,
        hotelName: null,
        organizationId: config.organization,
        pageSize: config.listItemsCount,
        currentPage: +box.page,
        page: +box.page,
        placeName: place.displayText,
        placeId: place.id,
        propertyId: config.property,
        site: config.domain,
        responseTimeout: 20000,
        totalHotels: hotelResponse.totalHotels,
        CampaignToken: _campaignToken,
        uri: place.name,
        userType: settings.userType,
        devMode: _devMode ? _devMode : null
    }
    vm.config = {
        limitTextHighlight: config.limitTextHighlight,
        limitHighlightElement: config.limitHighlightElement,
    };

    vm.pagination = {
        currentPage: params.page - 1,
        totalPages: 0,
        startPage: 0,
        endPage: 0,
        breaker: 0,
        selected: 0,
        buttons: [],
        render: [],
        pageSize: config.listItemsCount,
        totalHotels: 0
    };

    vm.responsePayment = {
        fixedPayments: [],
        monthInterestFree: [],
        loading: true
    };

    vm.hotelFilters = {
        loading: true,
        min: 0,
        max: 0
    };

    vm.filterSelection = {
        min: 0,
        max: 0
    }


    //favorites
    const service = new LowPriceNotifierService(config.domainAPIUrl);
    vm.alertsFavorites = {
        notificationSuccess: false,
        notificationError: false,
        notificationWarning: false,
        message: "",
        actionType: '' // 'save' or 'delete'
    };
    vm.showTimer = false;
    let notificationTimeout = null;
    vm.favoriteHotels = [];

    vm.onInit = (title, pref) => {


        $('.seo-list-init').css('display', 'none');
        vm.title = $("<div/>").html(title).text();
        _pref = pref;
        _paxes = newPax;
        tracker = new ListTracker(_paxes, params, paramsRates);

        vm.paxData = box.pax;
        vm.dataTotalCapacity = _paxes.adults + _paxes.children;
        initializeValues();


        initializeElements();

        _googleMap = new GoogleMapList(vm, $http, $filter, $scope, _userKeyFac)
        showAds();
        vm.getUSersFavorites();



    }

    vm.getUSersFavorites = () => {
        const request = { UserId: _userKeyFac, country: config.country.toUpperCase() };

        service.getHotelsByUser(request).then(response => {
            vm.favoriteHotels = response.value;
        }).catch(error => {
            console.error('Error fetching hotels by user:', error);
        });
    }

    //Favorites funciontality
    vm.updateHotelsState = (ismap) => {
        vm.detailCardMap.isFavorite = vm.isFavoriteHotel(vm.detailCardMap.id);
        vm.hotels.forEach(hotel => {
            $scope.$applyAsync(() => {
                hotel.isFavorite = vm.isFavoriteHotel(hotel.hotelId);
            });
        });
        if (ismap) {
            _googleMap.clearMarkers()
            _googleMap.centerMap();
        }
    };
    vm.renderCurrencyDisplay = function (amount) {
        const currencyComponent = `<currency-display amount="${amount}" show-currency-code="true"></currency-display>`;
        const compiledComponent = $compile(currencyComponent)($scope);
        $scope.$digest();
        return compiledComponent[0].outerHTML;
    };
    vm.addFavorites = function (event, hotel, ismap = false) {
        event.preventDefault();
        event.stopPropagation();
        let copyBoxPax = comvertToMain(box.pax);
        const addHotelRequest = {
            hotelId: ismap ? hotel.id : hotel.hotelId,
            userId: _userKeyFac,
            uri: hotel.uri,
            site: config.domain,
            channel: isMobile ? config.chkSourceMobile : config.chkSourceDesktop,
            originalPrice: ismap ? hotel.price > 0 ? hotel.price : 0 : hotel.taxes ? hotel.taxes.totalRoomRatePerNight : 0,
            checkin: params.checkIn,
            checkout: params.checkOut,
            rooms: copyBoxPax.map(pax => ({
                adults: pax.adults,
                children: pax.children.map(child => child.year)
            })),
            campaignToken: _campaignToken || "",
            placeId: place.id,
            placeType: place.type,
            placeUri: place.uri,
            placeName: place.name,
            isMobile: isMobile
        };

        if (vm.isFavoriteHotel(addHotelRequest.hotelId)) {
            const favoriteId = vm.getFavoriteId(addHotelRequest.hotelId);
            if (favoriteId) {
                service.removeHotel({ Id: favoriteId, UserId: _userKeyFac }).then(response => {
                    $scope.$applyAsync(() => {
                        vm.favoriteHotels = vm.favoriteHotels.filter(favHotel =>
                            !(favHotel.hotelId === addHotelRequest.hotelId &&
                                favHotel.checkin === box.checkIn &&
                                favHotel.checkout === box.checkOut &&
                                angular.equals(comvertToAB(favHotel.rooms), box.pax))
                        );
                        const favoriteHotel = { ...addHotelRequest, id: favoriteId, name: hotel.name };
                        trackerFavs.logDeleteFav(favoriteHotel, 'list');
                        vm.updateHotelsState(ismap);
                        vm.showNotification('error', hotel.name, 'delete');
                    });
                }).catch(error => {
                    $scope.$applyAsync(() => {
                        vm.showNotification('error', error);
                    });

                });
            }
        } else {
            // Agregar el hotel a favoritos
            service.addHotel(addHotelRequest).then(response => {
                $scope.$applyAsync(() => {
                    if (response.value) {
                        const favoriteHotel = { ...addHotelRequest, id: response.value, name: hotel.name };
                        vm.favoriteHotels.push(favoriteHotel);
                        trackerFavs.logAddFav(favoriteHotel, 'list');
                        vm.updateHotelsState(ismap);
                        vm.showNotification('success', hotel.name, 'save');
                    } else {
                        vm.showNotification('warning', '', '');
                    }

                });
            }).catch(error => {
                $scope.$applyAsync(() => {
                    vm.showNotification('error', error);
                });
            });
        }
        _globalUtil.vibrate();

    };

    vm.showNotification = (type, message, actionType) => {
        // Cancelar cualquier timeout anterior
        if (notificationTimeout) {
            $timeout.cancel(notificationTimeout);
        }
        vm.showTimer = false;
        $timeout(() => {
            vm.showTimer = true;
        });
        vm.alertsFavorites.notificationSuccess = type === 'success';
        vm.alertsFavorites.notificationError = type === 'error';
        vm.alertsFavorites.notificationWarning = type === 'warning';
        vm.alertsFavorites.message = message;
        vm.alertsFavorites.actionType = actionType; // 'save' or 'delete'

        // Ocultar la notificación después de 6 segundos
        notificationTimeout = $timeout(function () {
            vm.alertsFavorites.notificationSuccess = false;
            vm.alertsFavorites.notificationError = false;
            vm.alertsFavorites.notificationWarning = false;
            vm.alertsFavorites.message = "";
            vm.alertsFavorites.actionType = '';
        }, 6000);
    };
    vm.getFavoriteId = function (hotelId) {
        const favorite = vm.favoriteHotels.find(favHotel =>
            favHotel.hotelId === hotelId &&
            favHotel.checkin === box.checkIn &&
            favHotel.checkout === box.checkOut &&
            angular.equals(comvertToAB(favHotel.rooms), box.pax)
        );
        return favorite ? favorite.id : null;
    };

    vm.isFavoriteHotel = (hotelId) => {
        return vm.favoriteHotels.some(favHotel =>
            favHotel.hotelId === hotelId &&
            favHotel.checkin === box.checkIn &&
            favHotel.checkout === box.checkOut &&
            angular.equals(comvertToAB(favHotel.rooms), box.pax)
        );
    };
    vm.slideCarrousel = async (event, hotel, direction) => {
        event.preventDefault();
        if (!hotel.isLoadGallery) {
            hotel.isLoadGallery = false;
            let qr = {
                HotelId: hotel.hotelId,
                culture: params.culture,
                organizationId: params.organizationId,
                propertyId: params.propertyId,
                imageProfileId: 'desktop',
            }

            let response = await $http.get(config.endPoints.galleryDetailUrl, { params: qr });

            hotel.gallery = fn.clone([...response.data.gallery, hotel.mainPhoto]);
            hotel.isLoadGallery = true;
            hotel.galleryPosition = 2;

        }
        hotel.galleryPosition += direction == 'right' ? +1 : -1;

        $timeout(function () {
            hotel.mainPhoto = hotel.gallery[Math.abs((hotel.galleryPosition - 1) % hotel.gallery.length)];
        }, 0);

    }

    vm.areAllServicesNotHighlighted = (services) => {
        if (services.length === 0)
            return false;

        return services.every((service) => {
            return !service.highlited;
        });
    }

    vm.getTitleFilter = (filter, group) => {
        if (group.id == 8 && filter.uri != "sin-categoria-estrellas" && filter.uri != "without-stars-category") {
            return `<i class="font-icons icon-${filter.uri}-star"></i>`
        }

        return filter.display;
    }

    vm.getTitleFilterCustom = (filter, group, isPhil = false) => {
        if (filter.categoryGuid === 102 && !isPhil) {
            return `<i class="font-icons icon-${filter.uri}-star"></i>`
        }
        var title_parts = filter.display.split("_");
        const textSanitize = ln[`${title_parts[0]}`] && ln[`${title_parts[0]}`][`${title_parts[1]}`] ? ln[`${title_parts[0]}`][`${title_parts[1]}`] : filter.display;

        if (vm.getFilterPromotion(filter))
            return `${textSanitize}  <i class="icons-flame1 padding-icon-flame1"></i>`

        return textSanitize;
    }

    vm.getFilterPromotion = (filter) => {
        if (Array.isArray(config.filterToHighlight) && config.filterToHighlight.length > 0) {
            return config.filterToHighlight.includes(filter.display);
        }
        return false;
    };
    

    vm.getTitleFilterPopular = (filter) => {

        if (filter.categoryGuid != 0) {
            return vm.getTitleFilterCustom(filter, null);
        } else {
            return vm.getTitleFilter(filter, {})
        }
    }

    vm.onSelectRecommenderDate = (e, item, url) => {
        e.preventDefault();
        e.stopPropagation();

        tracker.selectDateSuggested(item);

        let urlarray = url.split('?');
        let query = fn.searchUri(urlarray[1]);
        query.checkin = item.CheckIn;
        query.checkout = item.CheckOut;
        let queryString = fn.objectToQueryParams(query);
        window.location.href = urlarray[0] + '?' + queryString;
    }

    vm.onEventMapToDetail = () => {
        tracker.onClickMapToDetail(_paxes);
    };

    vm.onSelectHotel = (hotel, evt) => {
        let objectIDs = [];
        objectIDs.push(hotel.hotelId.toString());
        let indexName = hotel.indexName;
        StorageService.set("_key_argo_object_n", JSON.stringify(algoliaTraker.clickedObjectIDsAfterSearch(objectIDs, indexName || "searchHotelPlacesUS", hotel)));
        const classname = evt.target.className;

        if (classname.includes(disabledCardTargets[0]) || classname.includes(disabledCardTargets[1])) {
            return;
        }

        //$("#loader-page").removeClass("d-none")
        tracker.onClickImpression(hotel);

        if (hotel.hotelId == +[vm.profileId] && hotel.dateRecommended && hotel.dateRecommended.length > 0) {
            return;
        }

        //window.open(hotel.url, target);
    }
    vm.getRecomendateDatePaxNight = (days) => {
        const nightTitle = days == 1 ? ln.night : ln.nights;
        return `${days} ${nightTitle}`;
    }
    vm.getRecomendateDatePax = (days) => {
        const adultsTitle = newPax.paxes.adults == 1 ? ln.adult : ln.adults;  
        const childrenTitle = newPax.paxes.children.length == 1 ? ln.child : ln.children;
        const childrenPart = newPax.paxes.children.length > 0
            ? ` ${ln.and} ${newPax.paxes.children.length} ${childrenTitle}:`
            : ':';
        return `${ln.for} ${newPax.paxes.adults} ${adultsTitle}${childrenPart}`;
    }
    vm.shouldDisplayCard = (hotel) => {
        const isSameHotel = +vm.profileId === +hotel.hotelId;

        if (isSameHotel) {
            return !(hotel.dateRecommended && hotel.dateRecommended.length > 0);
        }

        return true;

    }

    vm.onSelectDate = (event, hotel, date) => {
        event.preventDefault();
        const { checkin, checkout } = date;
        const url = updateUrlParams(hotel.url, checkin, checkout);
        const newUrl = `${url}`
        window.open(newUrl, '_blank');
    }

    vm.sendContentFooter = (title) => {
        trackerFooter.clickLink(`footer :: ${title}`);
    }

    vm.onClosed = (modal) => {
        $(`#${modal}`).modal("hide");
    }

    vm.onCloseMapCardDetail = (isClosed = false) => {
        var countUp = function () {
            if (isClosed && vm.showMapDetailCard) {
                vm.showMapDetailCard = false;
            } else {
                vm.detailCardMap.show = false;
                vm.showMapDetailCard = false;
                _googleMap.clearBubble();
            }
        }
        $timeout(countUp, 400);

    };

    vm.onEventMapToDetail = () => {
        tracker.onClickMapToDetail(_paxes);
    };

    vm.nextPage = () => {
        _globalUtil.windowScrollTop();
        if (vm.pagination.currentPage < (vm.pagination.totalPages - 1)) {
            vm.pagination.currentPage += 1;
            onReloadHotels()
        }
    }

    vm.prevPage = () => {
        _globalUtil.windowScrollTop();
        if (vm.pagination.currentPage > 0) {
            vm.pagination.currentPage -= 1;
            onReloadHotels()
        }
    }

    vm.selectPage = (page) => {
        _globalUtil.windowScrollTop();
        if (page != _separator) {
            vm.pagination.currentPage = page - 1;
            onReloadHotels();
        }
    }

    vm.onLoadPayment = (modal, btnTitle, btnText) => {
        __modal = modal;
        vm.showModal(__modal, btnTitle, btnText);
        onSearchPaymentMethod();
    }

    vm.showModal = (modal, btnTitle, btnText) => {
        $(`#${modal}`).modal("show");

        if (modal == "modal-payform" || modal == "modal-contact") {
            trackerFooter.selectContent(btnTitle, btnText);
        }
    }

    vm.hideModalList = (modal) => {
        $(`#${modal}`).modal('hide');
    }

    vm.showModalMap = (modal) => {
        isOpenMap = true;
        tracker.onClickOpenMap(_paxes);
        _googleMap.openMap(params, paramsRates, _paxes, _campaignToken, vm.paramsFilter);

    }

    vm.hideModalMapList = (modal) => {
        isOpenMap = false;
        onReloadHotels();
        $(`#${modal}`).modal("hide");
    }

    vm.allowShowFilter = (category) => {
        return config.filtersDisabled.indexOf(category) == -1
    }

    function comvertToAB(input) {
        return input.reduce((acc, room) => {
            acc.adults += room.adults;
            acc.children = acc.children.concat(room.children.map(age => ({ "year": age })));
            acc.rooms += 1;
            return acc;
        }, { "adults": 0, "children": [], "rooms": 0 });
    }
    function comvertToMain(data) {
        const { adults, children, rooms } = data;

        const adultsPerRoom = Math.floor(adults / rooms);
        const extraAdults = adults % rooms;

        const childrenPerRoom = Math.floor(children.length / rooms);
        const extraChildren = children.length % rooms;

        const result = [];

        let childIndex = 0;

        for (let i = 0; i < rooms; i++) {
            const currentRoomAdults = adultsPerRoom + (i < extraAdults ? 1 : 0);
            const currentRoomChildrenCount = childrenPerRoom + (i < extraChildren ? 1 : 0);
            const currentRoomChildren = [];
            for (let j = 0; j < currentRoomChildrenCount; j++) {
                currentRoomChildren.push({ year: children[childIndex].year });
                childIndex++;
            }

            result.push({
                adults: currentRoomAdults,
                children: currentRoomChildren
            });
        }

        return result;
    }


    /*****
    * 
    * SEARCH HOTELS
    * 
    *****/

    vm.onSearchSearch = (value, list, input, focus) => {
        if (!value || (value && value.length < 3)) {
            return;
        }
        // _inputName = focus;
        _list = list;
        vm.paramsFilter.search = value;
        vm.paramsFilter.hotelName = "";
        $http.get(config.endPoints.searchHotelUrl, {
            params: vm.paramsFilter
        }).then(onSuccessSuggestionSearch, onErrorLog)

    }

    vm.onSelectSearch = (item, type, index, fromHistory = false) => {
        let copyToSearch = vm.input_hotel_name;
        if (item && item.hotelId == null) {
            vm.paramsFilter.hotelName = item.search;
            vm.input_hotel_name = item.search;
            vm.profileId = null;
        } else {
            vm.paramsFilter.hotelName = null;
            vm.profileId = item.hotelId;
            vm.paramsFilter.profileId = vm.profileId;
            vm.input_hotel_name = item.name;
        }
        tracker.setEventFilterHotelName(item.hotelId, copyToSearch, vm.input_hotel_name, place);
        vm.placeInputHasFocus = false;
        onReloadHotelsFilter();

    }
    vm.handleKeyPress = function (event) {
        if ((event.which === 13 || event.keyCode === 13) && (vm.input_hotel_name && vm.input_hotel_name.length >= 3)) {
            vm.paramsFilter.hotelName = vm.input_hotel_name;
            tracker.setEventFilterHotelName("", vm.input_hotel_name, vm.input_hotel_name, place);
            vm.input_hotel_name = vm.input_hotel_name;
            vm.paramsFilter.search = "";
            vm.profileId = null;
            vm.placeInputHasFocus = false;
            onReloadHotelsFilter();
        }

    };

    vm.onRemoveSearch = () => {
        if (vm.profileId != null) {
            vm.profileId = null;
            vm.input_hotel_name = "";
            vm.paramsFilter.profileId = null;
            vm.placeInputHasFocus = false;
            onReloadHotelsFilter();
        } else if (vm.paramsFilter.hotelName) {
            vm.input_hotel_name = "";
            vm.placeInputHasFocus = false;
            vm.paramsFilter.hotelName = null;
            onReloadHotelsFilter();
        } else {
            vm.input_hotel_name = "";
            vm.placeInputHasFocus = false;
        }

    }

    vm.onFocusSearch = (element, input) => {
        if (vm.suggestionSearchHotel[element]["results"].length) {
            setTimeout(() => {
                $scope.$apply(() => {
                    vm.suggestionSearchHotel[element]["show"] = true;
                    vm.placeInputHasFocus = true;
                });
            }, 250)
        }
    }

    vm.onClickOutsideSearch = (element, prop) => {
        vm.placeInputHasFocus = false;
        element[prop] = false;
        vm.placeInputHasFocus = false;
    }

    function onSuccessSuggestionSearch(response) {

        const data = response.data || [];
        data.push({
            name: `<span class="font-icons icons-search icon-search-hotel"></span>   <span class="text-search-hotel">Buscar "${vm.input_hotel_name}"</span>`,
            hotelId: null,
            search: vm.input_hotel_name
        });
        vm.suggestionSearchHotel[_list]["results"] = data;
        vm.suggestionSearchHotel[_list]["show"] = true;
        vm.placeInputHasFocus = true;
    }

    /*****
     * 
     * Filters
     * 
     *****/
    vm.deleteFilterHotel = (reload = true) => {
        vm.input_hotel_name = "";
        vm.label_hotel_name = "";
        vm.pagination.currentPage = 0;

        if (reload) {
            onReloadHotels();
        }
    }

    vm.filterSelected = (filter) => {
        vm.paramsFilter.page = 1;
        vm.paramsFilter.currentPage = 1;
        vm.pagination.currentPage = 0;

        let objFiltred = vm.filtersSelectedArray.filter(item => item.groupId != filter.id)

        assignFilters(objFiltred);
        changeTextTitle();
        tracker.selectedFilter(filter.display, filter.uri);
    }

    vm.filterChildSelected = (filter, group) => {

        vm.paramsFilter.page = 1;
        vm.paramsFilter.currentPage = 1;
        vm.pagination.currentPage = 0;

        if (filter.isMultiselect == false) {
            let objFiltred = vm.filtersSelectedArray.filter(item => item.groupId != filter.groupId);
            objFiltred.push(filter)
            assignFilters(objFiltred);
        } else {
            let position = vm.filtersSelectedArray.find(item => item.id === filter.id);
            let objFiltred = [];
            if (position) {
                objFiltred = vm.filtersSelectedArray.filter(item => item.id != filter.id)
            } else {
                vm.filtersSelectedArray.push(filter)
                objFiltred = vm.filtersSelectedArray
            }
            assignFilters(objFiltred);
        }
        changeTextTitle()
        if (filter.id === 20) {
            var concatNewObject = group.filters.filter(item => item.id === 69);
            vm.filtersSelectedArray.push(concatNewObject[0])
            tracker.selectedFilter(group.display, concatNewObject[0].uri);
        }
        let filtersAlgolia = [];
        filtersAlgolia.push(group.uri + ":" + filter.uri);
        algoliaTraker.clickedFilters(vm.hotels[0].indexName || "searchHotelPlacesUS", filtersAlgolia, window.__pt.ln.filterslang[group.uri] || group.uri);
        tracker.selectedFilter(group.display, filter.uri);
    }

    vm.filterCustomSelected = (filter) => {

        vm.paramsFilter.page = 1;
        vm.paramsFilter.currentPage = 1;
        vm.pagination.currentPage = 0;

        let filters = vm.filterCustomList.filter(item => item.categoryGuid != filter.id);
        vm.filterCustomList = filters;
        tracker.selectedFilter(filter.display, filter.uri);
        if (vm.pricesSelected.length < 1) {
            vm.resetRangeFilter(false);
        }

        onReloadHotels();
    }

    vm.filterCustomChildSelected = (filter, group) => {

        vm.paramsFilter.page = 1;
        vm.paramsFilter.currentPage = 1;
        vm.pagination.currentPage = 0;

        let filters = vm.filterCustomList;

        if (!group.isMultiselect) {
            filters = vm.filterCustomList.filter(item => item.categoryGuid != group.id);
        }

        if (filter.isSelected) {
            filters.push(filter);
        } else {
            filters = vm.filterCustomList.filter(item => item.uri != filter.uri);
        }

        if (vm.pricesSelected.length < 1) {
            vm.resetRangeFilter(false);
        }
        let filtersAlgolia = [];
        filtersAlgolia.push(group.uri + ":" + filter.uri);
        algoliaTraker.clickedFilters(vm.hotels[0].indexName || "searchHotelPlacesUS", filtersAlgolia, window.__pt.ln.filterslang[group.uri] || group.uri);
        vm.filterCustomList = filters;
        tracker.selectedFilter(group.display, filter.uri);
        onReloadHotels();
    }

    vm.deleteFilterArray = () => {
        vm.filtersSelectedArray = [];
        vm.filtersSelectedArrayString = [];
        vm.filterCustomList = [];
        vm.paramsFilter.hotelName = "";
        vm.paramsFilter.search
        vm.input_hotel_name = "";
        vm.paramsFilter.hotelName = null;
        vm.profileId = null;
        vm.deleteFilterHotel(/*reload = */ false);
        vm.resetRangeFilter(/*reload = */ false);
        updatePathFilter();
        onReloadHotels();
    }

    vm.filterPillSelected = (filter) => {
        let objFiltred = vm.filtersSelectedArray.filter(item => item.id != filter.id)
        assignFilters(objFiltred);
    }

    vm.filterCustomPillSelected = (filters, index) => {
        if (!filters[index]) return;
        
        const { display } = filters[index];
        const popularFilter = vm.popularFilters.find(item => item.display === display);
    
        popularFilter && (popularFilter.isSelected = false);

        filters.splice(index, 1);
        updateQueryPathFilter();
        onReloadHotels();
    }

    vm.onSubmitFilter = (form) => {
        vm.pagination.currentPage = 0;
        if (vm.hotelFilters.filterCustoms.prices.min != vm.filterSelection.min || vm.hotelFilters.filterCustoms.prices.max != vm.filterSelection.max) {
            setFilterPhillRange();
        }
        onReloadHotels()
    }

    vm.onChangeOrderBy = (order) => {
        vm.paramsFilter.orderBy = order.code;
        vm.textOrderBy = order.display;
        tracker.orderEvent(vm.paramsFilter.orderBy);
        vm.onSubmitFilter();
    }

    vm.closeFiltersMobile = () => {
        vm.onSubmitFilter();
        vm.onClosed("modal-filters");
    }

    vm.onLoadFilters = (payload) => {

        if (!payload) {
            return;
        }

        const indexOfGroup = payload.findIndex(x => x.id === 3);
        if (indexOfGroup > -1) {
            payload[indexOfGroup].filters = payload[indexOfGroup].filters
                .filter(filter => filter.id === 24 || filter.id === 20 || filter.id === 70)
                .sort((a, b) => {
                    const order = [24, 70, 20];
                    return order.indexOf(a.id) - order.indexOf(b.id);
                });
        }
        vm.filters = setIsSelect(payload) || [];
    }

    vm.filterUpDownSet = (indexGroup) => {
        vm.filters[indexGroup].filterUpDown = !vm.filters[indexGroup].filterUpDown;
    }

    vm.filterUpDownSetCustom = (indexGroup) => {
        vm.hotelFilters.filterCustoms.filterGroups[indexGroup].filterUpDown = !vm.hotelFilters.filterCustoms.filterGroups[indexGroup].filterUpDown;
    }

    vm.renderNameFilter = (isCheckBox, name, index, indexChild) => {
        let childrenPosition = isCheckBox ? `_${indexChild}` : '';
        return `${name}_${index}${childrenPosition}`
    }

    vm.applyPopularFilter = (filter) => {

        if (filter.categoryGuid != 0) {
            let filterCustomGroup = vm.hotelFilters.filterCustoms.filterGroups.find(fgroup => fgroup.id === filter.categoryGuid);
            vm.filterCustomChildSelected(filter, filterCustomGroup);
        } else {
            let filterGroup = vm.hotelFilters.filterGroups.find(fgroup => fgroup.id === filter.groupId);
            vm.filterChildSelected(filter, filterGroup);
        }


    }

    vm.resetRangeFilter = (reload = true) => {
        vm.pricesSelected = [];
        vm.paramsFilter.filterPriceMin = undefined;
        vm.paramsFilter.filterPriceMax = undefined;
        vm.filterSelection.min = vm.hotelFilters.filterCustoms.prices.min;
        vm.filterSelection.max = vm.hotelFilters.filterCustoms.prices.max;
        setBoxRangeValue(vm.filterSelection.min, vm.filterSelection.max);

        if (reload) {
            onReloadHotels();
        }
    }

    /*****
     * 
     * Private functions
     * 
     *****/

    function initializeElements() {

        onSuccessPreviewHotels({ data: hotelResponse });

        if (!settings.rb) {
            if (settings.site.siteName == "pricetravel" && userLocation.country == "MX") {
                getSkillBase();
            }
            onSearchPaymentMethod();
            onLoadHotelsFilters();
        }
    }

    function initializeFilters() {
        allowRequest = false;

        const filters = _paramQuery.filters?.split(',') || [];

        if (filters.length && filters.some(filter => /freecancellation(_allowed)?/.test(filter))) {
            const freeCancellation = vm.hotelFilters?.filterCustoms?.filterGroups?.find(
                group => group?.uri === 'freecancellation'
            );
    
            const freeCancellationAllowed = freeCancellation?.filters?.find(
                filter => filter?.display === 'freecancellation_allowed'
            );
    
            if (freeCancellationAllowed) {
                freeCancellationAllowed.isSelected = true;
                vm.filterCustomChildSelected(freeCancellationAllowed, freeCancellation);
            }
        }
    }

    function initializeValues() {
        initialCampaignToken();
        vm.profileId = _paramQuery.profileId || _paramQuery.ProfileId || _paramQuery.profileid || null;

        if (isMobile) {
            params.pageSize = config.listItemsMobileCount;
        }
    }

    function initialCampaignToken() {
        let campaignTokenApi = _paramQuery.campaignTokenApi || _paramQuery.CampaignTokenApi || _paramQuery.campaigntokenapi || null;
        _campaignToken = _paramQuery.CampaignToken || _paramQuery.campaignToken || campaignTokenApi;

        if (_user && _user.firebaseId) {
            _campaignToken = config.campaignTokenUser;
        }
    }
    function initializeSliders(filterPrice) {

        let minValue = vm.paramsFilter.filterPriceMin || filterPrice.min;
        let maxValue = vm.paramsFilter.filterPriceMax || filterPrice.max;
        if (vm.pricesSelected.length === 0 && filterPrice.min && filterPrice.max) {
            minValue = filterPrice.min;
            maxValue = filterPrice.max;
        }

        let options = {
            min: filterPrice.min,
            max: filterPrice.max,
            value: [minValue, maxValue],
            //tooltip: 'always'
        };

        let prices = document.getElementById("prices");
        let prices_map = document.getElementById("prices_map");


        if (prices) {

            if (slide_price) {
                slide_price.destroy();
            }

            slide_price = new Slider("#prices", options);
            slide_price.on("slide", onChangeRangePrice);
            slide_price.on("slideStop", onChangeWithReloadRangePrice);
        }

        if (prices_map) {

            if (slide_price_map) {
                slide_price_map.destroy();
            }

            slide_price_map = new Slider("#prices_map", options);
            slide_price_map.on("slide", onChangeRangePrice);
            slide_price_map.on("slideStop", onChangeWithReloadRangePrice);
        }

        if (!vm.paramsFilter.filterPriceMin && !vm.paramsFilter.filterPriceMax) {
            vm.filterSelection.min = minValue;
            vm.filterSelection.max = maxValue;
        }

        if (vm.pricesSelected.length && (vm.filterSelection.max <= filterPrice.max)) {
            setBoxRangeValue(vm.filterSelection.min, vm.filterSelection.max);
        } else {
            setBoxRangeValue(filterPrice.min, filterPrice.max);
        }

        if (vm.filterSelection.min < filterPrice.min || vm.filterSelection.max > filterPrice.max) {
            vm.paramsFilter.filterPriceMin = undefined;
            vm.paramsFilter.filterPriceMax = undefined;
            vm.filterSelection.min = filterPrice.min;
            vm.filterSelection.max = filterPrice.max;
        }

        $('.min_slider_box').on('keyup', (e) => { onKeyupSliderBox(e, 'min') });
        $('.max_slider_box').on('keyup', (e) => { onKeyupSliderBox(e, 'max') });
        $('.min_slider_box').on('change', (e) => { onChangeSliderBox(e, 'min') });
        $('.max_slider_box').on('change', (e) => { onChangeSliderBox(e, 'max') });

        const sliderMinHandle = document.querySelector('.slider-handle.min-slider-handle.round');
        const sliderMaxHandle = document.querySelector('.slider-handle.max-slider-handle.round');
        if (sliderMinHandle && sliderMaxHandle) {
            sliderMinHandle.setAttribute('aria-label', 'Valor mínimo');
            sliderMaxHandle.setAttribute('aria-label', 'Valor máximo');
        }
    }

    function onReloadHotels() {
        _globalUtil.windowScrollTop();
        if (isOpenMap) {
            return _googleMap.initialize(true);
        }

        params.page = vm.pagination.currentPage + 1;
        vm.paramsFilter.page = vm.pagination.currentPage + 1;

        onLoadHotelsFilters();
    }

    function updateQueryPathFilter() {
        const path = window.location.pathname;
        let search = window.location.search;
        const pathSplit = path.split("/");

        search = updateSearch(search);
        const hotel = pathSplit[1];
        const destino = pathSplit[2];
        let filtros = "";
        if (vm.filtersSelectedArrayString.length) {
            filtros = `/${vm.filtersSelectedArrayString.join("/")}`;
        }
        const url = `/${hotel}/${destino}${filtros}`

        window.history.pushState(null, "any", url);
    }


    function updateHotelTags(groupHotels) {
        groupHotels.forEach(hotels => {
            hotels.forEach(hotel => {
                if (hotel.services && Array.isArray(hotel.services)) {
                    hotel.services = hotel.services
                        .map(tag => findReviewByText(tag.name))
                        .filter(Boolean)
                        .sort((a, b) => a.order - b.order)
                        .reduce((acc, current) => {
                            if (!acc.some(service => service.order === current.order)) {
                                acc.push(current);
                            }
                            return acc;
                        }, [])
                        .slice(0, 3)
                        .map(service => {
                            if (service.order === 1) {
                                service.text = 'Wi-fi';
                            }
                            return service;
                        });
                }
            });
        });
    }

    function findReviewByText(text) {
        for (const [key, review] of Object.entries(AMENITIES_DESCRIPTIONS)) {
            if (review.es.includes(text) || review.en.includes(text)) {
                return {
                    key,
                    text: config.language === "es" ? review.es : review.en,
                    ...review
                };
            }
        }
        return null;
    }

    function getSkillBase() {
        paramsSkillBase.adultQuantity = _paxes.adults;
        paramsSkillBase.kidQuantity = _paxes.children;
        paramsSkillBase.checkInDate = params.checkIn;
        paramsSkillBase.checkOutDate = params.checkOut;
        $http.get(config.endPoints.skillBaseUrl, { params: paramsSkillBase }).then(onSuccessSkillBase, onErrorLog);
    }

    function getAvailabilityReasons(idHotel) {

        let qr = {
            rid: idHotel,
            checkin: box.checkIn,
            checkout: box.checkOut,
            p: _globalUtil.getPaxToMessages(newPax)
        };

        $http.get(config.endPoints.availabilityReasonsUrl, { params: qr }).then(onSuccessAvailabilityReasons, onErrorLog);
    }

    function getRecommendedDates(idhotel, reasons) {

        let qr = {
            checkIn: box.checkIn,
            checkOut: box.checkOut,
            availabilityReasons: reasons,
            idHotel: idhotel,
            channelId: config.channelFac,
            token: paramsRates.CampaignToken ? paramsRates.CampaignToken : "",
            adults: _paxes.adults,
            kids: _paxes.children,
            providerId: 1,
            placeIdDestination: place.id,
            kidsAge: _globalUtil.getAgeKids(box)
        };

        $http.post(config.endPoints.recommenderDatesUrl, qr).then(onRenderRecomendedDates, onErrorLog);
    }

    function onSearchPaymentMethod() {
        if (vm.responsePayment.fixedPayments.length || vm.responsePayment.monthInterestFree.length) { return; }
        let params = {
            channel: settings.site.channel,
            language: cultureData.internalCultureCode,
            currency: settings.site.currency
        }
        $http.get(settings.site.endPoints.paymentMethodUrl, { params: params }).then(onSuccessPaymentMethods, onErrorLog)

    }

    function loadFilters(payload) {

        if (!payload) {
            return;
        }

        const indexOfGroup = payload.findIndex(x => x.id === 3);
        if (indexOfGroup > -1) {
            payload[indexOfGroup].filters = payload[indexOfGroup].filters
                .filter(filter => filter.id === 24 || filter.id === 20 || filter.id === 70)
                .sort((a, b) => {
                    const order = [24, 70, 20];
                    return order.indexOf(a.id) - order.indexOf(b.id);
                });
        }
        vm.filters = setIsSelect(payload) || [];
    }

    function loopHotels(filterResponse) {

        let lengthHotel = vm.hotels.length;
        let pax = fn.mapPaxToUrl(box.pax);
        let paxIsRequote = fn.mapPaxToUrl(newPax.paxes);
        let query = "";
        let queryIsRequote = "";
        let paramsUri = {
            checkin: box.checkIn,
            checkout: box.checkOut,
            source: _paramQuery.source ? _paramQuery.source : "",
            promotions: _paramQuery.promotions
        };
        let checkIn = fn.getDate(box.checkIn);
        let checkOut = fn.getDate(box.checkOut);
        let nights = fn.diffBetweenDays(checkIn, checkOut);


        if (_campaignToken) {
            paramsUri.CampaignToken = _campaignToken;
        }
        if (_paramQuery.coupon) {
            paramsUri.coupon = _paramQuery.coupon;
        }

        let paramsUriConcat = { ...paramsUri, ...pax };
        let paramsUriConcatRequote = { ...paramsUri, ...paxIsRequote };

        query = fn.objectToQueryParams(paramsUriConcat);
        queryIsRequote = fn.objectToQueryParams(paramsUriConcatRequote);

        let objectIDs = [];
        let indexName = "";
        for (let i = 0; i < lengthHotel; i++) {
            let hotel = vm.hotels[i];
            indexName = hotel.indexName;
            objectIDs.push(hotel.hotelId.toString());
            let queryCurrent = hotel.isRequote ? queryIsRequote : query;
            let indexHotelPla = config.hotelsIdsPR ? config.hotelsIdsPR.find(x => x == hotel.hotelId) : -1;

            hotel.url = _listUtil.getUrlToDetail(hotel, queryCurrent);
            hotel.position = (vm.pagination.currentPage * vm.pagination.pageSize) + i + 1;
            hotel.page = vm.pagination.currentPage + 1;
            hotel.rooms = hotel.isRequote ? paxIsRequote.rooms : pax.rooms;
            hotel.nights = nights;
            hotel.notAmount = config.hotelsTagLogin ? config.hotelsTagLogin.listA.includes(hotel.hotelId) : false;// LIST A Mensaje sin monto.
            hotel.notMessage = config.hotelsTagLogin ? config.hotelsTagLogin.listB.includes(hotel.hotelId) : false;// LIST B Sin mensaje.
            hotel.isLoading = filterResponse //mostrar el skeleton cuando no tenga tarifa cargada de filtros
            hotel.sourceTriGooHotelsIds = indexHotelPla > -1;
            hotel.isFavorite = false;
            hotel.showPromotion = false;
            hotel.showHotSBlackFri = false;
            hotel.skeleton = generateRandomSkeleton();
            if(hotel.taxes){
             hotel.taxes.feesAmountTotal = config.feesIncludes ?   hotel.taxes?.breakdown?.breakDowns
                        .filter(fee => config.breakDownExcludedList.includes(fee.title))
                        .reduce((acc, fee) => acc + fee.amount, 0) : 0;
                 hotel.taxes.feesAmountTotalForNight =  config.feesIncludes ? hotel.taxes.feesAmountTotal /  hotel.nights : 0;
            }

            hotel.phone = window.__pt.phoneForHotel && window.__pt.phoneForHotel[hotel.hotelId] && window.__pt.phoneForHotel[hotel.hotelId].length > 0
                ? window.__pt.phoneForHotel[hotel.hotelId][0]
                : null;
            if (config.showPromotionsTags) {
                var objectKeysPromo = _globalUtil.getShowPromotion(hotel.taxes);
                hotel.showPromotion = objectKeysPromo.activeKey;
                hotel.showHotSBlackFri = objectKeysPromo.HotSBlackFri;
            }


        }
        if (objectIDs.length > 0) {
            algoliaTraker.viewedObjectIDs(objectIDs, indexName || "searchHotelPlacesUS");
        }
        

        window.__pt.hotels = vm.hotels;
    }



    function matchHotelWithRate() {
        let lengthHotel = vm.hotels.length;
        for (let i = 0; i < lengthHotel; i++) {
            let hotel = vm.hotels[i];
            hotel.price = null;
            hotel.taxes = null;
            hotel.isRequote = false;

            if (rates && rates.rates && rates.rates[`h${hotel.hotelId}`]) {
                hotel.price = rates.rates[`h${hotel.hotelId}`];
            }

            if (rates && rates.tax && rates.tax[`h${hotel.hotelId}`]) {
                hotel.taxes = rates.tax[`h${hotel.hotelId}`];
                hotel.isRequote = hotel.taxes.isRequote ? true : false;
                hotel.showPromotion = false;
                hotel.showHotSBlackFri = false;

                if (config.showPromotionsTags) {
                    var objectKeysPromo = _globalUtil.getShowPromotion(hotel.taxes);
                    hotel.showPromotion = objectKeysPromo.activeKey;
                    hotel.showHotSBlackFri = objectKeysPromo.HotSBlackFri;
                }
                hotel.taxes.feesAmountTotal = config.feesIncludes ? hotel.taxes?.breakdown?.breakDowns
                        .filter(fee => config.breakDownExcludedList.includes(fee.title))
                        .reduce((acc, fee) => acc + fee.amount, 0) : 0;
                 hotel.taxes.feesAmountTotalForNight = config.feesIncludes ? hotel.taxes.feesAmountTotal /  hotel.nights : 0;
            }

        }
    }

    function onLoadRates() {
        if (_user && _user.firebaseId) {
            _campaignToken = config.campaignTokenUser;
        } else {
            initialCampaignToken();
        }

        vm.nightsList = box.pax.length || 0;
        let hotelsId = _listUtil.getHotelsIds(vm.hotels);

        paramsRates.hotelIds = hotelsId.join(",");
        paramsRates.rooms = newPax.paxesFormat;
        paramsRates.CampaignToken = _campaignToken;
        paramsRates.profileId = vm.profileId;
        paramsRates.userKey = _userKey;
        paramsRates.mobile = isMobile;
        paramsRates.responseTimeout = 30000;
        paramsRates.checkIn = params.checkIn;
        paramsRates.checkOut = params.checkOut;
        paramsRates.login = _isLogin;
        paramsRates.source = box.source ? box.source : "SPA-Hotel-List";
        paramsRates.filters = vm.filtersSelectedArrayString.join(",");
        paramsRates.channelId = isMobile ? config.chkSourceMobile : config.chkSourceDesktop;
        setInfoRooms(paramsRates, box.pax);
        $http.get(config.endPoints.quoteUrl, { params: paramsRates, headers: getHeaders() }).then(onLoadRatesRequote, onErrorRates);
    };

    function onLoadRatesPreLogin() {
        let hotelsId = vm.hotels.map(hotel => hotel.hotelId);
        if (!settings.rb && hotelsId.length > 0) {
            let paramsLogin = { ...paramsRates };
            paramsLogin.hotelIds = hotelsId.join(",")
            paramsLogin.CampaignToken = null;
            paramsLogin.userKey = null;
            paramsLogin.login = !_isLogin;
            paramsLogin.channelId = isMobile ? (config.channelConfigDefault?.mobileLogin.channelId != 0 ? config.channelConfigDefault.mobileLogin.channelId : paramsLogin.channelId): (config.channelConfigDefault?.desktopLogin.channelId != 0 ? config.channelConfigDefault.desktopLogin.channelId : paramsLogin.channelId);
            $http.get(config.endPoints.quoteUrl, { params: paramsLogin, headers: getHeaders() }).then(onSuccessLoadRatesWithLogin, onErrorLog);
        }

    };
    function updateUrlParams(url, newCheckin, newCheckout) {
        const urlObj = new URL(url);
        const paxes = fn.mapPaxToUrl(newPax.paxes);
        //urlObj.searchParams.set('isFromDateRecommended', true);
        urlObj.searchParams.set('checkin', newCheckin);
        urlObj.searchParams.set('checkout', newCheckout);
    
        for (const [key, value] of Object.entries(paxes)) {
            urlObj.searchParams.set(key, value);
        }
    
        return urlObj.toString();
    }
    

    function onLoadHotelsFilters() {

        if (_user && _user.firebaseId) {
            _campaignToken = config.campaignTokenUser;
        } else {
            initialCampaignToken();
        }

        let pax = newPax.paxesFormat.map(item => {
            if (!item.includes('|') && /^[0-9]+$/.test(item)) {
                item += '/n';
            }
            let replacedItem = item.replace(/\|/g, '/');
            return replacedItem;
        }).join('/');

        vm.hotelFilters.loading = true;
        vm.paramsFilter.pax = `/${pax}`;
        vm.paramsFilter.getexternalavailability = _listUtil.getExternalAvailability();
        vm.paramsFilter.channel = config.channelFac;
        vm.paramsFilter.channelId = config.channelFac;
        vm.paramsFilter.checkIn = params.checkIn;
        vm.paramsFilter.checkOut = params.checkOut;
        vm.paramsFilter.profileId = vm.profileId;
        vm.paramsFilter.CampaignToken = _campaignToken;
        vm.paramsFilter.login = _isLogin
        vm.paramsFilter.mobile = isMobile;
        vm.paramsFilter.filters = vm.filtersSelectedArrayString.join(",");
        vm.paramsFilter.filtersCustom = vm.filterCustomList.map(f => f.uri).join(",");
        vm.paramsFilter.userKey = _userKey;
        vm.paramsFilter.source = box.source ? box.source : "SPA-Hotel-List";
        vm.paramsFilter.searchDatesRecommended = true;
        vm.paramsFilter.uriPlace = hotelResponse.place.uri;
        vm.paramsFilter.applicable = true;
        vm.paramsFilter.promotions = $location.search().promotions ? $location.search().promotions.split(',') : null;


        let paramsFilter = { ...vm.paramsFilter };

        if (paramsFilter.filterPriceMax) {
            paramsFilter.filterPriceMax = paramsFilter.filterPriceMax / exchange.rate;
        }

        if (paramsFilter.filterPriceMin) {
            paramsFilter.filterPriceMin = paramsFilter.filterPriceMin / exchange.rate;
        }

        if (filtersLoaded) {
            vm.loading = true;
        }

        $http.get(config.endPoints.filterUrl, { params: paramsFilter, loginFlag: _isLogin, headers: getHeaders() }).then(onSuccessFilterResponse, onErrorLog);
    }


    function onSuccessAvailabilityReasons({ data }) {
        console.log(data);
    }

    function generateRandomSkeleton() {
        const getRandom = (max = 165, min = 80) => Math.floor(Math.random() * (max - min + 1)) + min;
        return {
            skeleton1: getRandom(130, 115) + "px",
            skeleton2: getRandom(120, 100) + "px",
            skeleton3: getRandom(150, 135) + "px",
            skeleton4: getRandom(95, 80) + "px",
            skeleton5: getRandom(110, 100) + "px",
        };
    }
    /***
     * 
     *  Filters
     */

    function assignFilters(objFiltred) {
        vm.filtersSelectedArray = objFiltred;
        vm.filtersSelectedArrayString = objFiltred.map(item => item.uri);
        updatePathFilter();

        if (vm.pricesSelected.length < 1) {
            vm.resetRangeFilter(false);
        }

        onReloadHotelsFilter();
        _globalUtil.windowScrollTop();
    }

    function onReloadHotelsFilter() {

        if (isOpenMap) {
            return _googleMap.initialize();
        }

        params.page = 1;
        onLoadHotelsFilters();
    }

    function updatePathFilter() {
        const path = window.location.pathname;
        let search = window.location.search;
        const pathSplit = path.split("/");

        search = updateSearch(search);
        const hotel = pathSplit[1];
        const destino = pathSplit[2];
        let filtros = "";
        if (vm.filtersSelectedArrayString.length) {
            filtros = `/${vm.filtersSelectedArrayString.join("/")}`;
        }
        const url = `/${hotel}/${destino}${filtros}${search}`

        window.history.pushState(null, "any", url);
    }

    function updateSearch(search) {
        const urlSearchParams = new URLSearchParams(window.location.search);
        const params = Object.fromEntries(urlSearchParams.entries());

        if (parseInt(params.page) > 1) {
            params.page = 1;
            search = '?' + Object.keys(params).map(key => key + '=' + params[key]).join('&');
        }
        return search;
    }

    function setIsSelect(filtersResponse) {
        const selected = []
        let newFilters = [];
        if (filtersResponse) {
            newFilters = filtersResponse.map((filters, ind) => {
                let selectedChild = filters.filters ? filters.filters.find(item => item.isSelected) : null;
                filters["isSelected"] = selectedChild ? false : true;
                if (filters.filters) {
                    const newFiltersChild = filters.filters.map(itemFilter => {
                        itemFilter["isMultiselect"] = filters.isMultiselect;
                        if (itemFilter.isSelected) {
                            selected.push(itemFilter);
                        }
                        return itemFilter;
                    });
                    filters["filters"] = newFiltersChild;
                }
                if (ind === 0) {
                    filters.filters = _listUtil.ptUsaProvisionalOrder(filters.filters);
                }

                return filters;
            })
        }


        vm.filtersSelectedArray = selected;
        vm.filtersSelectedArrayString = selected.map(item => item.uri);

        return newFilters;
    }


    /*
     Callbacks
     */
    function onSuccessPreviewHotels(data, filterResponse = false) {
        _globalUtil.windowScrollTop();
        let response = data.data;
        if (response && response.token) {
            vm.loading = false;
            vm.showAd = true;
            currentHotelList = response;
            vm.hotels = currentHotelList.hotels || [];
            vm.hotels = vm.hotels.filter(ht => ht.mainPhoto && ht.mainPhoto.cloudUri && ht.mainPhoto.cloudUri.length);
    
            vm.groupedHotel = _listUtil.groupItems(vm.hotels, _size);


            vm.groupedHotel.forEach(groups => {
                groups.forEach(group => {
                    if (!group.price || group.price == 0) {
                        onSuccessDateRecommended(group);
                    }
                });
            });

            vm.pagination = _listUtil.buildPaginator(currentHotelList.totalHotels, vm.hotels.length, vm.pagination.currentPage);
            vm.pagination.totalHotels = currentHotelList.totalHotels;
            vm.pagination.pageSize = config.listItemsCount;
            loadFilters(currentHotelList.filterGroups);
            loopHotels(!filterResponse);

            if (config.showCardTags) {
                updateHotelTags(vm.groupedHotel);
            }

            if (!filterResponse) {
                onLoadRates();
                onLoadRatesPreLogin();
            }

            /*** Se ejecutara siempre que sea una respuesta por el servicio de filtros ***/
            if (filterResponse) {
                showAvailabilityReason();
            }

            if (vm.hotels.length && !settings.rb)
                setRecentDestination(vm.hotels);
        } else {
            onError(data);
        }
    }

    function onSuccessDateRecommended(hotel) {
        const language = config.culture.split('-')[0];
        if (!hotel.dateRecommended)
            return;

        hotel.dateRecommended.forEach(reservation => {
            const formattedCheckin = formatDate(reservation.checkin, language);
            const formattedCheckout = formatDate(reservation.checkout, language);
            const dateDifference = calculateDateDifference(reservation.checkin, reservation.checkout);

            reservation.checkinDayOfWeek = formattedCheckin.dayOfWeek;
            reservation.checkinDayAndMonth = formattedCheckin.dayAndMonth;
            reservation.checkoutDayOfWeek = formattedCheckout.dayOfWeek;
            reservation.checkoutDayAndMonth = formattedCheckout.dayAndMonth;
            reservation.dateDifference = dateDifference;
        });
    }

    function onSuccessLoadRatesWithLogin(response) {
        responseWithLogin.loaded = true;
        responseWithLogin.data = response.data.tax;
        mergeLoginRates();
    }

    function mergeLoginRates() {

        let factor = _isLogin ? -1 : 1;
        let taxInfo = responseWithLogin?.data ?? [];
        let hotelsLength = vm.groupedHotel.length;

        for (let i = 0; i < hotelsLength; i++) {
            let group = vm.groupedHotel[i];
            let groupLength = group.length;

            for (let j = 0; j < groupLength; j++) {
                let hotel = group[j];
                let tax = taxInfo[`h${hotel.hotelId}`];

                if (tax && !config.hotelsTagLogin.listB.includes(hotel.hotelId)) {
                    let totalRoomRate = hotel.taxes ? hotel.taxes.totalRoomRate : 0;
                    let price_login = tax ? tax.totalRoomRate : totalRoomRate;
                    let saved = (totalRoomRate - price_login) * factor;
                    let discountPercentage = ((saved * 100) / totalRoomRate);
                    let isValid = discountPercentage > 2 && saved >= fn.thresHold(config.code);
                    hotel.price_with_login = price_login * factor;
                    hotel.total_saved = saved > 0 && isValid ? saved : 0;

                }

            }
        }
    }

    function onLoadRatesRequote(value, isRetry = false) {

        let hotelsId = _listUtil.getHotelsIds(vm.hotels);
        let paxesRates = box.pax;
        let dataRates = Object.keys(value.data.rates);
        hotelIdWithOutRates = hotelsId.filter((id) => !dataRates.includes(`h${id}`))
        let newPax = matchFamilyRule(paxesRates);

        vm.nightsList = box.pax.length || 0;

        if (!isRetry) {
            setInfoRooms(paramsRates, paxesRates);
        }

        /** Si no hay hoteles se respalda la respuesta de quote para recotizar posteriormente en filter*/
        if (!hotelIdWithOutRates.length) {
            retryData = value;
        }

        if (hotelIdWithOutRates.length && newPax && newPax.validRequote) {
            vm.paxData = newPax.paxes;
            dataRatesFetch = value;
            let cloneParamsRates = fn.clone(paramsRates);
            cloneParamsRates.hotelIds = hotelIdWithOutRates.join(",");
            cloneParamsRates.rooms = newPax.paxesFormat;

            setInfoRooms(cloneParamsRates, paxesRates);

            $http.get(config.endPoints.quoteUrl, { params: cloneParamsRates, headers: getHeaders() }).then(onSuccessGetRatesRequote, onErrorLog);

        } else {
            if (!isRetry) {
                onSuccessGetRates(value);
            }
        }

    }

    function onSuccessFilterResponse(response) {
        let data = response.data || {};
        if (response.status == 200) {

            filtersLoaded = true;
            vm.loading = false;
            vm.loadingRates = false;
            vm.hotelFilters = data;
            vm.hotelFilters.loading = false;
            vm.paramsFilter.searchDatesRecommended = false;
            vm.hotelFilters.filterCustoms.filterGroups = _listUtil.setIsSelectCustomFilter(data.filterCustoms.filterGroups);
            vm.popularFilters = vm.hotelFilters.filterCustoms.filterPopular || [];
            vm.hotelFilters.filterCustoms.prices.min = (vm.hotelFilters.filterCustoms.prices.min) * exchange.rate;
            vm.hotelFilters.filterCustoms.prices.max = (vm.hotelFilters.filterCustoms.prices.max) * exchange.rate;
            updateFilterCustom();
            initializeSliders(vm.hotelFilters.filterCustoms.prices);
            onSuccessPreviewHotels(response, true);

            /** Solo ejecutar cuando se aplican o eliminan filtros */
            if (filtersInitReq) {
                onLoadRatesPreLogin();
            }

            if (allowRequest){
                initializeFilters();
            }

            filtersInitReq = true;

            /** si la recotización intento ejecutarse antes de la respuesta de filtros */
            if (retryData) {
                onLoadRatesRequote(retryData, true);
            }
            mergeLoginRates();
        }
    }

    function onSuccessGetRates(data) {

        let response = data.data;
        if (response) {
            rates = response || {};

            if (!filtersLoaded) {
                matchHotelWithRate();
            }

            vm.loadingRates = false;
            tracker.afterLoadRates();
            tracker.viewItemList(vm.hotels);
            if (response.campaignToken && response.campaignToken.length) {
                $location.search('ctInternal', response.campaignToken)
            }
            _campaignToken = response.campaignToken;
            showAvailabilityReason();

        } else {
            onError(data);
        }

        $('.seo-list-init').remove();



    }

    function onSuccessGetRatesRequote(valueRequote) {
        let resultRequote = familyRequote(dataRatesFetch, valueRequote);
        onSuccessGetRates(resultRequote);
        loopHotels();
        mergeLoginRates();
    }

    function onSuccessAvailabilityReasons({ data }) {
        if (data && data.status === "ok") {
            let firstHotel = vm.hotels[0] || {};
            getRecommendedDates(firstHotel.hotelId, data.message);
        }
    }

    function onRenderRecomendedDates(res) {
        if (res && res.data && res.data.message) {
            const hotel = vm.hotels[0];
            let recommendedDates = Object.values(res.data.message);

            hotel.checkIn = params.checkIn;
            hotel.checkOut = params.checkOut;

            hotel.recommendedDates = recommendedDates.map(item => {
                return {
                    ...item,
                    people: _paxes.adults + _paxes.children,
                    rooms: box.pax.rooms,
                    hotelId: hotel.hotelId,
                    hotelName: hotel.name
                }
            });

        }
    }

    function onSuccessSkillBase(data) {
        let response = data.data;
        if (response.message == _successSkillBase) {
            skillBase = response.result;
            vm.phoneBase = skillBase.did;
            $(headerPhoneId).attr('href', `tel:${vm.phoneBase}`);
            $('.skillbase_p').text($filter('tel')(vm.phoneBase));
        } else {
            onErrorLog(data);
        }
    }

    function onSuccessPaymentMethods(response) {
        const data = response.data || [];
        const monthInterestFree = data.monthInterestFree ? data.monthInterestFree.options : [];
        const groupedPayments = monthInterestFree.reduce((groups, item) => {
            const maxMonths = Math.max(...item.paymentPlans);
            if (!groups[maxMonths]) {
                groups[maxMonths] = [];
            }
            groups[maxMonths].push(item);
            return groups;
        }, {});
        const sortedEntries = Object.entries(groupedPayments).sort(([keyA], [keyB]) => Number(keyB) - Number(keyA));
        const paymentMethods = {
            fixedPayments: data.fixedPayments
                .map(fixedPayments => fixedPayments.options)
                .reduce((prev, curr) => prev.concat(curr), []),
            monthInterestFree: sortedEntries,
            quotasPayments: data.quotasPayments ? data.quotasPayments.options : [],
            otherPayments: data.otherPayments ? data.otherPayments.options : [],
            loading: false,
            title: data.quotasPayments ? data.quotasPayments.title : "",
            maxmsi: data.monthInterestFree ? data.monthInterestFree.title : ""
        };
        vm.responsePayment = paymentMethods;
        vm.monthInterestData = data.monthInterestFree || [];
    }

    function onChangeRangePrice(values) {
        $scope.$apply(function () {
            var min = values[0] || 0;
            var max = values[1] || 0;
            vm.filterSelection.min = min;
            vm.filterSelection.max = max;
            setBoxRangeValue(min, max);
        });
    }

    function onKeyupSliderBox(e, prop) {
        let strnum = e.target.value + "".replaceAll(",", "");
        if (strnum.includes(",")) {
            return;
        }

        let valor = Math.abs(parseInt(strnum));

        if (prop === 'min' && valor < vm.filterSelection.min) {
            return;
        }

        if (prop === 'max' && valor + 1 < vm.filterSelection.min) {
            return;
        }

        vm.filterSelection[prop] = isNaN(valor) ? vm.hotelFilters.filterCustoms.prices[prop] : valor;
        slide_price.setValue([vm.filterSelection.min, vm.filterSelection.max], true, true);


        if (window.screen.width < 920) {
            vm.onSubmitFilter();
        }
    }

    function onChangeSliderBox() {
        setBoxRangeValue(vm.filterSelection.min, vm.filterSelection.max);
    }

    function setBoxRangeValue(min, max) {
        const thousandSeparator = window.__pt.settings.site.cultureSite == "es-co" ? "." : ",";
        $('.min_slider_box').val( (Math.round(min) + "").replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator));
        $('.max_slider_box').val((Math.round(max) + "").replace(/\D/g, "").replace(/\B(?=(\d{3})+(?!\d))/g, thousandSeparator));
    }

    function onChangeWithReloadRangePrice(value) {
        onChangeRangePrice(value);
        if (slide_price_map) {
            slide_price_map.setValue([vm.filterSelection.min, vm.filterSelection.max], true, true);
        }

        if (slide_price) {
            slide_price.setValue([vm.filterSelection.min, vm.filterSelection.max], true, true);
        }

        if (window.screen.width < 920) {
            vm.onSubmitFilter();
        }
    }

    /**
     * Callbacks Errors
     */
    function setFilterPhillRange() {

        vm.paramsFilter.filterPriceMin = vm.filterSelection.min;
        vm.paramsFilter.filterPriceMax = vm.filterSelection.max;

        if (Math.round(vm.hotelFilters.filterCustoms.prices.min) === vm.filterSelection.min) {
            vm.paramsFilter.filterPriceMin = vm.hotelFilters.filterCustoms.prices.min;
        } else if (Math.round(vm.hotelFilters.filterCustoms.prices.max) === vm.filterSelection.max) {
            vm.paramsFilter.filterPriceMax = vm.hotelFilters.filterCustoms.prices.max;
        }

        if (
            Math.round(vm.filterSelection.min) > Math.round(vm.hotelFilters.filterCustoms.prices.min)
            || Math.round(vm.filterSelection.max) < Math.round(vm.hotelFilters.filterCustoms.prices.max)
        ) {
            const minFormatted = $filter('currency')(vm.filterSelection.min, 0, false);
            const maxFormatted = $filter('currency')(vm.filterSelection.max, 0, false);
            vm.pricesSelected = [
                {
                    display: `De: $${minFormatted+ ""} a 
                    $${maxFormatted}`
                }
            ];
        } else {
            vm.pricesSelected = [];
        }
    }

    function onError(data) {
        let response = data.data;
        vm.loadingRates = false;
        vm.loading = false;
        vm.hasError = true;
        vm.hotels = [];
        vm.groupedHotel = [];
        vm.filters = [];
        tracker.errorQuote(response, 'Content Facade');
    }

    function onErrorRates(data) {
        let response = data.data;
        vm.loadingRates = false;
        vm.loading = false;
        vm.loadingRefresh = false;
        tracker.errorQuote(response, 'Rates');
        console.log("error", response);
    }

    function onErrorLog(data) {
        console.log(data)
    }

    /**
     * Misc
     */


    function showAds() {
        fn.onViewComponent("#ad-skyscraper-1", onShowAds, { ad: 1 });
        fn.onViewComponent("#ad-skyscraper-2", onShowAds, { ad: 2 });
        fn.onViewComponent("#ad-skyscraper-3", onShowAds, { ad: 3 });
    }

    function onShowAds(param) {
        tracker.ad(param.ad, _paxes, paramsRates)
    }

    function setRecentDestination(list) {
        let registry = StorageService.get(settings.recentDestinations) || [];
        const exists = registry.findIndex(item => item.place.id == place.id);
        if (exists > -1) {
            const temp = registry[exists];
            temp.box = box;
            temp.campaignTokenApi = _campaignToken;
            registry.splice(exists, 1);
            registry.unshift(temp);
        } else {
            registry.unshift({
                box: box,
                place: place,
                IdG: place.id,
                campaignTokenApi: _campaignToken,
                icon: config.cloudCdn + "/assets/img/Hotel_icon.svg",
                title: ln.hotel_lang,
                origin: "H",
                image: list[0].mainPhoto.cloudUri ? list[0].mainPhoto.cloudUri : ""
            });
        }

        if (registry.length > settings.limitDestinations) {
            registry = registry.slice(0, settings.limitDestinations);
        }

        StorageService.set(settings.recentDestinations, registry);
    }

    function setInfoRooms(paramsRoomRates, paxes) {
        let checkIn = fn.getDate(paramsRoomRates.checkIn);
        let checkOut = fn.getDate(paramsRoomRates.checkOut);
        let nights = fn.diffBetweenDays(checkIn, checkOut);
        let rooms = paxes.rooms;
        let totalPax = paxes.adults + paxes.children;
        vm.boxData = { rooms, nights, totalPax };
    }

    function updateFilterCustom() {
        var filtersCustom = vm.hotelFilters.filterCustoms || {};

        if (!vm.paramsFilter.orderBy && filtersCustom.orderBy && filtersCustom.orderBy.length) {
            vm.paramsFilter.orderBy = filtersCustom.orderBy[0].code;
            vm.textOrderBy = filtersCustom.orderBy[0].display;
        }

        $('.collapse_filter').collapse('show');
        vm.toggleRange = true;
    }

    function changeTextTitle() {
        vm.title = `${_pref} ${place.displayText}`;
    }

    function showAvailabilityReason() {
        let firstHotel = vm.hotels[0] || {};

        if (firstHotel && !firstHotel.price && config.recommenderDatesActive) {
            getAvailabilityReasons(firstHotel.hotelId);
        }
    }
}
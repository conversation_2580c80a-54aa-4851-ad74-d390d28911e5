﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo
{
    [ProtoContract]
    public class SeoResponse
    {
        [ProtoMember(1)]
        public SeoData Seo { get; set; }

        public SeoResponse()
        {
            Seo = new SeoData();
        }
    }

    [ProtoContract]
    public class SeoData
    {
        [ProtoMember(1)]
        public MetaData Meta { get; set; }

        [ProtoMember(2)]
        public List<FaqItem> Faqs { get; set; }

        public SeoData()
        {
            Meta = new MetaData();
            Faqs = new List<FaqItem>();
        }
    }

    [ProtoContract]
    public class MetaData
    {
        [ProtoMember(1)]
        public string Title { get; set; }

        [ProtoMember(2)]
        public string Description { get; set; }

        [ProtoMember(3)]
        public string Canonical { get; set; }

        [ProtoMember(4)]
        public string H1 { get; set; }

        [ProtoMember(5)]
        public List<string> Paragraphs { get; set; }

        public MetaData()
        {
            Title = string.Empty;
            Description = string.Empty;
            Canonical = string.Empty;
            H1 = string.Empty;
            Paragraphs = new List<string>();
        }
    }

    [ProtoContract]
    public class FaqItem
    {
        [ProtoMember(1)]
        public string Question { get; set; }

        [ProtoMember(2)]
        public string Answer { get; set; }

        public FaqItem()
        {
            Question = string.Empty;
            Answer = string.Empty;
        }
    }
}

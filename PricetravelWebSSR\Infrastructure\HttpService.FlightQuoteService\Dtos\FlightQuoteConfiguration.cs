﻿namespace PricetravelWebSSR.Infrastructure.HttpService.FlightQuoteService.Dtos
{
    public class FlightQuoteConfiguration
    {
        public string UriBase { get; set; } = string.Empty;
        public string UriApiPTH { get; set; } = string.Empty;
        public string UriTravelItinerary { get; set; } = string.Empty;
        public string UriUpsell { get; set; } = string.Empty;
        public string UriRevalidate { get; set; } = string.Empty;
        public string UriCreateBooking { get; set; } = string.Empty;
        public string UriSummary { get; set; } = string.Empty;
        public string SiteConfig { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string User { get; set; } = string.Empty;
        public string PasswordSummary { get; set; } = string.Empty;
        public string UserSummary { get; set; } = string.Empty;

    }
}

﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{
    [ProtoContract]
    public class SimilarDestination
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public string Name { get; set; }

        [ProtoMember(3)]
        public string StateName { get; set; }

        [ProtoMember(4)]
        public string Display { get; set; }

        [ProtoMember(5)]
        public string Uri { get; set; }

        [ProtoMember(6)]
        public string Image { get; set; }

        [ProtoMember(7)]
        public int HotelCount { get; set; }

        [ProtoMember(8)]
        public string Country { get; set; }

        [ProtoMember(9)]
        public string Latitude { get; set; }

        [ProtoMember(10)]
        public string Longitude { get; set; }

        public SimilarDestination()
        {
            Name = string.Empty;
            StateName = string.Empty;
            Display = string.Empty;
            Uri = string.Empty;
            Image = string.Empty;
            Country = string.Empty;
            Latitude = string.Empty;
            Longitude = string.Empty;
        }
    }

}

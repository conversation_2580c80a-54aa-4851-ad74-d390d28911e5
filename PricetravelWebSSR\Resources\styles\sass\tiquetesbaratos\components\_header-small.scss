header, .header-small{
    height: 72px;
    display: block;
    padding: var(--space-16) 0;
    box-shadow:var(--shadow-100);
    background: var(--bg-base);
    position: relative;
    z-index: 10;
}
img.logoPT{
    width: 120px;
}
.social-icons .font-icons {
    font-size: 24px;
}

.phone-number a:hover, header a:hover{
    text-decoration: none;
}

// header i{
//     vertical-align: text-bottom!important;
// }
header {
    a.text-dark{
        display: block;
        text-align: center;
        line-height: 90%;
        margin-top: -3px;
        span{
            display: block;
            color: var(--text-link);
            font-size: 14px;
        }
    }
}
@media (max-width: 767px) {
    header {
        a.text-dark{
            margin-top: 0px;
            span{
                display: none;
            }
        }
    }

}
.logo-pt{
    background: url("https://static.cdnpth.com/assets-core/img/pricetravel-logo.svg");
    max-width: 188px;
    width: 100%;
    height: 29px;
    background-size: contain;
    background-repeat: no-repeat;
    display: inline-block;
  }
@media (max-width: 767px) {
    img.logoPT{
        width: 120px;
    }
    .phonenumber .alert .container{
        padding: 0;
    }
    .phonenumber {
        padding: 12px !important;
    }

}
@media (min-width: 768px) and (max-width: 990px) {
    img.logoPT{
        width: 120px;
    }
}
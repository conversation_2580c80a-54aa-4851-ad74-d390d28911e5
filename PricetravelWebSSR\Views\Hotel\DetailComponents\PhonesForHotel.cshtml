@using Newtonsoft.Json.Serialization
@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Mappers
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Login
@using Newtonsoft.Json
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject ConfigMapper configMapper
@inject StaticHelper staticHelper

@{
    var ClassAssign = ViewData["ClassAssign"] as String;
    var primaryPhone = ViewData["primaryPhone"] as HotelPhone;
    var user = ViewData["User"] as User;
}
@if (primaryPhone != null && settingOptions.Value.SiteName == "pricetravel" && user == null)
{
    <div class="phone-reservation @(ClassAssign)">
        <p class="title">@viewHelper.Localizer("phonereservation_title")</p>
        <p class="subtle">@viewHelper.Localizer("phonereservation_subtitle")</p>
        <div class="phone-wrapper">
            <i class="icons-phone" aria-hidden="true"></i>
            <a href="tel:@primaryPhone.PhoneBase">@primaryPhone.PhoneFormatted</a>
        </div>
    </div>
}
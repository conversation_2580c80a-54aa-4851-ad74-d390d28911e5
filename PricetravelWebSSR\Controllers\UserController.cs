﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using PricetravelWebSSR.Helpers;
using PricetravelWebSSR.Interfaces;
using PricetravelWebSSR.Mappers;
using PricetravelWebSSR.Middleware;
using PricetravelWebSSR.Models.Configuration;
using PricetravelWebSSR.Models.Login;
using PricetravelWebSSR.Options;

namespace PricetravelWebSSR.Controllers
{
    [TypeFilter(typeof(SessionTokenFilter))]
    public class UserController : Controller
    {
        private readonly ILoginServices _accountServices;
        private readonly IAlternateHandler _alternateHandler;
        private readonly ICommonHandler _commonHandler;
        private readonly IUserHandler _userHandler;
        private readonly SettingsOptions _options;
        private readonly ViewHelper _util;

        public UserController(
            ILoginServices accountServices,
            IAlternateHandler alternateHandler,
            ICommonHandler commonHandler,
            IUserHandler userHandler,
            IOptions<SettingsOptions> options,
            ViewHelper util

        )
        {
            _accountServices = accountServices;
            _alternateHandler = alternateHandler;
            _commonHandler = commonHandler;
            _userHandler = userHandler;
            _options = options.Value;
            _alternateHandler = alternateHandler;
            _util = util;
        }


        [Route("{culture}/user/profile")]
        [Route("{culture}/user/perfil")]
        [HttpGet]
        public async Task<ActionResult> Profile(string culture)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));

            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "user/profile", Route = "login", Type = Types.PageType.Generic }, cts.Token);
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = culture }, cts.Token);
            var user = await _accountServices.GetUser();
            var profile = await _userHandler.QueryAsync(new UserProfile(), cts.Token);
            var meta = MetaMapper.UserMapper("profile", _options, _util, cultureInfo);
            
            ViewData["MetaTag"] = meta;
            ViewData["Profile"] = profile;
            ViewData["Alternates"] = alternates;
            ViewData["User"] = user;
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;

            return View();
        }


        [Route("{culture}/user/reservations")]
        [Route("{culture}/user/reservaciones")]
        public async Task<ActionResult> Reservations(string culture)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = culture }, cts.Token);

            var user = await _accountServices.GetUser();
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "user/reservations", Route = "login", Type = Types.PageType.Generic }, cts.Token);
            var meta = MetaMapper.UserMapper("profile", _options, _util, cultureInfo);

            ViewData["MetaTag"] = meta;
            ViewData["Alternates"] = alternates;
            ViewData["User"] = user;
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;

            return View();
        }

        [Route("{culture}/user")]
        [Route("{culture}/usuario")]
        public async Task<ActionResult> Account(string culture = "es")
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = culture }, cts.Token);

            var user = await _accountServices.GetUser();
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "user", Route = "login", Type = Types.PageType.Generic }, cts.Token);
            var meta = MetaMapper.UserMapper("home", _options, _util, cultureInfo);

            ViewData["MetaTag"] = meta;
            ViewData["Alternates"] = alternates;
            ViewData["User"] = user;
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;

            return View();
        }

        [Route("{culture}/user/companions")]
        [Route("{culture}/user/acompañantes")]
        public async Task<ActionResult> Companions(string culture)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = culture }, cts.Token);

            var user = await _accountServices.GetUser();
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "user/companions", Route = "login", Type = Types.PageType.Generic }, cts.Token);
            var profile = await _userHandler.QueryAsync(new UserProfile(), cts.Token);
            var meta = MetaMapper.UserMapper("companions", _options, _util, cultureInfo);

            ViewData["MetaTag"] = meta;
            ViewData["Profile"] = profile;
            ViewData["Alternates"] = alternates;
            ViewData["User"] = user;
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;

            return View();
        }


        [Route("{culture}/user/security")]
        [Route("{culture}/user/seguridad")]
        public async Task<ActionResult> Security(string culture)
        {
            using var cts = new CancellationTokenSource(TimeSpan.FromMilliseconds(58000));
            var userSelection = await _commonHandler.QueryAsync(new UserSelectionRequest() { }, cts.Token);
            var cultureInfo = await _commonHandler.QueryAsync(new Culture { CultureCode = culture }, cts.Token);
            var user = await _accountServices.GetUser();
            var alternates = await _alternateHandler.QueryAsync(new Models.Places.PlaceRequest { Culture = culture, Id = 0, Path = "user/security", Route = "login", Type = Types.PageType.Generic }, cts.Token);
            var profile = await _userHandler.QueryAsync(new UserProfile(), cts.Token);
            var meta = MetaMapper.UserMapper("security", _options, _util, cultureInfo);

            ViewData["MetaTag"] = meta;
            ViewData["Profile"] = profile;
            ViewData["Alternates"] = alternates;
            ViewData["User"] = user;
            ViewData["cultureData"] = userSelection.Culture;
            ViewData["currencyData"] = userSelection.Currency;
            ViewData["exchange"] = userSelection.ExchangeClient;
            ViewData["userLocation"] = userSelection.Context.Location;

            return View();
        }

    }
}

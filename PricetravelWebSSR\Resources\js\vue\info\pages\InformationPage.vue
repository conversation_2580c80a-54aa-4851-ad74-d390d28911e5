<template>
    <div id="legal-page" class="container py-32">
        <!-- Breadcrumb  --->
        <breadcrumb />

        <h1 class="mb-24 mt-32">{{ __("information.site_information") }}</h1>
        
        <ul class="legalgrid">
            <li>
                <a :href="`/${culture.cultureCode}/info/payment-methods/mx`" class="legalcard">
                    <img src="/assets/img/header/pricetravel/es.svg" alt="flag" width="36" height="36" class="rounded-circle" style="object-fit: cover; padding: 2px;">
                    <h2>{{ __("information.payment_methods") + ' ' + __("legal.mx") }}</h2>
                    <p>{{ __('information.payment_methods_description') }}</p>
                </a>
            </li>
            <li>
                <a :href="`/${culture.cultureCode}/info/payment-methods/global`" class="legalcard">
                    <i class="icons-i18n-outline"></i>
                    <h2>{{ __("information.payment_methods") + ' ' + __("legal.global") }}</h2>
                    <p>{{ __('information.payment_methods_description') }}</p>
                </a>
            </li>
            <li>
                <a :href="invoiceUrl" class="legalcard">
                    <i class="icons-file-lines"></i>
                    <h2>{{ __("information.billing") }}</h2>
                    <p>{{ __('information.billing_description') }}</p>
                </a>
            </li>
            <li>
                <a :href="`/${culture.cultureCode}/info/about-us`" class="legalcard">
                    <i class="icons-confirm-outline"></i>
                    <h2>{{ __("information.about_site") + " " + siteConfig.appName }}</h2>
                    <p>{{ __('information.about_site_description') }}</p>
                </a>
            </li>
        </ul>
    </div>
</template>

<script>
    export default {
        props: {
            invoiceUrl: {
                type: String,
                default: 'https://www.pricetravel.com.mx/ayuda/factura-electronica'
            },
        },
        data() {
            const locationData = window.__pt.userLocation || window.__pt.settings.site;
            const rawCountry = locationData?.country?.toLowerCase() || '';
            const mappedCountry = rawCountry === 'us' ? 'global' : rawCountry;
            
            return {
                activeTab: mappedCountry || 'global',
                languages: [
                    {
                        name: this.__("legal.mx"),
                        code: 'Price Res, SAPI de CV',
                        jurisdiction: "mx"
                    },
                    {
                        name: this.__("legal.global"),
                        code: 'BTC',
                        jurisdiction: "global"
                    },
                ],
                siteConfig: window.__pt.settings.site,
                culture: window.__pt.cultureData
            }
        },
    }
</script>
﻿import { setDatalayer, UTILS } from "./main"

export default class Generics {


    constructor() {
        this.settings = window.__pt.settings.site;
        this.request = window.__pt.data || {};
    }

    paymentOnline(id, message, type = "Error") {
        let event = {
            eventAction: UTILS.actions.payment_online,
            eventCategory: `${message} - ${type}`,
            eventLabel: id,
            event: UTILS.events.gtmEvent,
            eventName: UTILS.events.gtmEvent,
            eventExtra: '',
        }
        setDatalayer(event);
    }
}

export const Generic = new Generics();
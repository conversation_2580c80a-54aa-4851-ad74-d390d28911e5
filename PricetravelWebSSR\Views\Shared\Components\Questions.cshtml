@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.Seo;
@using PricetravelWebSSR.Models.Meta.Metatags;
@using PricetravelWebSSR.Models.Response;
@using System.Web;
@using PricetravelWebSSR.Types;
@inject ViewHelper viewHelper
@inject IOptions<SettingsOptions> settingOptions
@inject StaticHelper staticHelper
@{
	var placeInfo = ViewData["PlacesInfoS"] as PlacesResponse;
	var seoContent = ViewData["seoContents"] as SeoResponse;
	var title = ViewData["Title"] as string;
}
@if (seoContent.Seo.Faqs != null && seoContent.Seo.Faqs.Count > 0)
{
	<div class="container mt-5">
		<h2 class="mb-4" style="font-size: 24px;">
			@viewHelper.Localizer("seo_question", placeInfo != null ? @viewHelper.Localizer("seo_question_info") : "", (placeInfo != null ? placeInfo.Name : title) ?? "")
		</h2>
		<div class="row">
			<!-- Columna 1 -->
			<div class="col-md-6">
				<div class="mb-3">
					@foreach (var question in seoContent.Seo.Faqs.Take((seoContent.Seo.Faqs.Count + 1) / 2))
					{
						<div id="heading@(question.GetHashCode())" data-toggle="collapse" data-target="#collapse@(question.GetHashCode())" style="border-bottom: 1px solid var(--border-subtle); margin-bottom: 16px;" ng-click="vm.activetab[@question.GetHashCode()] = !vm.activetab[@question.GetHashCode()]">
							<h2 style="">
								<button class="btn font-16 btn-link btn-block text-left" type="button" style="display: flex; justify-content: space-between; width: 100%;min-height: 43.19px;" aria-expanded="false" aria-controls="collapse@(question.GetHashCode())">
									<span>@Html.Raw(@question.Question)</span>
									<i class="icons-chevron-down" style="place-items: center;" ng-class="vm.activetab[@question.GetHashCode()] ? 'rotate' : ''"></i>
								</button>
							</h2>
							<div id="collapse@(question.GetHashCode())" aria-labelledby="heading@(question.GetHashCode())" class="collapse">
								<div class="card-body p-0 font-14" style="margin-bottom: 16px;">
									@Html.Raw(@question.Answer)
								</div>
							</div>
						</div>
					}
				</div>
			</div>
			<!-- Columna 2 -->
			<div class="col-md-6">
				<div class="mb-3">
					@foreach (var question in seoContent.Seo.Faqs.Skip((seoContent.Seo.Faqs.Count + 1) / 2))
					{
						<div id="heading@(question.GetHashCode())" data-toggle="collapse" data-target="#collapse@(question.GetHashCode())" style="border-bottom: 1px solid var(--border-subtle); margin-bottom: 16px;" ng-click="vm.activetab[@question.GetHashCode()] = !vm.activetab[@question.GetHashCode()]">
							<h2 style="">
								<button class="btn font-16 btn-link btn-block text-left" type="button" style="display: flex; justify-content: space-between; width: 100%;min-height: 43.19px;" aria-expanded="false" aria-controls="collapse@(question.GetHashCode())">
									<span>@Html.Raw(@question.Question)</span>
									<i class="icons-chevron-down" style="place-items: center;" ng-class="vm.activetab[@question.GetHashCode()] ? 'rotate' : ''"></i>
								</button>
							</h2>
							<div id="collapse@(question.GetHashCode())" aria-labelledby="heading@(question.GetHashCode())" class="collapse">
								<div class="card-body p-0 font-14" style="margin-bottom: 16px;">
									@Html.Raw(@question.Answer)
								</div>
							</div>
						</div>
					}
				</div>
			</div>
		</div>
	</div>
}
@if (seoContent.Seo.Meta.Paragraphs != null && seoContent.Seo.Meta.Paragraphs.Count > 0)
{
	<div class="container mb-4 mt-5">
		<h2 class="mb-4" style="font-size: 24px;">@viewHelper.Localizer("seo_info", (placeInfo != null ? placeInfo.Name : title != "" ? title : settingOptions.Value.AppName) ?? settingOptions.Value.AppName)</h2>
		@{
			foreach (var paragra in seoContent.Seo.Meta.Paragraphs)
			{
				<p class="font-14">
					@Html.Raw(paragra)
				</p>
			}
		}
	</div>
}
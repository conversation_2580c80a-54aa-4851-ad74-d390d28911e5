﻿<template>
    <div id="legal" class="container py-32">
        <!-- Breadcrumb --->
        <breadcrumb />

        <!-- Main Title --->
        <h1 :class="legalType == 'profeco_contract' ? 'text-md-center' : ''">{{ getPageTitle }} </h1>
        
        <!-- Jurisdiction notification --->
        <p class="my-28 my-lg-36 info-notification" v-if="legalType != 'profeco_contract'">
            <i class="icons-i18n-outline"></i>
            <span v-html="jurisdictionNotification"></span>
        </p>

        <hr class="my-28 my-lg-36" :class="!(legalType == 'profeco_contract') ? 'd-none d-lg-block' : ''">

        <div :class="[
                    getContentSelected !== '' ? 'legallayout' : '',
                    legalType === 'profeco_contract' ? 'legallayout--noMenu' : ''
                ]">
            <!-- Menu --->
            <nav v-if="getContentSelected != '' && getMenuSelected.length > 0" class="legalindice" :class="isMenuOpen ? 'legalindice--open':''">
                <button class="legalindice__btn d-lg-none" @click="toggleIndice">
                    <h2>{{ __(`legal.on_this_page_${mappedCountry}`) }}</h2>
                    <span class="position-relative">
                        <i class="icons-list font-16 font-primary" :class="isMenuOpen ? 'disappear' : ''"></i>
                        <i class="icons-close font-16 font-primary" :class="isMenuOpen ? '' : 'disappear'"></i>
                    </span>
                </button>
                <h2 class="d-none d-lg-block">{{ __(`legal.on_this_page_${mappedCountry}`) }}</h2>
                <ul class="legalindice__menu" :class="isMenuOpen ? 'legalindice__menu--open':''">
                    <li v-for="(item, index) in getMenuSelected" :key="index">
                        <a :href="item.landmark" :class="{ active: item.isActive }" 
                            @click.prevent="handleScroll(item, index)">
                            {{ item.title }}
                            <i v-if="item.submenus && item.submenus.length > 0" 
                                class="icons-chevron-down font-16" 
                                :class="{ 'rotate': item.isSubmenuOpen }">
                            </i>
                        </a>

                        <ul v-if="item.submenus && item.submenus.length > 0"
                            v-show="item.isSubmenuOpen"
                            class="legalindice__submenu">
                            <li v-for="(subitem, subindex) in item.submenus" :key="subindex">
                                <a :href="subitem.landmark" :class="{ active: subitem.isActive }" @click.prevent="handleScroll(subitem, index)">
                                    {{ subitem.title }}
                                </a>
                            </li>
                        </ul>
                    </li>
                </ul>
            </nav>
    
            <!-- Info --->
            <div class="legalcontent" v-if="getContentSelected != ''" v-html="getContentSelected"></div>
            <div v-else>
                <img :width="product === 'TB' ? '150' : '300'" :src="`${cdn}${img}`" class="d-block mr-auto ml-auto mb-3">
                <h3 class="d-center mb-5"> 404 </h3>
            </div>
        </div>
    </div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { useLegalStore } from '../../stores/legal';

    export default {
        props: {
            content: null,
            product: null,
            img: null,
            cdn: null,
        },
        data() {
            const rawCountry = window.location.pathname.split("/").slice(-1)[0];
            const mappedCountry = rawCountry === 'global' ? rawCountry : 'mx';
            const legalType = window.__pt.legalType.replace(/-/g, "_");
            const culture = window.__pt.cultureData;

            return {
                jurisdiction: mappedCountry || 'global',
                isMenuOpen: false,
                legalType,
                mappedCountry,
                culture
            }
        },
        computed: {
            getPageTitle() {
                return `${this.__(`legal.${this.legalType}_${this.mappedCountry}`)} ${this.__(`legal.${this.jurisdiction}`)}`;
            },
            jurisdictionNotification() {
                return this.jurisdiction === 'mx'
                    ? this.__("legal.terms_conditions_notification_mx", [`/${this.culture.cultureCode}/legal/terms-and-conditions/global`])
                    : this.__("legal.terms_conditions_notification_global", [`/${this.culture.cultureCode}/legal/terms-and-conditions/mx`]);
            }
        },
        setup() {
            const useLegal = useLegalStore();
            const { setResponse, changeLanguage, setContentSelectedInitial } = useLegal;
            const { isStaticContent, getContentSelected, getMenuSelected } = storeToRefs(useLegal);

            return { setResponse, isStaticContent, changeLanguage, getContentSelected, getMenuSelected, setContentSelectedInitial };
        },
        mounted() {
            this.setResponse(this.content);
            this.setContentSelectedInitial(this.jurisdiction);
            window.addEventListener('scroll', this.handleScrollSpy);
        },
        beforeUnmount() {
            window.removeEventListener('scroll', this.handleScrollSpy);
        },
        methods: {
            toggleIndice() {
                this.isMenuOpen = !this.isMenuOpen;
            },
            handleScroll(item, index) {
                const section = document.getElementById(item.landmark);
                if (section) { section.scrollIntoView({ behavior: "smooth" }); }
                
                setTimeout(() => {
                    this.getMenuSelected.forEach(menuItem => {
                        menuItem.isActive = false;
                        if (menuItem.submenus) {
                            menuItem.submenus.forEach(subitem => {
                                subitem.isActive = false;
                            });
                        }
                    });

                    item.isActive = true;

                    if (item.submenus && item.isActive && !item.landmark) {
                        item.isSubmenuOpen = true;
                    }
                    
                    if (!item.isSubmenuItem) {
                        this.isMenuOpen = false;
                    }
                }, 700);
            },
            handleScrollSpy() {
                const sections = this.getMenuSelected.map(item => ({
                    id: item.landmark,
                    element: document.getElementById(item.landmark),
                    submenus: item.submenus || []
                }))
                .filter(item => item.element !== null);

                let currentSection = null;

                sections.forEach(({ id, element, submenus }) => {
                    const rect = element.getBoundingClientRect();
                    if (rect.top <= 100 && rect.bottom + 36 >= 100) {
                        currentSection = id;
                    }

                    submenus.forEach(subitem => {
                        const subElement = document.getElementById(subitem.landmark);
                        if (subElement) {
                            const subRect = subElement.getBoundingClientRect();
                            if (subRect.top <= 100 && subRect.bottom + 36 >= 100) {
                                currentSection = subitem.landmark;
                            }
                        }
                    });
                });

                this.getMenuSelected.forEach(menuItem => {
                    menuItem.isActive = menuItem.landmark === currentSection;
                    
                    if (menuItem.submenus) {
                        menuItem.submenus.forEach(subitem => {
                            subitem.isActive = subitem.landmark === currentSection;
                        });

                        menuItem.isSubmenuOpen = menuItem.submenus.some(subitem => subitem.isActive) || menuItem.isActive;
                    }
                });
            }
        }
    }
</script>
﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{

    [ProtoContract]
    public class GalleryImage
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public string Title { get; set; }

        [ProtoMember(3)]
        public string Uri<PERSON>ini { get; set; }

        [ProtoMember(4)]
        public string UriThumb { get; set; }

        [ProtoMember(5)]
        public string UriSmall { get; set; }

        [ProtoMember(6)]
        public string UriMedium { get; set; }

        [ProtoMember(7)]
        public string UriLarge { get; set; }

        [ProtoMember(8)]
        public int Order { get; set; }

        public GalleryImage()
        {
            Title = string.Empty;
            UriMini = string.Empty;
            UriThumb = string.Empty;
            UriSmall = string.Empty;
            UriMedium = string.Empty;
            UriLarge = string.Empty;
        }
    }

}

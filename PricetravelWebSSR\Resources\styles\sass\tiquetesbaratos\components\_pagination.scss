.pagination {
    list-style: none;
    padding: 0;
    display: flex;
    align-items: stretch;
    justify-content: center;
    background-color: var(--bg-base);
    border-radius: 8px;
    box-shadow: var(--shadow-100);
    transition: box-shadow 150ms ease-out;
    overflow: hidden;

    li {
        .p-btn {
            height: 100%;
            padding: 12px 16px;
            border-right: 1px solid var(--border-subtle);
            text-align: center;
            font-size: 0.875rem;
            color: var(--text-main);
            transition: all 150ms ease-out;
            white-space: nowrap;
            user-select: none;
            &:hover {
                background-color:  var(--bg-level2);
            }
            &:disabled {
                color: var(--text-disabled);
                background: var(--bg-disabled);
    
                &:hover {
                    color: var(--text-disabled);
                    background: var(--bg-disabled);
                    cursor: not-allowed;
                }
            }
            &.active {
                background-color:  var(--bg-level2);
                font-weight: 600;
            }
        }
        &:last-of-type {
            .p-btn {
                border-right: none;
            }
        }
    }
    &:hover {
        box-shadow: var(--shadow-300);
    }
}
﻿<template>
    <ul class="pagination">
        <li>
            <button class="p-btn" @click="isDisablePrev ? ()=>{} : prevPage()" :disabled="isDisablePrev">
                <i class="icons-angle-left align-middle"></i>
                {{__("profile.previus")}}
            </button>
        </li>
        <li v-for="(btn, index) in paginate" >
            <button 
                class="p-btn"
                :key="index"
                :class="{'active': btn == getPagination.currentPage, 'cursor-default': btn === '...'}"
                @click="selectPage(btn)">
                {{btn}}
            </button>
        </li>
        <li>
            <button class="p-btn" @click="isDisableNext ? ()=>{} : nextPage()" :disabled="isDisableNext">
                {{__("profile.next")}}
                <i class="icons-angle-right align-middle"></i>
            </button>
        </li>
    </ul>
</template>

<script>
    import { storeToRefs } from 'pinia';
    //import { windowScrollTop } from '../../../../utils/helpers/animates';
    import { useUserStore } from '../stores/user';

    const configSite = __pt.settings.site;
    export default {
        props: ['total'],
        data() {
            return {
                config: configSite
            }
        },
        setup() {
            const useUser = useUserStore();
            const { getPagination } = storeToRefs(useUser);
            const { setPage } = useUser;
            return { getPagination, setPage }
        },
        computed: {
            paginate() {
                return this.getPagination.render && this.config.mobile ? this.getPagination.render.slice(0, 4) : this.getPagination.render
            },
            isDisablePrev(){
                return (this.getPagination.currentPage == 0 || this.getPagination.currentPage == 1 );
            },
            isDisableNext(){
                return (/* this.getPagination.currentPage == this.getPagination.totalPages - 1 || */ this.getPagination.currentPage == this.getPagination.totalPages)
            }
        },
        methods: {
            nextPage() {
                //windowScrollTop()
                if (this.getPagination.currentPage < (this.getPagination.totalPages /* - 1 */)) {
                    this.setPage(this.getPagination.currentPage + 1);
                }
            },
            prevPage() {
                //windowScrollTop()
                if (this.getPagination.currentPage > 0) {
                    this.setPage(this.getPagination.currentPage - 1);
                }
            },
            selectPage(page) {
                if (page != "...") {
                    //windowScrollTop()
                    this.setPage(page);
                }
            }
        }
    }
</script>
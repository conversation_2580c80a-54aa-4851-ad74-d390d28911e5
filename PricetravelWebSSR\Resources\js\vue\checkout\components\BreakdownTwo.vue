<template>
    <div>
        <!--  BREAKDOWN NOT INCLUDED -->
        <div>
            <div v-if="summary.totalRate.breakdownExcluded.length" class="mb-1" :class="{ 'mt-2': isMobileDetail }">
                <span class="font-weight-bold" :class="{ 'font-14': isMobileDetail }">{{
                    __('breakdown.charges_not_included') }}</span>
            </div>
            <div class="" v-for="breakdown in summary.totalRate.breakdownExcluded" :key="breakdown.title">
                <div v-if="breakdown.title == 'TaxExcluded' && summary.rate.country == 'CO'">
                    <div class="item-columns">
                        <div class="item font-title">
                            <i @click="openaModalResidents()" style="font-size: 17px;cursor: pointer;"
                                class="font-icons  btn-link icons-info align-bottom"></i>
                            {{ __(`breakdown.${breakdown.title}_${summary.rate.country}`) }}
                        </div>
                        <div class="item-right font-price">
                            <currency-display :amount="breakdown.amount" :showCurrencyCode="true"
                                :applyDecimals="true"></currency-display>
                        </div>
                    </div>
                    <!-- <residents-colo :breakdown="breakdown"></residents-colo>  -->
                </div>

                <div class="item-columns" v-else :class="{ 'border-0 mt-0 ': isMobileDetail }">
                    <a class="item btn-link font-strong font-title"
                        v-if="breakdown.title == 'HotelMandatoryTax' && summary.rate.country == 'PE'"
                        @click="openaModalResidentsPeru()" :class="{ 'font-14': isMobileDetail }">
                        <i style="font-size: 17px;cursor: pointer;"
                            class="font-icons btnTertiary icons-info font-blue pr-1 align-bottom"></i><span
                            class="ml-1">{{ __(`breakdown.${breakdown.title}`) }}</span>
                    </a>
                    <div class="item font-title" v-else :class="{ 'font-14': isMobileDetail }">
                        {{ __(`breakdown.${breakdown.title}`) }}
                    </div>
                    <div class="item-right font-price" v-if="breakdown.title != 'TaxExcluded'"
                        :class="{ 'font-14': isMobileDetail }">
                        <span v-if="breakdown.title == 'HospitalityTax'"><currency-display :amount="breakdown.amount"
                                :showCurrencyCode="true" :applyDecimals="true"></currency-display></span>
                        <span v-else>
                            <span v-if="breakdown.originalCurrency">
                                <span v-if="source?.includes('trivago') || source?.includes('google')">
                                </span>
                                <currency-display :amount="breakdown.originalAmount" :showCurrencyCode="true"
                                    :applyDecimals="true" :currencyCode="breakdown.originalCurrency"
                                    :applyConvertion="false" :applySymbol="true"></currency-display>*
                            </span>
                            <span v-else> <currency-display :amount="breakdown.amount" :showCurrencyCode="true"
                                    :applyDecimals="true"></currency-display>*</span>
                        </span>
                    </div>
                </div>
                <!-- <div class="col-5 d-flex justify-content-end"
                    v-if="(source == 'trivago' || source == 'google-hotel-ads') && breakdown.title != 'TaxExcluded' && breakdown.originalCurrency && breakdown.originalCurrency != currencySite">
                    {{ breakdown.amount | currency }}*
                </div> -->
            </div>
            <small class="text-muted font-12" v-if="summary.totalRate.breakdownExcluded.length > 0">
                *{{ __('messages.you_w_pay_upon_arr') }}
            </small>
            <section
                v-if="(source?.includes('trivago') || source?.includes('google')) && summary.totalRate.breakdownExcluded.length && isMetaTotal">
                <hr class="ml-2 mr-2 mb-0 mt-2" v-if="!isMobileDetail" />
                <div class="item-columns mt-2" :class="{ 'pb-2 font-14': isMobileDetail }">
                    <div class="item text-muted font-title" :class="{ 'font-14 font-main': isMobileDetail }">
                        {{ __('messages.source_triv_gool') }}
                    </div>
                    <div class="item-right text-muted font-price" :class="{ 'font-14 font-strong': isMobileDetail }">
                        <currency-display :amount="totalExluded + summary.totalRate.totalAmount"
                            :showCurrencyCode="true" :applyDecimals="true"></currency-display>
                    </div>
                </div>
            </section>
        </div>

    </div>
</template>

<script>
import ResidentsColo from './ResidentsColo';
const site = window.__pt.settings.site;
export default {
    props: {
        summary: {},
        source: "",
        isMetaTotal: false,
        currencySite: "",
        isMobileDetail: false
    },
    data() {
        return {
            siteConfig: site,
            totalExluded: 0
        }
    },
    components: {
        ResidentsColo,
    },
    methods: {
        openaModalResidents() {
            $("#modal-residents-colo").modal("show")
        },
        openaModalResidentsPeru() {
            $("#staticfax").modal("show")
        }
    },
    mounted() {
        this.totalExluded = this.summary.totalRate.breakdownExcluded.length > 0 ? this.summary.totalRate.breakdownExcluded.reduce((accum, item) => accum + item.amount, 0) : 0;

        if (this.summary.rate.country == 'MX') {
            this.summary.totalRate.breakdownExcluded = this.summary.totalRate.breakdownExcluded.filter(breakdown => breakdown.title != 'TaxExcluded')
        }
    }
}
</script>
<style scoped>
.font-price {
    font: var(--body-sm);
    color: var(--text-strong);
}

.font-title {
    font: var(--tight-sm);
}
</style>
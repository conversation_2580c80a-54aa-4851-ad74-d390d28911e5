@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent
@using PricetravelWebSSR.Options;
@using System.Text.Json;
@using PricetravelWebSSR.Models.Collection;
@using PricetravelWebSSR.Types;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.Configuration
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@{
    var metatag = ViewData["MetaTag"] as MetaTag;
    var resolution = viewHelper.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = viewHelper.IsRobot();
    var cultureConf = ViewData["cultureData"] as Culture;
    var imgPath = viewHelper.Localizer("img", @settingOptions.Value.AppName.ToLower());
    var userLocation = ViewData["UserLocation"] as UserLocation;
    var country = ViewData["Country"] as string;

    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;
}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot } })

<main id="payment-methods" ng-cloak ng-controller="controller as vm">
    <div class="paymentmethod__banner">
        <div class="container">
            <h1 class="font-32 font-lg-40">@viewHelper.Localizer("pay_best_optionstb")</h1>
            <p>@viewHelper.Localizer("know_our_paymentforms")</p>
            <a href="/" class="btnPrimary btnPrimary--md">@viewHelper.Localizer("find_next_trip") <i class="icons-angle-right font-20"></i> </a>
        </div>
    </div>
    <div class="container py-40">
        <section>
            <div class="mb-32 d-flex g-12">
                <i class="icons-credit-card-outline font-32 font-main d-flex"></i>
                <div>
                    <h2 class="font-20 font-main">@viewHelper.Localizer("modal_payment_card_title")</h2>
                    <div class="my-3 d-flex align-items-center g-12 flex-wrap">
                        <img class="paymentmethod__banklogo" bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-visa.svg" alt="visa card" width="40" height="28" />
                        <img class="paymentmethod__banklogo" bn-lazy-src="https://static.cdnpth.com/assets-core/img/logos_banks/amex-card.svg" alt="american express" width="40" height="28" />
                        <img class="paymentmethod__banklogo" bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg" alt="mastercard" width="40" height="28" />

                        @if (settingOptions.Value.SiteName.ToLower() == "tiquetesbaratos" || country != "mx" )
                        {
                            <img class="paymentmethod__banklogo" bn-lazy-src="https://static.cdnpth.com/assets-core/img/logos_banks/dinners-small.svg" alt="Diners Club" width="78" height="20" />
                        }
                        @if (settingOptions.Value.SiteName.ToLower() != "tiquetesbaratos" && country != "mx")
                        {
                            <img class="paymentmethod__banklogo" bn-lazy-src="/assets/img/logos_banks/logo-jcb.svg" alt="Japan Credit Bureau" width="34" height="26" />
                            <img class="paymentmethod__banklogo" bn-lazy-src="https://3.cdnpt.com/images/bancos/logo-maestro.svg" alt="Maestro" width="34" height="26" />
                            <img class="paymentmethod__banklogo" bn-lazy-src="/assets/img/logos_banks/logo-discover.svg" alt="Discover" width="78" height="14" />
                        }
                    </div>
                </div>
            </div>

            @if (country == "mx" )
            {
                <section>
                    <h3 class="mb-3 font-16">@viewHelper.Localizer("modal_payment_TC_promotion")</h3>
                    <div class="border rounded-16 bg-white">
                        <section class="paymentmethod__msiSection"
                            ng-repeat="entry in vm.responsePayment.monthInterestFree track by $index" >
                            <h4 class="mb-3 font-16 mb-lg-0">@viewHelper.Localizer("modal_payment_until") {{ entry[0] }} @viewHelper.Localizer("modal_payment_months") <span class="font-green">@viewHelper.Localizer("modal_payment_months_interest")*</span></h4>
                            <div class="d-flex align-items-center g-base flex-wrap">
                                <div ng-repeat="item in entry[1]"
                                        aria-label="{{item.name}}"
                                        class="icon-bank-brand icon-brand-{{item.icon || (item.name | lowercase) | removeSpaces }}-card">
                                </div>
                            </div>
                        </section>
                    </div>
                    <p class="mt-24 mb-24 font-12 font-subtle">*@viewHelper.Localizer("modal_payment_warning")</p> 
                </section>
            }
        </section>


        <hr class="my-40">

        <section class="d-flex g-12">
            <i class="icons-wallet font-32 font-main d-flex"></i>
            <div>
                <h2 class="font-20 font-main">@viewHelper.Localizer("modal_payment_digital_wallet")</h2>
                <div class="my-3 d-flex align-items-center g-20 flex-wrap">
                    <img src="/assets/img/logos_banks/logo-paypal.svg" alt="paypal" width="135" height="44" />
                    <img src="/assets/img/logos_banks/logo-applepay.svg" alt="apple pay" width="76" height="52" />
                </div>
                <p class="mb-0 font-12 font-subtle">*@viewHelper.Localizer("modal_payment_restriction")</p>
            </div>
        </section>


        @if (country == "mx" )
        {
            <hr class="my-40">

            <section class="d-flex g-12">
                <i class="icons-monument font-32 font-main d-flex"></i>
                <div>
                    <h2 class="mb-20 font-20 font-main">@viewHelper.Localizer("bank_deposit_transfer")</h2>
                    <p>@viewHelper.Localizer("bank_deposit_transfer_description")</p>
                    <div class="d-flex align-items-center g-20 flex-wrap">
                        <div class="icon-bank-brand icon-brand-scotia-card" aria-label="scotiabank" style="width: 160px; height:40px;"></div>
                        <div class="icon-bank-brand icon-brand-bbva-card" aria-label="bbva" style="width: 160px; height:40px;"></div>
                        <div class="icon-bank-brand icon-brand-banorte-card" aria-label="banorte" style="width: 160px; height:40px; background-size: cover !important;"></div>
                    </div>
                </div>
            </section>
            
            <hr class="my-40">

            <section class="d-flex g-12">
                <i class="icons-store font-32 font-main d-flex"></i>
                <div>
                    <h2 class="mb-20 font-20 font-main">@viewHelper.Localizer("pay_at_pdv", settingOptions.Value.AppName)</h2>
                    <p>@viewHelper.Localizer("pay_at_pdv_description") <a href="@(settingOptions.Value.SiteUrl)/@(cultureConf.CultureCode)/@viewHelper.Localizer("link_pdv_url")" target="_blank" rel="noopenner noreferrer" class="link text-lowercase">@viewHelper.Localizer("pdv_locations")</a>.</p>
                </div>
            </section>
        }
    </div>
</main>

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml")


@* @section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
} *@

@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/pages.css")" as="style">
}
@section Css {
    <link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/pages.css")">
}
@section Scripts {
    <script type="text/javascript">
        window.__pt = window.__pt || {};
    </script>

    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_pdv.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/controllers/login-controllers.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/home/<USER>")"></script>
}

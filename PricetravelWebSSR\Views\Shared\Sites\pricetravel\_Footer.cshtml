﻿@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.Configuration
@inject ViewHelper viewHelper;
@inject IOptions<SettingsOptions> settingOptions

@{
    var metatag = ViewData["MetaTag"] as MetaTag;
    var cultureConfiguration = ViewData["cultureData"] as Culture;
    var footerMinimal = (bool)(ViewData["footerMinimal"] ?? false);
    var showLinksLegals = (bool)(ViewData["showLinksLegals"] ?? false);
    var userLocation = ViewData["UserLocation"] as UserLocation;

    if (cultureConfiguration is null)
    {
        cultureConfiguration = new Culture();
    }

    if (userLocation is null){
        userLocation = new UserLocation();
    }
}
<div class="svg_line svg_line_footer" ng-cloak></div>
<footer class="container mt-4" ng-cloak>
    @if (metatag != null && metatag.BreadCrumbs != null && metatag.BreadCrumbs.ItemListElement.Count > 0)
    {
        <div class="row">
            <div class="col-12">
                @await Html.PartialAsync("_BreadCrumb")
            </div>
        </div>
    }
    @if (!footerMinimal)
    {
        <nav class="row justify-content-between accordion small">
            <div class="col-12 col-md-auto accordion accordion-item border-md-0">
                <p class="mb-3 d-md-block h5 footer-group-heading">@viewHelper.Localizer("customer_services") <i class="icons-font icons-chevron-down  float-right d-md-none"></i> </p>
                <ul class="list-unstyled accordion-collapse collapse d-md-block">
                    <li class="mb-2">
                        <a class="link link--xs" href="@viewHelper.Localizer("help_" + settingOptions.Value.SiteName)" target="_blank" ng-click="vm.sendContentFooter('@viewHelper.Localizer("help")')">
                            @viewHelper.Localizer("help")
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs" target="_blank" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/@viewHelper.Localizer("link_pdv_url")">
                            @viewHelper.Localizer("link_pdv",@settingOptions.Value.AppName)
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs" href="@viewHelper.Localizer("invoices_" + settingOptions.Value.SiteName)" ng-click="vm.sendContentFooter('@viewHelper.Localizer("invoices")')">
                            @viewHelper.Localizer("invoices")
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs"  href="@viewHelper.Localizer("get_booking_" + settingOptions.Value.SiteName, cultureConfiguration.CultureCode)"  ng-click="vm.sendContentFooter('@viewHelper.Localizer("get_booking")')">
                            @viewHelper.Localizer("get_booking")
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs" href="@viewHelper.Localizer("update_booking_" + settingOptions.Value.SiteName)"  ng-click="vm.sendContentFooter('@viewHelper.Localizer("update_booking")')">
                            @viewHelper.Localizer("update_booking")
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs" href="@viewHelper.Localizer("cancel_booking_" + settingOptions.Value.SiteName)"  ng-click="vm.sendContentFooter('@viewHelper.Localizer("cancel_booking")')">
                            @viewHelper.Localizer("cancel_booking")
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col-12 col-md-auto accordion accordion-item border-md-0">
                <p class="mb-3 d-md-block h5 footer-group-heading">@viewHelper.Localizer("about_us") <i class="icons-font icons-chevron-down  float-right d-md-none"></i></p>
                <ul class="list-unstyled accordion-collapse collapse d-md-block">
                    <li class="mb-2">

                        <a class="link link--xs" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/info/about-us" ng-click="vm.sendContentFooter('@viewHelper.Localizer("our_history")')">
                            @viewHelper.Localizer("our_history")
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs" target="_blank" href="https://www.pricetravelholding.com/sala-de-prensa/" ng-click="vm.sendContentFooter('@viewHelper.Localizer("press_room")')">
                            @viewHelper.Localizer("press_room")
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs" href="http://www.pricetravel.com/revista/" ng-click="vm.sendContentFooter('@viewHelper.Localizer("pricetravel_magazine",@settingOptions.Value.AppName)')">
                            @viewHelper.Localizer("pricetravel_magazine",@settingOptions.Value.AppName)
                        </a>
                    </li>
                    <li class="mb-2">
                        <a class="link link--xs" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/@(viewHelper.Localizer("url_destination"))" ng-click="vm.sendContentFooter('@viewHelper.Localizer("destinations")')" title="@viewHelper.Localizer("destinations")">
                            @viewHelper.Localizer("destinations")
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col-12 col-md-auto accordion accordion-item border-md-0">
                <p class="mb-3 h5 d-md-block footer-group-heading">@viewHelper.Localizer("providers") <i class="icons-font icons-chevron-down  icons-chevron-down  float-right d-md-none"></i> </p>
                <ul class="list-unstyled accordion-collapse collapse d-md-block">
                    <li class="mb-2">
                        <a class="link link--xs" target="_blank" href="https://autoenrollment.pricetravel.com/" ng-click="vm.sendContentFooter('@viewHelper.Localizer("hotel_register")')">
                            @viewHelper.Localizer("hotel_register")
                        </a>
                    </li>
                </ul>
            </div>
            <div class="col-12 col-md-auto accordion accordion-item border-md-0">
                <p class="mb-3 h5 d-md-block footer-group-heading">@viewHelper.Localizer("contact_us") <i class="icons-font icons-chevron-down t float-right d-md-none"></i></p>
                <ul class="list-unstyled accordion-collapse collapse d-md-block">
              <li class="mb-2">

                        <b>@viewHelper.Localizer("links_mx_country")</b>
                        <a class="link link--xs" href="tel:@settingOptions.Value.StaticPhoneNumbers.PrimaryPhone">@settingOptions.Value.StaticPhoneNumbers.PrimaryPhoneFormat</a>

                    </li>
                    <li class="mb-2">

                        <b>@viewHelper.Localizer("links_co_country")</b>
                        <a class="link link--xs" href="tel:@settingOptions.Value.StaticPhoneNumbers.SecondaryPhone">@settingOptions.Value.StaticPhoneNumbers.SecondaryPhoneFotmat</a>

                    </li>
                    <li class="mb-2">

                        <b>@viewHelper.Localizer("links_us_country")</b>
                        <a class="link link--xs" href="tel:@settingOptions.Value.StaticPhoneNumbers.UsaPhone">@settingOptions.Value.StaticPhoneNumbers.UsaPhoneFormat</a>

                    </li>
                    <li class="mb-2">

                        <b>@viewHelper.Localizer("links_oc_country")</b>
                        <a class="link link--xs" href="tel:@settingOptions.Value.StaticPhoneNumbers.RestOfWorld">@settingOptions.Value.StaticPhoneNumbers.RestOfWorldFormat</a>

                    </li>
                    <li class="mb-2">
                        <b>@viewHelper.Localizer("email_string")</b>
                        <a class="link link--xs" href="mailto:@viewHelper.Localizer("mail_contact_" + @settingOptions.Value.SiteName.ToLower())">@viewHelper.Localizer("mail_contact_" + @settingOptions.Value.SiteName.ToLower())</a>
                    </li>
                    <li class="mb-2 social-icons">
                        <a class="link link--xs" href="https://www.facebook.com/PriceTravel" title="Facebook" class="me-30 d-inline-block">
                            <span class="font-icons icons-b-facebook-square"></span>
                        </a>
                        <a class="link link--xs" href="https://twitter.com/pricetravel" title="Twitter" class="me-30 d-inline-block">
                            <span class="font-icons icons-b-twitter"></span>
                        </a>
                        <a class="link link--xs" href="https://www.instagram.com/pricetravel/" title="Instagram" class="d-inline-block">
                            <span class="font-icons icons-b-instagram"></span>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        <hr class="d-none d-md-block">
    }

    <div class="row justify-content-between justify-content-md-center m-1 align-items-center">
        <p class="col-12 col-md-auto me-md-20 font-12">@viewHelper.Localizer(userLocation.Country == "MX" ? "legals" : "legals_world", DateTime.Now.Year)</p>
        @if (!showLinksLegals)
        {
            <p class="col-md-auto col-auto "><a class="link link--xs" target="_blank" rel="noopener noreferrer" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/legal/terms-and-conditions">@viewHelper.Localizer("terms_conditions")</a></p>
            <p class="col-md-auto col-auto"><a class="link link--xs" target="_blank" rel="noopener noreferrer" href="@(settingOptions.Value.SiteUrl)/@(cultureConfiguration.CultureCode)/legal/privacy-policy">@viewHelper.Localizer("privacy_terms")</a></p>
        }
    </div>


</footer>

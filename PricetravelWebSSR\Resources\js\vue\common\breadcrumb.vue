<template>
    <nav>
        <ul class="breadcrumbs">
            <li><a href="/" class="link link--md">{{ __("breadcrumbs.home") }}</a></li>
            <li v-for="(step, stepIndex) in stepsArray" :key="stepIndex">
                <a v-if="stepIndex < stepsArray.length - 1" 
                    :href="getPathUpTo(stepIndex)"
                    class="link link--md">
                    {{ __(`breadcrumbs.${step}`)}}
                </a>
                <span v-else>{{ __(`breadcrumbs.${step}`) + " " + getJurisdictionsByPath}}</span>
            </li>
        </ul>
    </nav>
</template>

<script>
    export default {
        data() {
            const culture = window.__pt.culture.cultureCode;
            const pathArray = window.location.pathname.split("/").filter(part => part !== '')
            const stepsArray = pathArray.filter(part => !['es', 'en', 'global', 'mx'].includes(part));            
            
            return {
                culture,
                pathArray,
                stepsArray
            }
        },
        computed: {
            getJurisdictionsByPath() {
                const lastPath = this.pathArray[this.pathArray.length - 1]
                const hasJurisdictionInPath = lastPath ==  'global' || lastPath == 'mx'
                const jurisdiction = hasJurisdictionInPath ? `${this.__(`breadcrumbs.${lastPath}`)}` : ""
                
                return jurisdiction
            }
        },
        methods: {
            getPathUpTo(index) {
                const segments = this.stepsArray.slice(0, index + 1);
                return `/${this.culture}/${segments.join('/')}`;
            }
        }
    }
</script>
<template id="child">
    <div class="room-container" :class="{ 'last-room': provider != 1 }">
        <div class="form-details">
            <div class="room-title">
                {{ __('messages.roomNew') }} {{ roomNumber }}
                <span class="room-occupancy">
                    ({{ roomData.pax.adults }} {{ roomData.pax.adults === 1 ? __('messages.adultNew') : __('messages.adultsNew') }}{{ roomData.pax.children && roomData.pax.children.length ? ', ' + roomData.pax.children.length + '&nbsp;' + (roomData.pax.children.length === 1 ? __('messages.MinorNew') : __('messages.MinorsNew')) + '&nbsp;(' + roomData.pax.children.map(child => child.year).join(', ') + '&nbsp;' + (roomData.pax.children.some(child => child.year === 1) && roomData.pax.children.every(child => child.year === 1) ? __('messages.year') : __('messages.years')) + ')' : '' }})
                </span>
            </div>
            <div class="room-type">{{ roomData.name }}</div>
        </div>
        <div class=" form-details">
            <div class="row">
                <div class="col-md-6 col-12">
                    <div class="form-group">
                        <label :for="`first_name_${roomNumber}`" class="form-label">{{ __('messages.first_name')
                        }}</label>
                        <ValidationProvider rules="alpha_spaces|min:2" v-slot="{ errors, flags }"
                            :ref="`first_name_${roomNumber}`">
                            <input type="text" :id="`first_name_${roomNumber}`" name="first_name" class="form-control"
                                :class="{
                                    'is-invalid': showErrors.first_name && errors && errors.length,
                                    'is-valid': flags.valid && !errors.length && roomUser.first_name && roomUser.first_name.trim().length > 0
                                }"
                                v-model="roomUser.first_name"
                                @input="handleFieldInput('first_name')"
                                @focus="handleFieldFocus('first_name')"
                                @blur="handleFieldBlur('first_name')"
                                :placeholder="__('messages.placeholder_name')">
                            <span v-if="showErrors.first_name && errors[0]" class="invalid-feedback">{{
                                __(`customErrorMessages.first_name.${errors[0]}`) }}</span>
                        </ValidationProvider>
                    </div>
                </div>
                <div class="col-md-6 col-12">
                    <div class="form-group">
                        <label :for="`last_name_${roomNumber}`" class="form-label">{{ __('messages.last_name')
                        }}</label>
                        <ValidationProvider rules="alpha_spaces|min:2" v-slot="{ errors, flags }"
                            :ref="`last_name_${roomNumber}`">
                            <input type="text" :id="`last_name_${roomNumber}`" name="last_name" class="form-control"
                                :class="{
                                    'is-invalid': showErrors.last_name && errors && errors.length,
                                    'is-valid': flags.valid && !errors.length && roomUser.last_name && roomUser.last_name.trim().length > 0
                                }"
                                v-model="roomUser.last_name"
                                @input="handleFieldInput('last_name')"
                                @focus="handleFieldFocus('last_name')"
                                @blur="handleFieldBlur('last_name')"
                                :placeholder="__('messages.placeholder_lastname')">
                            <span v-if="showErrors.last_name && errors[0]" class="invalid-feedback">{{
                                __(`customErrorMessages.last_name.${errors[0]}`) }}</span>
                        </ValidationProvider>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { ValidationProvider } from 'vee-validate';
import { HotelAnalytic } from '../../analytics/HotelsAnalytics'
const culture = window.__pt.cultureData;
export default {
    components: {
        ValidationProvider
    },
    props: {
        user: Object,
        provider: Number,
        submitEvent: Boolean,
        roomData: Object,
        rateData: Object,
        roomNumber: Number,
        totalRooms: Number,
        isMobile: Boolean
    },
    data() {
        return {
            culture,
            isTextAreaOpen: false,
            formFields: ['first_name', 'last_name'],
            roomUser: {
                first_name: "",
                last_name: "",
            },
            // Control de estado para validaciones
            fieldInFocus: {
                first_name: false,
                last_name: false
            },
            showErrors: {
                first_name: false,
                last_name: false
            }
        }
    },
    computed: {
        isLastRoom() {
            return this.roomNumber === this.totalRooms;
        }
    },
    async mounted() {
        this.onInit();
    },
    watch: {
        submitEvent: {
            deep: false,
            handler() {
                setTimeout(async () => {
                    for (const fieldName of this.formFields) {
                        this.showErrors[fieldName] = true;
                        await this.validateField(fieldName);
                    }
                });
            },
        },
        user: {
            deep: true,
            immediate: true,
            handler(newUser) {
                // if (newUser && newUser.first_name) {
                //     this.roomUser.first_name = newUser.first_name;
                // }
                // if (newUser && newUser.last_name) {
                //     this.roomUser.last_name = newUser.last_name;
                // }
            }
        }
    },
    methods: {
        onInit() {
        },
        syncUserData() {
            this.$emit('update-room-user', {
                roomNumber: this.roomNumber,
                userData: {
                    first_name: this.roomUser.first_name,
                    last_name: this.roomUser.last_name,
                    paxFam: this.rateData ? this.rateData.paxFam : '',
                    roomId: this.roomData ? this.roomData.roomID : 0
                }
            });
        },
        async validateField(field) {
            const refName = `${field}_${this.roomNumber}`;
            const provider = this.$refs[refName];

            if (!provider) {
                console.error(`ValidationProvider ref '${refName}' not found`);
                return;
            }

            const { valid, errors } = await provider.validate({ silent: false });

            if (!valid) {
                HotelAnalytic.trackUserError(
                    field,
                    { errors },
                    this.__('messages.enter_your_data')
                );
            }
            this.syncUserData();
        },

        async handleFieldInput(field) {
            const refName = `${field}_${this.roomNumber}`;
            const provider = this.$refs[refName];

            if (!provider) return;

            const { valid } = await provider.validate({ silent: true });

            if (valid && this.showErrors[field]) {
                this.showErrors[field] = false;
            }

            this.syncUserData();
        },

        handleFieldFocus(field) {
            this.fieldInFocus[field] = true;
        },


        async handleFieldBlur(field) {
            this.fieldInFocus[field] = false;

            if (!this.showErrors[field]) {
                this.showErrors[field] = true;
                await this.validateField(field);
            }
        }
    }
}
</script>
<style scoped>
/* Room container styles */
.room-container {
    padding: 16px var(--space-24) !important;
    border-bottom: 1px solid var(--border-subtle);

    &:last-child {
        margin-bottom: 0;
    }
}

@media screen and (max-width: 768px) {
    .room-container {
        padding: 16px var(--space-16) !important;
    }
}

/* Room information */
.room-title {
    font: var(--title-xxs);
}

.room-occupancy {
    font-weight: 400;
    margin-left: 10px;
    color: var(--text-strong);
}

.room-type {
    font-size: 16px;
    margin-bottom: 12px;
    color: var(--text-strong);
}

.last-room:last-child {
    margin-bottom: 0;
    border-bottom: none;
}

/* Media queries */
@media screen and (max-width: 768px) {
    .last-room:last-child {
        margin-bottom: 0;
        border-bottom: none;
    }
}
</style>

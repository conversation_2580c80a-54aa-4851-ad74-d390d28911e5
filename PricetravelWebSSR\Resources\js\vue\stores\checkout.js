import { defineStore } from "pinia";

export const useCheckoutStore = defineStore('checkout', {
    state: () => ({
        quoteResponse: {
            hash: "",
            message: "",
            places: {},
            quote: {}
        },
        voucherResponse: {
            info: {},
            quote: {},
            reservation: {}
        }
    }),
    getters: {
        getQuote: (state) => state.quoteResponse,
        getVoucher: (state) => state.voucherResponse,

    },
    actions: {
        setQuote(quoteResponse) {
            this.quoteResponse = quoteResponse;
        },
        setVoucher(voucherResponse) {
            this.voucherResponse = voucherResponse;
        }
    }
});
@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Meta.Metatags
@inject ViewHelper viewHelper;
@inject IOptions<SettingsOptions> settingOptions

<div id="DownloadAppBanner" class="d-md-none" ng-show="!vm.isBannerClosed()">
    <div class="container">
        <div class="d-flex align-items-center" style="gap: 4px;">
            <i class="icons-mobile-alt d-flex"></i>
            <p class="mb-0 font-14">
                @viewHelper.Localizer("banner_download_app_text_1")
                <strong>@viewHelper.Localizer("banner_download_app_text_2")</strong>
                @viewHelper.Localizer("banner_download_app_text_3")
            </p>
        </div>
        <div class="d-flex align-items-center" style="gap: 8px;">
            <a href="https://www.tiquetesbaratos.com/link/REIwNjJEMEU3MjdD" target="_blank" rel="noopenner noreferrer" class="btnSecondary btnSecondary--xs">
                @viewHelper.Localizer("banner_download_app_button")
            </a>
            <button class="closeBtn" aria-label="@viewHelper.Localizer("banner_download_app_close")" ng-click="vm.onClosedBanner('DownloadAppBanner')">
                <i class="icons-close d-flex pt-0"></i>
            </button>
        </div>
    </div>
</div>

@using PricetravelWebSSR.Helpers;
@using Microsoft.Extensions.Options;
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent
@using PricetravelWebSSR.Options;

@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper
@{

    var legalContent = ViewData["LegalContent"] as List<LegalContentResponse>;
    var imgPath = viewHelper.Localizer("img", @settingOptions.Value.SiteName.ToLower());
}

<div class="container" id="app-main">
    <Info :content='@Json.Serialize(legalContent)' :product='@Json.Serialize(settingOptions.Value.SiteName)' :img='@Json.Serialize(imgPath)' :cdn='@Json.Serialize(settingOptions.Value.CloudCdn)' />
</div>

@section Scripts {

    <script type="text/javascript">
        window.__pt = window.__pt || {};
        window.__pt.legalContent = @Html.Raw(viewHelper.ToJsonString(legalContent));
    </script>

    <script src="@staticHelper.GetVersion("/assets/js/vue/app.js")"></script>
}

﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.GeneralContent
@using PricetravelWebSSR.Models.ContentDeliveryNetwork.LegalContent
@using PricetravelWebSSR.Options;
@using System.Text.Json;
@using PricetravelWebSSR.Models.Collection;
@using PricetravelWebSSR.Types;
@using PricetravelWebSSR.Models.Meta.Metatags
@using PricetravelWebSSR.Models.Configuration
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@{
    var metatag = ViewData["MetaTag"] as MetaTag;
    var resolution = viewHelper.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = viewHelper.IsRobot();
    var cultureConf = ViewData["cultureData"] as Culture;
    var legalContent = ViewData["LegalContent"] as List<LegalContentResponse>;
    var imgPath = viewHelper.Localizer("img",@settingOptions.Value.AppName.ToLower());
    
    ViewData["IsRobot"] = isRobot;
    ViewData["isMobile"] = isMobile;
    ViewData["Resolution"] = resolution;
}


@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot } })

<main id="app-main" ng-cloak>
    <Legal-Page />
</main>

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml")


@section Meta {
    @await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] }})
}

@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/legal.css")" as="style">
}
@section Css {
    <link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/legal.css")">
}
@section Scripts {
    <script type="text/javascript">
        window.__pt = window.__pt || {};
        window.__pt.legalContent = @Html.Raw(viewHelper.ToJsonString(legalContent));
    </script>

    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_pdv.min.js")"></script>
    <script src="@staticHelper.GetVersion($"/assets/js/vue/lang/{cultureConf.CultureCode}.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/controllers/login-controllers.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/vue/app.js")"></script>
}

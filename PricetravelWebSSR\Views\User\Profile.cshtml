﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Models.Login
@using PricetravelWebSSR.Options;
@using System.Text.Json;
@using PricetravelWebSSR.Models.Collection;
@using PricetravelWebSSR.Types;
@using PricetravelWebSSR.Models.Meta.Metatags
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper
@{
    var resolution = viewHelper.GetImageResolution();
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isRobot = viewHelper.IsRobot();
    var cultureConf = ViewData["cultureData"] as Culture;
    var user = ViewData["Profile"] as UserProfile;
    var metaTag = ViewData["MetaTag"] as MetaTag;

    ViewData["Page"] = "user-profile";
    ViewData["IsRobot"] = isRobot;
    ViewData["IsMobile"] = isMobile;
    ViewData["Resolution"] = resolution;
}

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Header.cshtml", new ViewDataDictionary(ViewData) { { "navs", "show" }, { "login", isRobot } })


<main id="app-main">
    <profile :user='@Json.Serialize(user)'></profile>
</main>

<form id="AntiForgeryToken">
    @Html.AntiForgeryToken()
</form>

@await Html.PartialAsync($"~/Views/Shared/Sites/{settingOptions.Value.SiteName}/_Footer.cshtml", new ViewDataDictionary(ViewData) { { "footerMinimal", true }, { "login", isRobot } })

@section Meta {
	@await Html.PartialAsync("_MetaMain", new ViewDataDictionary(ViewData) { { "Meta", ViewData["MetaTag"] } })
}


@section Preload {
    <link rel="preconnect" href="@settingOptions.Value.CloudCdn">
    <link rel="preload" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/account.css")" as="style">
}
@section Css {
    <link type="text/css" rel="stylesheet" href="@staticHelper.GetVersion($"/assets/css/dist/{settingOptions.Value.SiteName}/account.css")">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-select@1.13.14/dist/css/bootstrap-select.min.css">
}
@section Scripts {
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap-select@1.14.0-beta3/dist/js/bootstrap-select.min.js"></script>
    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_pdv.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/bundle/bundle_profile.min.js")"></script>
    <script src="@staticHelper.GetVersion($"/assets/js/vue/lang/{cultureConf.CultureCode}.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/controllers/login-controllers.min.js")"></script>
    <script src="@staticHelper.GetVersion("/assets/js/vue/app.js")"></script>
}
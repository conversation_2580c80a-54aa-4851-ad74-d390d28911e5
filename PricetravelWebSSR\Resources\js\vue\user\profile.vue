﻿<template>
    <div class="container py-3 pr-0 pl-0">
        <breadcrumb />
        <div id="profile" class="container py-2">
            <h1 class="mb-4">
                <i class="icons-person font-28 align-middle"></i>
                {{ __('profile.profile') }}
            </h1>
            <div class="DataSections">
                <!------------- NAME -------------->
                <ValidationObserver ref="nameUser"
                                    v-slot="{ invalid }"
                                    tag="form"
                                    @submit.prevent="onSubmit('nameUser')"
                                    class="DataSections__field"
                                    :class="{ disabled: activeFieldset !== null && activeFieldset !== 'name' }">
                    <legend class="field__title">{{ __('profile.session_name') }} </legend>
                    <p class="field__data" v-show="!editProfileName">
                        <span v-if="!getUserProfile.name || !getUserProfile.lastName" class="font-italic">{{ __('profile.pending') }}</span>
                        <span v-else>{{ getUserProfile.name + " " + getUserProfile.lastName }}</span>
                    </p>
                    <button type="button"
                            class="field__editBtn btnTertiary btnTertiary--md"
                            v-show="!editProfileName"
                            @click="activateFieldset('name', true)"
                            :disabled="activeFieldset !== null && activeFieldset !== 'name'">
                        {{ __('profile.edit') }}
                    </button>

                    <div class="field__edit" v-show="editProfileName">
                        <ValidationProvider rules="required|alpha_spaces" v-slot="{ errors }" ref="first_name">
                            <label for="first_name" class="font-14" :class="{ 'font-red': errors[0] }">{{ __('messages.first_name') }}</label>
                            <input type="text" id="first_name" name="first_name"
                                   :class="{ 'invalid': errors && errors.length }" v-model="getUserProfile.name"
                                   :placeholder="__('messages.first_name')">
                        </ValidationProvider>
                        <ValidationProvider rules="required|alpha_spaces" v-slot="{ errors }" ref="last_name">
                            <label for="last_name" class="font-14" :class="{ 'font-red': errors[0] }">{{ __('messages.last_name') }}</label>
                            <input type="text" id="last_name" name="last_name"
                                   :class="{ 'invalid': errors && errors.length }" v-model="getUserProfile.lastName"
                                   :placeholder="__('messages.last_name')">
                        </ValidationProvider>
                    </div>

                    <div class="field__saveBtn" v-show="editProfileName">
                        <button type="button" class="btnTertiary btnTertiary--md" @click="activateFieldset('name')">{{ __('profile.cancel') }}</button>
                        <button type="submit"
                                class="btnPrimary btnPrimary--md"
                                ng-click="vm.editProfile.name = !vm.editProfile.name"
                                :disabled="!getUserProfile.name || !getUserProfile.lastName || invalid">
                            {{ __('profile.save') }}
                        </button>
                    </div>
                </ValidationObserver>
                <!------------- PHONE -------------->
                <ValidationObserver ref="phoneUser"
                                    v-slot="{ invalid }"
                                    tag="form"
                                    @submit.prevent="onSubmit('phoneUser')"
                                    class="DataSections__field"
                                    :class="{ disabled: activeFieldset !== null && activeFieldset !== 'phone' }">
                    <legend class="field__title" :class="{ 'font-red': invalid && editProfilePhone }">{{ __('profile.phone_login') }}</legend>
                    <p class="field__data" v-show="!editProfilePhone">
                        <span v-if="!getUserProfile.phone" class="font-italic">{{ __('profile.pending') }}</span>
                        <span v-else> +{{ getUserProfile.codePhone + " " + getUserProfile.phone }}</span>
                    </p>
                    <button type="button"
                            class="field__editBtn btnTertiary btnTertiary--md"
                            v-show="!editProfilePhone"
                            @click="activateFieldset('phone', true)"
                            :disabled="activeFieldset !== null && activeFieldset !== 'phone'">
                        {{ __('profile.edit') }}
                    </button>

                    <div class="field__edit" v-show="editProfilePhone">
                        <ValidationProvider rules="required|integer" v-slot="{ errors }" ref="phone">
                            <input type="tel" id="phone"
                                   :aria-label="__('messages.phone')"
                                   :placeholder="__('messages.phone')" :class="{ 'invalid': errors && errors.length }"
                                   v-model.number="getUserProfile.phone">
                        </ValidationProvider>
                    </div>
                    <div class="field__saveBtn" v-show="editProfilePhone">
                        <button type="button" class="btnTertiary btnTertiary--md" @click="activateFieldset('phone')">{{ __('profile.cancel') }}</button>
                        <button type="submit"
                                class="btnPrimary btnPrimary--md"
                                ng-click="vm.editProfile.phone = !vm.editProfile.phone"
                                :disabled="!getUserProfile.phone || invalid">
                            {{ __('profile.save') }}
                        </button>
                    </div>
                </ValidationObserver>
                <!------------- BIRTHDATE -------------->
                <ValidationObserver ref="birthdateUser"
                                    v-slot="{ invalid }"
                                    tag="form"
                                    @submit.prevent="onSubmit('birthdateUser')"
                                    class="DataSections__field"
                                    :class="{ disabled: activeFieldset !== null && activeFieldset !== 'birthdate' }">
                    <legend class="field__title">{{ __('profile.birthdate') }}</legend>
                    <p class="field__data" v-show="!editProfileBirthdate">
                        <span v-if="!getUserProfile.birthdate || getUserProfile.birthdate.includes('undefined')" class="font-italic">{{ __('profile.pending') }}</span>
                        <span v-else>{{ getUserProfile.birthdate }}</span>
                    </p>
                    <button type="button"
                            class="field__editBtn btnTertiary btnTertiary--md"
                            v-show="!editProfileBirthdate"
                            @click="activateFieldset('birthdate', true)"
                            :disabled="activeFieldset !== null && activeFieldset !== 'birthdate'">
                        {{ __('profile.edit') }}
                    </button>

                    <div class="field__edit" v-show="editProfileBirthdate">
                        <div class="d-flex g-16">
                            <ValidationProvider rules="required|is_not:0" v-slot="{ errors }" ref="day_user" class="flex-grow-1">
                                <label for="day_user" id="day_user_label" :class="{ 'font-red': errors[0] }">
                                    {{__('profile.day') }}
                                </label>
                                <select v-model="getUserProfile.day_user" id="day_user" name="day_user" class="selectpicker py-12" data-dropup-auto="false" data-container="body" data-none-selected-text="DD" :class="{ 'invalid': errors && errors.length }">
                                    <option v-for="option in day_options" :value="option.value">
                                        {{ option.text }}
                                    </option>
                                </select>
                            </ValidationProvider>
                            <ValidationProvider rules="required|is_not:0" v-slot="{ errors }" ref="month_user" class="flex-grow-1">
                                <label for="month_user" id="month_user_label" :class="{ 'font-red': errors[0] }">
                                    {{__('profile.month') }}
                                </label>
                                <select v-model="getUserProfile.month_user" id="month_user" name="month_user" class="selectpicker py-12" data-dropup-auto="false" data-container="body" data-none-selected-text="MM" :class="{ 'invalid': errors && errors.length }">
                                    <option v-for="option in month_options" :value="option.value">
                                        {{ option.text }}
                                    </option>
                                </select>
                            </ValidationProvider>
                            <ValidationProvider rules="required|is_not:0" v-slot="{ errors }" ref="year_user" class="flex-grow-1">
                                <label for="year_user" id="year_user_label" :class="{ 'font-red': errors[0] }">
                                    {{__('profile.year') }}
                                </label>
                                <select v-model="getUserProfile.year_user" id="year_user" name="year_user" class="selectpicker py-12" data-dropup-auto="false" data-container="body" data-none-selected-text="AAAA" :class="{ 'invalid': errors && errors.length }">
                                    <option v-for="option in createYearsOptions()" :value="option.value">
                                        {{ option.text }}
                                    </option>
                                </select>
                            </ValidationProvider>
                        </div>
                    </div>
                    <div class="field__saveBtn h-100 align-items-md-center pt-md-3" v-show="editProfileBirthdate">
                        <button type="button" class="btnTertiary btnTertiary--md" @click="activateFieldset('birthdate')">{{ __('profile.cancel') }}</button>
                        <button type="submit"
                                class="btnPrimary btnPrimary--md"
                                ng-click="vm.editProfile.birthdate = !vm.editProfile.birthdate"
                                :disabled="!getUserProfile.day_user || getUserProfile.day_user === 'undefined' ||
                                !getUserProfile.month_user || getUserProfile.month_user === 'undefined' ||
                                !getUserProfile.year_user || getUserProfile.year_user === 'undefined'">
                            {{ __('profile.save') }}
                        </button>
                    </div>
                </ValidationObserver>
                <!------------- NATIONALITY -------------->
                <ValidationObserver ref="nationalityUser"
                                    v-slot="{ invalid }"
                                    tag="form"
                                    @submit.prevent="onSubmit('nationalityUser')"
                                    class="DataSections__field"
                                    :class="{ disabled: activeFieldset !== null && activeFieldset !== 'nationality' }">
                    <legend class="field__title" :class="{ 'font-red': invalid && editProfileNationality }">{{ __('profile.nationality') }}</legend>
                    <p class="field__data" v-show="!editProfileNationality">
                        <span v-if="!getUserProfile.nationality" class="font-italic">{{ __('profile.pending') }}</span>
                        <span v-else>{{ labelForNation(getUserProfile.nationality) }}</span>
                    </p>
                    <button type="button"
                            class="field__editBtn btnTertiary btnTertiary--md"
                            v-show="!editProfileNationality"
                            @click="activateFieldset('nationality', true)"
                            :disabled="activeFieldset !== null && activeFieldset !== 'nationality'">
                        {{ __('profile.edit') }}
                    </button>

                    <div class="field__edit" v-show="editProfileNationality">
                        <ValidationProvider rules="required" v-slot="{ errors }" ref="nationality-select">
                            <select v-model="nationalityLowerCase"
                                    id="nationality-select"
                                    :aria-label="__('profile.nationality')"
                                    class="selectpicker" data-dropup-auto="false" data-container="body" data-none-selected-text="" data-live-search="true"
                                    :class="{ 'invalid': errors[0] }">
                                <option v-for="(item, index) in nations"
                                        :value="item.value"
                                        :key="item.value">
                                    {{ item.text }}
                                </option>
                            </select>
                        </ValidationProvider>
                    </div>
                    <div class="field__saveBtn" v-show="editProfileNationality">
                        <button type="button" class="btnTertiary btnTertiary--md" @click="activateFieldset('nationality')">{{ __('profile.cancel') }}</button>
                        <button type="submit"
                                class="btnPrimary btnPrimary--md"
                                ng-click="vm.editProfile.nationality = !vm.editProfile.nationality"
                                :disabled="!nationalityLowerCase || invalid">
                            {{ __('profile.save') }}
                        </button>
                    </div>
                </ValidationObserver>
                <!------------- GENRE -------------->
                <ValidationObserver ref="genreUser"
                                    v-slot="{ invalid }"
                                    tag="form"
                                    @submit.prevent="onSubmit('genreUser')"
                                    class="DataSections__field"
                                    :class="{ disabled: activeFieldset !== null && activeFieldset !== 'genre' }">
                    <legend class="field__title" :class="{ 'font-red': invalid && editProfileGenre }">{{ __('profile.genre') }}</legend>
                    <p class="field__data" v-show="!editProfileGenre">
                        <span v-if="!getUserProfile.gender" class="font-italic">{{ __('profile.pending') }}</span>
                        <span v-else>{{ selectedGenderText }}</span>
                    </p>
                    <button type="button"
                            class="field__editBtn btnTertiary btnTertiary--md"
                            v-show="!editProfileGenre"
                            @click="activateFieldset('genre', true)"
                            :disabled="activeFieldset !== null && activeFieldset !== 'genre'">
                        {{ __('profile.edit') }}
                    </button>

                    <div class="field__edit h-100" v-show="editProfileGenre">
                        <ValidationProvider rules="required" v-slot="{ errors }" ref="genre-select" class="d-flex align-items-center g-16 flex-nowrap h-100">
                            <label class="d-flex align-items-center g-base" v-for="n in genrer_options">
                                <input v-model="getUserProfile.gender" class="input--radio" type="radio" :name="`genre-${n.value}`" :id="`genre-${n.value}`" :value="n.value">
                                {{ n.text }}
                            </label>
                        </ValidationProvider>
                    </div>
                    <div class="field__saveBtn" v-show="editProfileGenre">
                        <button type="button" class="btnTertiary btnTertiary--md" @click="activateFieldset('genre')">{{ __('profile.cancel') }}</button>
                        <button type="submit"
                                class="btnPrimary btnPrimary--md"
                                :disabled="!getUserProfile.gender || invalid">
                            {{ __('profile.save') }}
                        </button>
                    </div>
                </ValidationObserver>
                <!------------- PASSPORT -------------->
                <ValidationObserver ref="passportUser"
                                    v-slot="{ invalid }"
                                    tag="form"
                                    @submit.prevent="onSubmit('passportUser')"
                                    class="DataSections__field"
                                    :class="{ disabled: activeFieldset !== null && activeFieldset !== 'passport' }">
                    <legend class="field__title" :class="{ 'font-red': invalid && editPassport }">{{ __('profile.passport') }} </legend>
                    <p class="field__data" v-show="!editPassport">
                        <span v-if="!getUserProfile.passport" class="font-italic">{{ __('profile.pending') }}</span>
                        <span v-else>{{ getUserProfile.passport }}</span>
                    </p>
                    <button type="button"
                            class="field__editBtn btnTertiary btnTertiary--md"
                            v-show="!editPassport"
                            @click="activateFieldset('passport', true)"
                            :disabled="activeFieldset !== null && activeFieldset !== 'passport'">
                        {{ __('profile.edit') }}
                    </button>

                    <div class="field__edit" v-show="editPassport">
                        <ValidationProvider rules="required" v-slot="{ errors }" ref="passport">
                            <input type="text" id="passport" name="passport"
                                   :aria-label="__('profile.passport')"
                                   :class="{ 'invalid': errors && errors.length }"
                                   v-model="getUserProfile.passport"
                                   :placeholder="__('profile.passport')">
                        </ValidationProvider>
                    </div>
                    <div class="field__saveBtn" v-show="editPassport">
                        <button type="button" class="btnTertiary btnTertiary--md" @click="activateFieldset('passport')">{{ __('profile.cancel') }}</button>
                        <button type="submit"
                                class="btnPrimary btnPrimary--md"
                                :disabled="!getUserProfile.passport || invalid">
                            {{ __('profile.save') }}
                        </button>
                    </div>
                </ValidationObserver>
            </div>
            <loading v-if="isLoading"></loading>
        </div>
    </div>
</template>

<script>
    import { ValidationObserver, ValidationProvider } from 'vee-validate';
    import intlTelInput from 'intl-tel-input';
    import { storeToRefs } from 'pinia';
    import { useUserStore } from '../stores/user';
    import GlobalUtil from '../../utils/pricetravel/shared/globalUtil';
    import { accountAnalytics } from '../analytics/AccountAnalytics';

    const _paramQuery = window.__pt.fn.search();
    const userLocation = window.__pt.userLocation;
    const siteconfig = window.__pt.settings.site;
    const cultureData = window.__pt.cultureData || {};
    const userData = window.__pt.user || {};
    const _globalUtil = new GlobalUtil(_paramQuery, siteconfig);
    const configs = siteconfig.login.firebaseSettings;
    export default {
        data() {
            return {
                nations: [],
                site: window.__pt.settings.site,
                culture: __pt.cultureData.cultureCode,
                editProfileName: false,
                editProfilePhone: false,
                editProfileBirthdate: false,
                editProfileNationality: false,
                editProfileGenre: false,
                editPassport: false,
                activeFieldset: null,
                day_options: [
                    { "text": "--", "value": "" },
                    { "text": "1", "value": "01" },
                    { "text": "2", "value": "02" },
                    { "text": "3", "value": "03" },
                    { "text": "4", "value": "04" },
                    { "text": "5", "value": "05" },
                    { "text": "6", "value": "06" },
                    { "text": "7", "value": "07" },
                    { "text": "8", "value": "08" },
                    { "text": "9", "value": "09" },
                    { "text": "10", "value": "10" },
                    { "text": "11", "value": "11" },
                    { "text": "12", "value": "12" },
                    { "text": "13", "value": "13" },
                    { "text": "14", "value": "14" },
                    { "text": "15", "value": "15" },
                    { "text": "16", "value": "16" },
                    { "text": "17", "value": "17" },
                    { "text": "18", "value": "18" },
                    { "text": "19", "value": "19" },
                    { "text": "20", "value": "20" },
                    { "text": "21", "value": "21" },
                    { "text": "22", "value": "22" },
                    { "text": "23", "value": "23" },
                    { "text": "24", "value": "24" },
                    { "text": "25", "value": "25" },
                    { "text": "26", "value": "26" },
                    { "text": "27", "value": "27" },
                    { "text": "28", "value": "28" },
                    { "text": "29", "value": "29" },
                    { "text": "30", "value": "30" },
                    { "text": "31", "value": "31" },

                ],
                month_options: [
                    { "text": "--", "value": "" },
                    { "text": this.__(`months.01`), "value": "01" },
                    { "text": this.__(`months.02`), "value": "02" },
                    { "text": this.__(`months.03`), "value": "03" },
                    { "text": this.__(`months.04`), "value": "04" },
                    { "text": this.__(`months.05`), "value": "05" },
                    { "text": this.__(`months.06`), "value": "06" },
                    { "text": this.__(`months.07`), "value": "07" },
                    { "text": this.__(`months.08`), "value": "08" },
                    { "text": this.__(`months.09`), "value": "09" },
                    { "text": this.__(`months.10`), "value": "10" },
                    { "text": this.__(`months.11`), "value": "11" },
                    { "text": this.__(`months.12`), "value": "12" },
                ],
                genrer_options: [
                    { "text": this.__(`profile.fem`), "value": "F" },
                    { "text": this.__(`profile.man`), "value": "M" },
                ],
                isLoading: false
            }
        },
        props: {
            user:null,
        },
        components: {
            ValidationObserver,
            ValidationProvider
        },
        computed: {
            nationalityLowerCase: {
                get() {
                    return this.getUserProfile?.nationality
                        ? this.getUserProfile.nationality.toLowerCase()
                        : '';
                },
                set(newValue) {
                    this.setNationality(newValue);
                }
            },
            selectedGenderText() {
                const selected = this.genrer_options.find(option => option.value === this.getUserProfile.gender);
                return selected ? selected.text : '';
            }
        },
        watch: {
            nationalityLowerCase(newVal) {
                this.$nextTick(() => {
                    $('.selectpicker').selectpicker('val', newVal);
                });
            },
            nations(newVal) {
                this.$nextTick(() => {
                    $('#nationality-select').selectpicker('destroy');
                    $('#nationality-select').selectpicker();
                    $('#nationality-select').selectpicker('val', this.nationalityLowerCase);
                });
            },
            'getUserProfile.day_user'(newVal) {
                if (newVal) this.refreshSelectWithValue('day_user', newVal);
            },
            'getUserProfile.month_user'(newVal) {
                if (newVal !== null && newVal !== undefined) {
                    this.refreshSelectWithValue('month_user', this.padLeft(newVal));
                }
            },
            'getUserProfile.year_user'(newVal) {
                if (newVal) this.refreshSelectWithValue('year_user', newVal);
            },
        },
        setup() {
            const useUser = useUserStore();
            const { setNationality, setBirthday, setBithdateParts, setUserProfile, copyOriginalData, resetUser, setCodePhone } = useUser;
            const { getUserProfile, getReservations, getPaxes, getBirthdate, getDialPhone } = storeToRefs(useUser);
            
            return { getUserProfile, getReservations, getPaxes, setNationality, setBirthday, getBirthdate, setBithdateParts, setUserProfile, resetUser, copyOriginalData, setCodePhone, getDialPhone }
        },
        beforeMount() {
            const countries = window.intlTelInputGlobals.getCountryData();
            this.createNationsOptions(countries);
        },
        mounted() {
            const procedUser = this.processUser(this.user);
            this.setUserProfile(procedUser);
            this.copyOriginalData(this.user);
            
            const initialCountriesArray = this.site.siteName === 'tiquetesbaratos' ? ["co", "us"] : ["mx", "us"];
            const input = document.querySelector("#phone");
            let iti = intlTelInput(input, {
                preferredCountries: initialCountriesArray,
                initialCountry: "auto"
            });

            //this.setCodePhone(iti.getSelectedCountryData().dialCode);
            var allCountries = window.intlTelInputGlobals.getCountryData();
            var matchingCountry = allCountries.find(c => "+" + c.dialCode === `+${this.getDialPhone}`);

            if (matchingCountry) {
                iti.setCountry(matchingCountry.iso2);
            } else {
                console.warn("No se encontró país con ese dial code.");
            }

            input.addEventListener("countrychange", () => {
                const selected = iti.getSelectedCountryData();
                if (selected) {
                    this.setCodePhone(selected.dialCode);
                    console.log(selected.dialCode);
                }
            });
            this.$nextTick(() => {
                $('#nationality-select').selectpicker();
                $('#nationality-select').selectpicker('val', this.nationalityLowerCase);

                ['day_user', 'month_user', 'year_user'].forEach(id => {
                    $(`#${id}`).selectpicker();
                    $(`#${id}`).selectpicker('val', this.getUserProfile[id]);
                });
            });
        },
        methods: {
            async onSubmit(refName) {
                if (this.$refs[refName]) {
                    const isValid = await this.$refs[refName].validate();
                    if (isValid) {
                        this.isLoading = true;
                        let user = { ...this.getUserProfile };
                        if (user.phone) {
                            user.phone = user.phone + "";
                        } 

                        if (user.day_user && user.month_user && user.year_user) {
                            const birthdate = `${user.day_user}/${user.month_user}/${user.year_user}`;
                            user.birthdate = birthdate;
                            this.setBirthday(birthdate);
                        }
                        
                        let options = {
                            headers: {
                                "X-CSRF-TOKEN": _globalUtil.getCSFRToken()
                            }
                        }

                        let response = await axios.post(siteconfig.siteUrl + "/hotel/api/update-account", user, options).catch((data) => this.onError(data, 'error'));

                        if (response && response.status == 200) {
                            this.activeFieldset = null;
                            this.copyOriginalData(user);
                            this.closeAllFieldset();
                            accountAnalytics.editUserProfile("Edicion", userData.type);
                            this.isLoading = false;
                        } else {
                            this.onError();
                        }
                    } else {
                        console.log("Errores en el formulario:", refName);
                    }
                } else {
                    console.error(`ValidationObserver ${refName} no está disponible`);
                }
            },
            activateFieldset(field, edit = false) {
                switch (field) {
                    case "name":
                        this.editProfileName = !this.editProfileName;
                        break;
                    case "phone":
                        this.editProfilePhone = !this.editProfilePhone;
                        break;
                    case "birthdate":
                        this.editProfileBirthdate = !this.editProfileBirthdate;
                        break;
                    case "nationality":
                        this.editProfileNationality = !this.editProfileNationality;
                        break;
                    case "genre":
                        this.editProfileGenre = !this.editProfileGenre;
                        break;
                    default:
                        this.editPassport = !this.editPassport;
                        break;
                }
                if (edit) {
                    this.activeFieldset = field;
                } else {
                    this.activeFieldset = null;
                    this.resetUser();
                }

            },
            closeAllFieldset() {
                this.editProfileName = false;
                this.editProfilePhone = false;
                this.editProfileBirthdate = false;
                this.editProfileNationality = false;
                this.editProfileGenre = false;
                this.editPassport = false;
            },
            createNationsOptions(countries) {
                this.nations = [];
                for (let index = 0; index < countries.length; index++) {
                    this.nations.push({ "text": countries[index].name, "value": countries[index].iso2 });
                }
            },
            labelForNation(nation) {
                return this.nations.find(n => n.value === nation).text;
            },
            createYearsOptions(age = 0) {
                const today = new Date();
                const year = today.getFullYear();
                const yearUntil = age == 0 ? 1900 : (year - age - 2);

                return [
                    { text: "--", value: "" },
                    ...Array.from({ length: year - yearUntil }, (_, i) => {
                        const y = year - i;
                        return { text: String(y), value: String(y) }
                    })
                ];
            },
            processUser(user) {
                const [day_user = '', month_user = '', year_user = ''] = user.birthdate ? user.birthdate.split('/') : [];
                return {
                    ...user,
                    day_user,
                    month_user,
                    year_user
                };
            },
            refreshSelectWithValue(selectId, value) {
                this.$nextTick(() => {
                    const $select = $(`#${selectId}`);
                    if ($select.length) {
                        $select.selectpicker('destroy');
                        $select.selectpicker();
                        $select.selectpicker('val', value);
                    }
                });
            },
            padLeft(value) {
                return value.toString().padStart(2, '0');
            }
        }
    }
</script>
﻿<template>
    <div class="container py-3 pr-0 pl-0">
        <breadcrumb />
        <div id="reservations" class="container py-2">
            <div class="mb-4 d-flex align-items-center justify-content-between g-8 flex-wrap">
                <h1>
                    <i class="icons-suitcase font-28 align-middle"></i>
                    {{ __('reservations.reservation_trips') }}
                </h1>
                <a @click="openModalSync()" target="_blank" class="link link--md" v-if="Object.keys(getReservations).length > 0">{{ __('reservations.cant_find_reservation') }}</a>
            </div>

            <!--- EMPTY STATE ---->
            <div class="text-center" v-if="getCategoryWithData === null && getReservations.data.length === 0">
                <img :src="`/assets/img/login/${config.siteName}/traveler-airport.${site === 'TB' ? 'svg' : 'png'}`"
                     alt="travel"
                     width="280"
                     style="width: 100%; max-width: 520px;">
                <p class="my-24 font-18 font-600">
                    {{ user?.name || "Hey" }}, {{ __('reservations.no_upcoming_trips', ) }}<br> {{__('reservations.where_to_go') }}
                </p>
                <a class="mb-24 btnPrimary" :href="`${config.siteUrl}`">{{ __('reservations.search_trip_options') }}</a><br>
                <a class="link" @click="openModalSync()">
                    {{ __('reservations.cant_find_reservation') }}
                </a>
            </div>
            <!-- TABS -->
            <menu class="ReservationsTabs" v-if="!getLoading && getReservations.data.length > 0">
                <li>
                    <button @click="setCategory('pending')" :class="{ active: getCategory === 'pending' }" :disabled="getQuotesData === 0">
                        {{ __('reservations.quote') }}
                    </button>
                </li>
                <li>
                    <button @click="setCategory('upcoming')" :class="{ active: getCategory === 'upcoming' }" :disabled="getUpcomingData === 0">
                        {{ __('reservations.upcoming') }}
                    </button>
                </li>
                <li>
                    <button @click="setCategory('pending-payment')" :class="{ active: getCategory === 'pending-payment' }" :disabled="getPendingData === 0">
                        {{ __('reservations.progressTitle') }}
                    </button>
                </li>
                <li>
                    <button @click="setCategory('completed')" :class="{ active: getCategory === 'completed' }" :disabled="getCompletedData === 0">
                        {{ __('reservations.past') }}
                    </button>
                </li>
                <li>
                    <button @click="setCategory('cancelled')" :class="{ active: getCategory === 'cancelled' }" :disabled="getCancelledData === 0">
                        {{ __('reservations.cancelled') }}
                    </button>
                </li>
            </menu>

            <!-- LOADER -->
            <loading v-if="getLoading"></loading>

            <!-- CARDS -->
            <div class="list-unstyled d-flex flex-column g-28" v-if="Object.keys(getReservations).length > 0">
                <section class="ReservationsCard" v-for="(item, index) in getReservations.data">
                    <div class="ReservationsCard__cover" :class="{'justify-content-end' : item.bookingStatus === 'COMPLETED' || item.bookingStatus === 'QUOTE'}">
                        <img onerror="this.src='/assets/img/login/placeholder.svg';" :src="item.productType === 'transfer' ? '/assets/img/transfer-card.jpg' : item.imageUrl" alt="cover" width="610">

                        <span v-if="item.bookingStatus !== 'COMPLETED' && item.bookingStatus !== 'QUOTE'"
                              class="cover__status"
                              :class="{
                            'cover__status--success': item.bookingStatus === 'OK',
                            'cover__status--warning': item.bookingStatus === 'PROGRESS',
                            'cover__status--error': item.bookingStatus === 'CANCELLED' || item.bookingStatus === 'TRASH',
                        }">
                            {{ __(`reservations.${item.bookingStatus.toLowerCase()}`) }}
                        </span>
                        <div>
                            <ul class="list-unstyled d-flex align-items-center g-12">
                                <li v-for="n in item.tags" class="cover__servicehired"><i :class="n"></i></li>
                            </ul>

                            <h2 class="mb-0 font-16 font-md-20">
                                {{ item.productType === 'hotel' || item.productType === 'package' || item.productType === 'transfer' || item.productType === 'tour' ? item.name : item.destination }}
                            </h2>
                            <p class="mb-0 font-14 font-md-16"> {{ item.checkoutStartDate | date('D MMM') }} - {{ item.checkoutEndDate | date('D MMM') }} • {{item.destination}}</p>
                        </div>
                    </div>
                    <div class="ReservationsCard__footer">
                        <div>
                            <p class="mb-12 text-right">
                                <span class="d-block font-12 font-subtle">{{ __('reservations.locator') }}</span>
                                {{ item.bookingReference }}
                            </p>
                            <p class="mb-12 text-right font-14"
                               v-if="item.bookingStatus === 'PROGRESS' && !item.extraInfo.hotelCollect">
                                {{ __('reservations.total') }}
                                <span>
                                    <currency-display :amount="item.totalAmount" :showCurrencyCode="true" :currencyCode="item.currency" :applyDecimals="false" :reduceIsoFont="true" :applyConvertion="false" class="d-block font-16 font-600"></currency-display>
                                </span>
                            </p>
                        </div>
                        <div class="d-flex align-items-center justify-content-end g-32 flex-md-column-reverse align-items-md-end g-md-16">
                            <a :href="goItinerary(item)"
                               target="_blank"
                               rel="noreferrer noopenner"
                               class="btnTertiary btnTertiary--xs"
                               :class="{ 'flex-grow-1': item.bookingStatus === 'PROGRESS' &&  !item.extraInfo.hotelCollect }">
                                {{ __('reservations.see_itinerary') }}
                            </a>
                            <button type="button"
                                    class="btnPrimary btnPrimary--xs flex-grow-1"
                                    v-if="(item.bookingStatus === 'PROGRESS' &&  !item.extraInfo.hotelCollect) || item.bookingStatus === 'QUOTE'"
                                    @click.stop.prevent="payOnlineReservation(item)">
                                {{ __('reservations.pay_reservation') }}
                            </button>
                        </div>
                    </div>
                </section>
            </div>

            <div class="mt-4 d-center">
                <paginator v-if="Object.keys(getReservations).length > 0 && getTotalReservations > 10" />
            </div>


            <div class="notification notification--error notification--floating" v-if="notUrlPayment">
                <p class="mb-0">
                    {{ __('reservations.cant_find_url_payment') }}
                </p>

                <svg class="timer" width="24" height="24" ng-if="vm.showTimer">
                    <circle class="circle" cx="12" cy="12" r="8" />
                </svg>
            </div>

            <!--MODAL SINCRONIZACIÓN RESERVAS-->
            <div id="modal-sync-reservations" class="modal fade" tabindex="-1">
                <div class="modal-dialog modal-dialog-centered" style="max-width: 460px;">
                    <div class="modal-content px-3 py-4 p-md-32 rounded-8">
                        <h2 class="mb-24 font-20">
                            {{ __('reservations.new_reservation') }}
                            <button type="button" class="close" accesskey="" data-bs-dismiss="modal" data-dismiss="modal" aria-label="Close"> <i class="icons-close font-24"></i> </button>
                        </h2>
                        <p>{{ __('reservations.instructions') }}</p>
                        <div class="alert alert-warning alert-dismissible fade show" role="alert" v-if="notfound">
                            {{ __('reservations.not_found') }}
                        </div>
                        <ValidationObserver ref="userReservation"
                                            v-slot="{ invalid }"
                                            tag="form"
                                            @submit.prevent="onSubmit('userReservation')">
                            <div class="field__edit">
                                <ValidationProvider rules="required|numeric" v-slot="{ errors }" ref="localizator">
                                    <label for="localizator" class="font-14" :class="{ 'font-red': errors[0] }">{{ __('reservations.locator') }}</label>
                                    <input type="text" id="localizator" name="localizator"
                                           :class="{ 'is-invalid': errors && errors.length, 'invalid': errors && errors.length }" v-model="reservation.localizator"
                                           @input="reservation.localizator = reservation.localizator.replace(/\s/g, '')"
                                           :placeholder="__('reservations.localizator')">
                                    <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                                </ValidationProvider>
                                <ValidationProvider rules="required|email" v-slot="{ errors }" ref="email">
                                    <label for="email" class="font-14 mt-4" :class="{ 'font-red': errors[0] }">{{ __('reservations.email') }}</label>
                                    <input type="text" id="email" name="email"
                                           :class="{ 'is-invalid': errors && errors.length, 'invalid': errors && errors.length }" v-model="reservation.email"
                                           @input="reservation.email = reservation.email.replace(/\s/g, '')"
                                           :placeholder="__('reservations.email')">
                                    <span v-if="errors[0]" class="invalid-feedback">{{ errors[0] }}</span>
                                </ValidationProvider>
                            </div>
                            <div class="d-flex justify-content-center gap-2 mt-4">
                                <button type="button" class="mt-24 ms-4 btnTertiary btnTertiary--md w-50" @click="closeModal()">{{ __('reservations.cancel') }}</button>
                                <button type="submit" class="mt-24 btnPrimary btnPrimary--md w-50">{{ __('reservations.search') }}</button>
                            </div>
                            <!--<hr />
                    <a :href="`${config.siteUrl}/${culture}/help-center/search-itinerary`" target="_blank" class="link mt-4 mx-auto text-center w-50 d-block link--md">{{ __('reservations.cant_find_reservation') }}</a>-->
                        </ValidationObserver>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
    import { ValidationObserver, ValidationProvider } from 'vee-validate';
    import { storeToRefs } from 'pinia';
    import { useUserStore } from '../stores/user';
    import GlobalUtil from '../../utils/pricetravel/shared/globalUtil';
    import { accountAnalytics } from '../analytics/AccountAnalytics';
    const fn = window.__pt.fn;
    const site = window.__pt.settings.site;
    const _paramQuery = fn.search();
    const userLocation = window.__pt.userLocation;
    const _globalUtil = new GlobalUtil(_paramQuery, site);

    export default {

        data() {
            return {
                config: window.__pt.settings.site,
                culture: __pt.cultureData.cultureCode,
                categoryWithData: null,
                reservation: {
                    localizator: null,
                    email: null,
                },
                notfound: false,
                notUrlPayment: false
            }
        },
        setup() {
            const useUser = useUserStore();
            const { setReservation, getReservationService, setLoading, setCurrentPage, setCategoryName, setCache, getAllReservations } = useUser;
            const { getReservations, getCurrentPage, getLoading, getCategory, getTotalReservations, getUpcomingData, getPendingData, getCompletedData, getCancelledData, getCategoryWithData, getQuotesData, getTotalAllCategoriesReservations } = storeToRefs(useUser);

            return { setReservation, getReservations, getCurrentPage, getReservationService, setLoading, getLoading, setCurrentPage, setCategoryName, getCategory, setCache, getTotalReservations, getAllReservations, getUpcomingData, getPendingData, getCompletedData, getCancelledData, getCategoryWithData, getQuotesData, getTotalAllCategoriesReservations }
        },
        async beforeMount() {
            await this.getAllReservations();
            accountAnalytics.calculateTotalReservations(this.getTotalAllCategoriesReservations);
        },
        mounted() {
            accountAnalytics.viewUserReservations();
        },
        props: {
            user: null,
            site: null
        },
        components: {
            ValidationObserver,
            ValidationProvider
        },
        methods: {
            setCategory(category) {
                accountAnalytics.handleCategorySelection(category);
                this.setCategoryName(category);
                this.setCurrentPage(1);
            },
            async payOnlineReservation(reservation) {
                this.setLoading(true);
                try {
                    const headers = {
                        headers: {
                            "X-CSRF-TOKEN": _globalUtil.getCSFRToken()
                        }
                    };
                    const params = {
                        id: reservation.bookingReference,
                        email: reservation.email
                    };
                    const response = await axios.post(site.siteUrl + "/hotel/api/payment-online", params, headers);

                    if (
                        response &&
                        response.status === 200 &&
                        response.data.status === "ok" &&
                        response.data.urlRedirect !== ""
                    ) {
                        accountAnalytics.handlePayReservationClick(reservation.bookingStatus, reservation.productType, reservation.bookingReference);
                        window.location.href = response.data.urlRedirect;
                        return;
                    } else {
                        this.notUrlPayment = true;
                        const timer = setInterval(() => {
                            this.notUrlPayment = false;
                        }, 4000);
                    }
                } catch (error) {
                    console.log(error);
                }

                this.setLoading(false);
            },
            async syncReservation() {
                this.setLoading(true);
                let headers = {
                    headers: {
                        "X-CSRF-TOKEN": _globalUtil.getCSFRToken()
                    }
                }
                let params = {
                    "id": this.reservation.localizator,
                    "email": this.reservation.email,
                    "query": ""
                }

                let response = await axios.post(site.siteUrl + "/hotel/api/user/sync-booking", params, headers)
                    .catch((data) => console.log('Error syncing booking:', data));

                if (response && response.data.status) {
                    this.resetForm();
                    this.closeModal();
                    await this.getAllReservations(true, response.data.reservationId);
                } else {
                    this.setLoading(false);
                    this.notfound = true;
                    accountAnalytics.trackBookingNotFoundOnImport();
                }
            },
            async onSubmit(refName) {
                if (this.$refs[refName]) {
                    const isValid = await this.$refs[refName].validate();
                    if (isValid) {
                        await this.syncReservation();
                    } else {
                        console.log("Errores en el formulario:", refName);
                    }
                } else {
                    console.error(`ValidationObserver ${refName} no está disponible`);
                }
            },
            openModalSync() {
                $("#modal-sync-reservations").modal("show");
            },
            closeModal() {
                $("#modal-sync-reservations").modal("hide");
            },
            goItinerary(item) {
                if (this.config.sufix === 'TB') {
                    return `https://viajes.tiquetesbaratos.com/ayuda/tiquetes-baratos/iniciar-sesion?masterLocator=${item.bookingReference}&email=${item.email}`
                } else {
                    return `https://www.pricetravel.com/${this.culture}/help-center/itinerary?MasterLocatorId=${item.bookingReference}&Email=${item.email}`
                }
            },
            resetForm() {
                this.reservation.localizator = null;
                this.reservation.email = null;
            }
        }
    }
</script>
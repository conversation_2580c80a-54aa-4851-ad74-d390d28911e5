﻿<template>
    <div id="legal-page" class="container py-32">
        <!-- Breadcrumb  --->
        <breadcrumb />

        <h1 class="mb-24 mt-32">{{ __("legal.legal_info") }}</h1>

        <!-- Tabs  --->
        <div class="d-flex g-base border-bottom">
            <button 
                class="tab" 
                role="tab"
                v-for="(item, index) in languages"
                :key="item.code"
                @click="activeTab = item.jurisdiction"
                :class="{ active: activeTab === item.jurisdiction }">
                {{ item.name }}
            </button>
        </div>
        <ul class="legalgrid">
            <li>
                <a :href="getTermsUrl" class="legalcard">
                    <i class="icons-list-check"></i>
                    <h2>{{ __("legal.terms_and_conditions") }}</h2>
                    <p>{{ __('legal.terms_conditions_description', [ activeTabName ]) }}</p>
                </a>
            </li>
            <li>
                <a :href="getPrivacyUrl" class="legalcard">
                    <i class="icons-user-shield"></i>
                    <h2>{{ __("legal.privacy_policy") }}</h2>
                    <p>{{ __('legal.privacy_policy_description') }}</p>
                </a>
            </li>
            <li v-if="activeTab === 'mx'">
                <a :href="getProfecoUrl" class="legalcard">
                    <i class="icons-memo-circle-check"></i>
                    <h2>{{ __("legal.profeco_contract_mx") }}</h2>
                    <p>{{ __('legal.profeco_contract_description') }}</p>
                </a>
            </li>
        </ul>
    </div>
</template>

<script>
    import { storeToRefs } from 'pinia';
    import { useLegalStore } from '../../stores/legal';
    

    export default {
        data() {
            const locationData = window.__pt.userLocation || window.__pt.settings.site;
            const rawCountry = locationData?.country?.toLowerCase() || '';
            const mappedCountry = rawCountry === 'us' ? 'global' : rawCountry;
            
            return {
                activeTab: mappedCountry || 'global',
                languages: [
                    {
                        name: this.__("legal.mx"),
                        code: 'Price Res, SAPI de CV',
                        jurisdiction: "mx"
                    },
                    {
                        name: this.__("legal.global"),
                        code: 'BTC',
                        jurisdiction: "global"
                    },
                ],
                siteConfig: window.__pt.settings.site,
                culture: window.__pt.cultureData
            }
        },
        computed: {
            getTermsUrl() {
                return `${this.siteConfig.siteUrl}/${this.culture.cultureCode}/legal/terms-and-conditions/${this.activeTab}`;
            },
            getPrivacyUrl() {
                return `${this.siteConfig.siteUrl}/${this.culture.cultureCode}/legal/privacy-policy/${this.activeTab}`;
            },
            getProfecoUrl() {
                return `${this.siteConfig.siteUrl}/${this.culture.cultureCode}/legal/profeco-contract`;
            },
            activeTabName() {
                const activeLanguage = this.languages.find(
                    (item) => item.jurisdiction === this.activeTab
                );
                return activeLanguage ? activeLanguage.name : '';
            }
        },
        setup() {
            const useLegal = useLegalStore();
            const { getLanguageSelected } = storeToRefs(useLegal);
            
            return { getLanguageSelected }
        },
        mounted() {
        },
    }
</script>
﻿@using PricetravelWebSSR.Helpers
@using Microsoft.Extensions.Options
@using PricetravelWebSSR.Options;
@using PricetravelWebSSR.Models.Collection;
@using PricetravelWebSSR.Types;
@inject IOptions<SettingsOptions> settingOptions
@inject ViewHelper viewHelper
@inject StaticHelper staticHelper

@{
    var resolution = viewHelper.GetImageResolution();
    var tabs = ViewData["CollectionTabsLanding"] as TabsAnswers;
    var isMobile = resolution.Device == DeviceType.Mobile;
    var isHome = (bool)(ViewData["Home"] ?? false);
    var isHomeAbout = (bool)(ViewData["HomeAbout"] ?? false); 
    string classSelect = isHome ? "col-xl-3 col-lg-4 col-md-6 col-sm-12" : "col-md-4 col-sm-6 col-lg-6 col-xl-4";
    classSelect = isHomeAbout ? "col-12" : classSelect;
    var page = (string)ViewData["PageShow"];
    var pageOrigin =  (string)ViewData["PageOrig"];
    var idCollapse = "landingCollapse" + page;
    pageOrigin = pageOrigin != "" ? "." + pageOrigin : isHome ? ".home" : ".homeabout";
    var ngIfClick = isMobile ?  "ng-click=vm.collapseHome" + pageOrigin + "=!vm.collapseHome" + pageOrigin : "";
    var ngIfDefine1 = "ng-show=vm.collapseHome" + pageOrigin;
    var ngIfDefine2 = "ng-show=!vm.collapseHome" + pageOrigin;
    var classCollapseMobile = isMobile ? "row collapse mt-0" : "row";
    var toggle = isMobile ? "data-toggle=collapse href=#"+ @idCollapse + " role=button aria-expanded=false aria-controls="+ @idCollapse +"" : "";
    var isMobileLink = isMobile ? "mb-4 d-flex justify-content-between hover-link-about" : "mb-4 d-flex justify-content-between";
}
@if (tabs.Answers.Count > 0)
{
    <div class="my-2">
        <div  class="@isMobileLink" @ngIfClick  @toggle>
            <h3 class="h3">@tabs.Title</h3>
            @if(isMobile){
                 <i @ngIfDefine2  style="font-size: 18px;align-self: center;" class="icons-chevron-down ml-2" ></i>
                 <i @ngIfDefine1 style="font-size: 18px;align-self: center;" class="icons-chevron-up ml-2" ></i>
            }

        </div>
        <div class="@classCollapseMobile" id="@idCollapse">
            @foreach (var asnwer in tabs.Answers)
            {
                <div class="@classSelect mb-4">
                    <div class="card" style="margin: 10px;height: 100%">
                        <div class="card-body">
                            @if(!isHomeAbout){<label class="title-answers">@asnwer.Name</label>}
                            <div class="text-muted font-size-14">
                                @Html.Raw(@asnwer.Description)
                            </div>
                        </div>
                    </div>
                </div>
            }
        </div>
    </div>
}



﻿@using PricetravelWebSSR.Helpers
@inject ViewHelper viewHelper

@{
    var page = ViewData["page"];
}

<div class="modal fade" id="modal-payform" tabindex="-1" aria-labelledby="modal-payment" aria-hidden="true" role="dialog">
    <div class="modal-dialog modal-lg modal-dialog-scrollable">
        <div class="modal-content" role="document">
            <div class="modal-header px-20">
                <h2 class="mb-0 font-20 font-main">@viewHelper.Localizer("modaltb_payment_title")</h2>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <i class="icons-close font-24 font-subtle" aria-hidden="true"></i>
                </button>
            </div>

            <div class="modal-body px-20">
                <section class="d-flex flex-column g-12 flex-sm-row">
                    <i class="icons-credit-card-outline font-32 d-flex"></i>
                    <div class="flex-grow-1">
                        <h3 class="mb-8 font-18">@viewHelper.Localizer("modaltb_payment_card_title")</h3>
                        <div class="border rounded p-3 d-flex justify-content-between align-items-center g-8 flex-wrap">
                            <p class="mb-0 font-600">@viewHelper.Localizer("modaltb_payment_until") <span class="font-green">{{ vm.responsePayment.maxmsi }}*</span></p>
                            <div class="d-inline-flex align-items-center g-8 flex-wrap">
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-visa.svg" alt="visa card" width="70" height="32" />
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-amex.svg" alt="american express" width="48" height="48" />
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-mastercard.svg" alt="mastercard" width="60" height="40" />
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-maestro.svg" alt="maestro" width="60" height="40" />
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-dinersclub.svg" alt="dinersclub" width="120" height="32" />
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-electron.svg" alt="visa electron" width="60" height="40" />
                            </div>
                        </div>
                        <p class="mt-8 mb-0 font-12 font-subtle">*@viewHelper.Localizer("modaltb_payment_warning")</p>
                    </div>
                </section>

                <hr class="my-20">

                <section class="d-flex flex-column g-12 flex-sm-row">
                    <i class="icons-wallet font-32 d-flex"></i>
                    <div class="flex-grow-1">
                        <h3 class="mb-0 font-18">@viewHelper.Localizer("modaltb_payment_no_card_title")</h3>
                        <p class="mb-8">@viewHelper.Localizer("modaltb_payment_other_methods")</p>
                        <div class="border rounded">
                            <div class="p-3 d-flex flex-column g-8 border-bottom flex-lg-row justify-content-lg-between align-items-lg-center">
                                <div>
                                    <p class="mb-0 font-600">Botón Bancolombia</p>
                                    <p class="m-0">@viewHelper.Localizer("modaltb_payment_bancolombia")</p>
                                </div>
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-bancolombia.png" alt="bancolombia" width="150" height="50" />
                            </div>
                            <div class="p-3 d-flex flex-column g-8 border-bottom flex-lg-row justify-content-lg-between align-items-lg-center">
                                <div>
                                    <p class="mb-0 font-600">@viewHelper.Localizer("modaltb_payment_pse_title")</p>
                                    <p class="m-0">@viewHelper.Localizer("modaltb_payment_pse_description")</p>
                                </div>
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-pse.svg" alt="PSE" width="60" height="60" />
                            </div>
                            <div class="p-3 d-flex flex-column g-8 border-bottom flex-lg-row justify-content-lg-between align-items-lg-center">
                                <p class="mb-0 font-600">DaviPlata</p>
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-daviplata.svg" alt="daviplata" width="90" height="60" />
                            </div>
                            <div class="p-3 d-flex flex-column g-8 flex-lg-row justify-content-lg-between align-items-lg-center">
                                <p class="mb-0 font-600">Nequi</p>
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-nequi.svg" alt="nequi" width="90" height="60" />
                            </div>
                        </div>
                    </div>
                </section>

                <hr class="my-20">

                <section class="d-flex flex-column g-12 flex-sm-row">
                    <i class="icons-deposit font-32 d-flex"></i>
                    <div>
                        <h3 class="font-18">@viewHelper.Localizer("modaltb_payment_cash_title")</h3>
                        <p>@viewHelper.Localizer("modaltb_payment_cash_description")</p>
                        <div class="border rounded">
                            <div class="p-3 d-flex flex-column g-8 border-bottom flex-lg-row justify-content-lg-between align-items-lg-center">
                                <div>
                                    <p class="mb-0 font-600">Efecty</p>
                                    <p class="m-0">@viewHelper.Localizer("modaltb_payment_efecty")</p>
                                </div>
                                <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-efecty.svg" alt="efecty" width="60" height="60" />
                            </div>
                            <div class="p-3 d-flex justify-content-between align-items-center g-8 flex-wrap">
                                <p class="mb-0 font-600">@viewHelper.Localizer("modaltb_payment_bank_deposit")</p>
                                <div class="d-flex align-items-center g-8 flex-wrap">
                                    <img loading="lazy" src="https://3.cdnpt.com/images/bancos/logo-bancodebogota.svg" alt="banco de bogota" width="120" height="60" />
                                    <img loading="lazy" src="https://3.cdnpt.com/images/bancos/bancolombia.png" alt="banco de colombia" width="110" height="60" />
                                    <img loading="lazy" src="https://3.cdnpt.com/images/bancos/davivienda.png" alt="daviplata" width="110" height="60" />
                                </div>
                            </div>
                        </div>
                    </div>
                </section>
            </div>
        </div>
    </div>
</div>

﻿using ProtoBuf;

namespace PricetravelWebSSR.Models.ContentDeliveryNetwork.DestinationContent
{
    [ProtoContract]
    public class FoodInfo
    {
        [ProtoMember(1)]
        public int Id { get; set; }

        [ProtoMember(2)]
        public bool IsActive { get; set; }

        [ProtoMember(3)]
        public string Summary { get; set; }

        [ProtoMember(4)]
        public string Info { get; set; }

        [ProtoMember(5)]
        public List<Dish> Dishes { get; set; }

        public FoodInfo()
        {
            Summary = string.Empty;
            Info = string.Empty;
            Dishes = new List<Dish>();
        }
    }

}
